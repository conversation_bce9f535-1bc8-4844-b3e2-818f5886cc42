<p-toast></p-toast>
<div>
    <div class="mb-1">Services</div>
    <a *ngIf="!serviceStatus" style="color: red">Pick One At Least</a>
    <p-card>
        <div class="row" *ngFor="let companySubscription of _Company.Subscriptions">
            <div class="col">
                <p-checkbox [binary]="true" inputId="binary" [(ngModel)]="companySubscription.Checked"
                    (click)="checkSubscription(companySubscription)" [disabled]="user.UserType != 0"
                    id="cb_dicount"></p-checkbox>
                <i class="pi pi-euro ms-3" style="color: slateblue"></i>
                {{companySubscription. EnName }}
            </div>
            <div class="col" *ngIf="_Company.ActivityScope == 0">
                <label for="adminEmail">{{ _Company.Currency }}/Y</label>
                <p-inputNumber [min]="0" [useGrouping]="false" inputId="discountsValue" class="p-inputtext-sm w-50 ms-2"
                    (ngModelChange)="checkSubscription(companySubscription)"
                    [(ngModel)]="companySubscription.FeePerYear" [disabled]="true" />
            </div>
            <div class="col" *ngIf="_Company.ActivityScope == 1">
                <label for="adminEmail">{{ _Company.Currency }}/Y</label>
                <p-inputNumber [min]="0" [useGrouping]="false" inputId="discountsValue" class="p-inputtext-sm w-50 ms-2"
                    (ngModelChange)="checkSubscription(companySubscription)"
                    [(ngModel)]="companySubscription.FeePerYear" [disabled]="user.UserType != 0 " />
            </div>
        </div>
        <div class="row">
            <div class="col">
                <p-checkbox [binary]="true" inputId="binary" [(ngModel)]="_Company.Reviewed"
                    [disabled]="user.UserType != 0"></p-checkbox>
                <i class="pi pi-euro ms-3" style="color: slateblue"></i>
                Discounts are reviewed by super admin
            </div>
        </div>
    </p-card>
    <br />
    <!-- Services Available Tiers -->
    <div class="mb-1">Services Available Tiers</div>
    <a *ngIf="!tireStatus" style="color: red">Pick One At Least</a>
    <p-card>
        <div class="row" *ngFor="let item of _Company.Tiers">
            <div class="col">
                <p-checkbox [binary]="true" inputId="binary" [disabled]="user.UserType != 0|| item.disabled"
                    [(ngModel)]="item.Checked" (click)="checkTier(item)"></p-checkbox>
                <i class="pi pi-euro ms-3" style="color: slateblue"></i>
                {{ item.EnName }}
            </div>
        </div>
    </p-card>
    <!-- [formGroup]="subscriptionsForm" -->
    <div class="row">
        <div class="col-2">
            <label class="block font-bold mb-2" for="locale-user">Admin Discount</label>
            <input type="number" [ngModel]="this._Company.DiscountGranted" (ngModelChange)="calcTotal($event)"
                class="form-control" required min="0" [disabled]="user.UserType != 0" />
        </div>
        <div class="col">
            <div class="row text-right pe-2">
                <label class="col-12 font-bold mb-2 me-2" for="locale-user">Total Fee</label>
            </div>
            <div class="row text-right">
                <label class="col-12 font-bold mb-2" for="locale-user">
                    {{ _Company.Total }}{{ _Company.Currency}}/year</label>
            </div>
        </div>
        <div class="flex justify-content-end gap-2 my-3">
            <p-button label="back" styleClass="p-button-outlined p-button-secondary" (click)="back()"></p-button>
            <p-button *ngIf="!enableEdit " [disable]="saveButtonDisable" label="add company"
                (click)="addCompany()"></p-button>
            <p-button *ngIf="enableEdit" [disable]="saveButtonDisable" label="edit company"
                (click)="editCompany()"></p-button>
        </div>
    </div>
</div>