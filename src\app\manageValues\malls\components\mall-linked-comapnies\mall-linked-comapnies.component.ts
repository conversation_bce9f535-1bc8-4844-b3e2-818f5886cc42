import { Component, EventEmitter, Input, Output } from '@angular/core';
import { MallModel } from 'src/app/Model/MallModel';
import { Countries } from 'src/app/utilities/countries';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'mall-linked-comapnies',
  templateUrl: './mall-linked-comapnies.component.html',
  styleUrls: ['./mall-linked-comapnies.component.scss']
})
export class MallLinkedComapniesComponent {
  @Input() _MallData: MallModel;
  @Input() isEditing: boolean;
  @Input() activeIndex: number;
  @Output() activeIndexChange: EventEmitter<number> = new EventEmitter();

  allCountries: Countries = new Countries();

  constructor() { }
  ngOnInit() { }

  getBranchNames(companyBranches): string {
    return companyBranches.map(branch => branch.EnName).join(', ');
  }
  companyProfileLogo(logo) {
    return logo ? environment.imageSrc + logo : '';
  }
}
