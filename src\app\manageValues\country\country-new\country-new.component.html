<!-- <p-toast></p-toast> -->
<div>

    <h3>
        <p-button icon="pi pi-arrow-left" styleClass="p-button-rounded p-button-secondary p-button-text"
            routerLink="/countries">
        </p-button>
        {{title}}
    </h3>
    <p-messages *ngIf="this.newCountry.HasError" [value]="msgs"></p-messages>
    <!-- <p-tabView [activeIndex]="1"> -->
    <p-tabView [(activeIndex)]="activeIndex" (onChange)="onTabChange($event)">
        <p-tabPanel header="Main Info" [ngStyle]="{ color: 'var(--cyan-300-color)' }">
            <p-card role="region">
                <country-main-info [(_CountryData)]="newCountry" [(activeIndex)]="activeIndex"
                    [isEditing]="isEditing"></country-main-info>
            </p-card>
        </p-tabPanel>
        <p-tabPanel header="Cities" [disabled]="tabsDisabled">
            <p-card role="region">
                <app-country-cities [(_CountryData)]="newCountry" [(activeIndex)]="activeIndex"></app-country-cities>
            </p-card>
        </p-tabPanel>
        <p-tabPanel header="Available Categories" [disabled]="tabsDisabled">
            <p-card role="region">
                <country-available-categories [(_CountryData)]="newCountry"
                    [(activeIndex)]="activeIndex"></country-available-categories>
            </p-card>
        </p-tabPanel>
        <p-tabPanel header="Available Tiers & Prices" [disabled]="tabsDisabled">
            <p-card role="region">
                <country-available-tiers [(_CountryData)]="newCountry"
                    [(activeIndex)]="activeIndex"></country-available-tiers>
            </p-card>
        </p-tabPanel>
        <p-tabPanel header="Subscriptions Prices" [disabled]="tabsDisabled">
            <p-card role="region">
                <country-subscriptions-prices [(_CountryData)]="newCountry"
                    [(activeIndex)]="activeIndex"></country-subscriptions-prices>
            </p-card>
        </p-tabPanel>
    </p-tabView>
</div>