<p-toast></p-toast>
<h4>
    <p-button icon="pi pi-arrow-left" styleClass="p-button-rounded p-button-secondary p-button-text"
        (click)="goBack()"></p-button>
    Discount Id: {{discount.UserFriendly}}
</h4>
<p-card>
    <div class="flex col-sm mb-2">
        <div class="col-sm">
            <h3 style="text-align: start">Company</h3>
            <div class="d-flex justify-content-start">
                <p-avatar image="{{getImageUrl( discount!.Company!.LogoUrl)}}" styleClass="mr-2" size="large"
                    shape="circle"></p-avatar>
                <span>{{ discount!.Company!.EnName }}</span>
            </div>
        </div>
        <div class="col-sm">
            <h3 style="text-align: start">English Title</h3>
            <div class="d-flex justify-content-start">
                <p-avatar image="{{getImageUrl( discount.LogoUrl)}}" styleClass="mr-2" size="large"
                    shape="circle"></p-avatar>
                <span>{{ discount.EnTitle }}</span>
            </div>
        </div>
        <div class="col-sm">
            <h3 style="text-align: start"> Arabic Title</h3>
            <div class="d-flex justify-content-start">
                <p-avatar image="{{getImageUrl( discount.LogoUrl)}}" styleClass="mr-2" size="large"
                    shape="circle"></p-avatar>
                <span>{{ discount.ArTitle }}</span>
            </div>
        </div>
    </div>
    <div>
        <br>
        <br>
        <h4 style="text-align: start">Type</h4>
        <p-chip [label]=" discount.DiscountType | discountType"></p-chip>
    </div>
    <p-divider></p-divider>
    <div class="row mb-2">
        <div class="col-sm">
            <h4 style="text-align: start">Value</h4>
            <div class="d-flex justify-content-start">
                <p-chip [label]="  discount.DiscountValue | discountValue "></p-chip>
            </div>
        </div>
        <div class="col-sm">
            <h4 style="text-align: start">Estimated Saving</h4>
            <div class="d-flex justify-content-start">
                {{ discount.EstimatedSaving }}
            </div>
        </div>
        <div class="col-sm">
            <h4 style="text-align: start">Original Price</h4>
            <div class="d-flex justify-content-start">
                {{ discount.OriginalPrice }}
            </div>
        </div>
        <div class="col-sm">
            <h4 style="text-align: start">Protection Type</h4>
            <div class="d-flex justify-content-start">
                {{ discount.ProtectionType | ProtectionType }}
            </div>
        </div>
    </div>
    <p-divider></p-divider>
    <div>
        <h4 style="text-align: start">Conditions</h4>
        <div class="d-flex justify-content-start">
            <div *ngFor="let item of discount.Conditions" class="mx-2">
                <i *ngIf="item.EnName === 'Dine-in'" class="fa-solid fa-utensils"> </i>
                <i *ngIf="item.EnName === '2 people'" class="fas fa-user-friends"> </i>

                <span> {{ item.EnName }}</span>
            </div>
        </div>
    </div>
    <p-divider></p-divider>
    <div>
        <h4 style="text-align: start">English Terms</h4>
        <div class="d-flex justify-content-start">
            <span> {{ discount.EnTerms }}</span>
        </div>
    </div>
    <br />
    <div>
        <h4 style="text-align: start">Arabic Terms</h4>
        <div class="d-flex justify-content-start">
            <span> {{ discount.ArTerms }}</span>
        </div>
    </div>
    <p-divider></p-divider>
    <div>
        <h4 style="text-align: start">Filter</h4>
        <div class="d-flex justify-content-start">
            <div *ngFor="let item of discount.Filters" class="mx-2">
                <p-tag styleClass="mr-2" severity="info" value="{{ item.EnName }}" [rounded]="true"></p-tag>
            </div>
        </div>
    </div>
    <p-divider></p-divider>
    <div>
        <h4 style="text-align: start">Related branches</h4>
        <div class="d-flex justify-content-start">
            <div *ngFor="let item of discount.Branches" class="mx-2">
                <p-tag styleClass="mr-2" severity="info" value="{{ item.EnName }}" [rounded]="true"></p-tag>
            </div>
        </div>
    </div>
</p-card>
<br />
<p-card>
    <div class="row mb-2">
        <div class="col-sm">
            <h4 style="text-align: start">Period</h4>
            <div class="d-flex justify-content-start">
                <span>{{ discount.DiscountPeriod | discountPeriod }}</span>
            </div>
        </div>
        <div class="col-sm">
            <h4 style="text-align: start">Start Date</h4>
            <div class="d-flex justify-content-start">
                {{ discount.StartDate.toString() | date : "dd/MM/yyyy" }}
            </div>
        </div>
        <div class="col-sm">
            <h4 style="text-align: start">End Date</h4>
            <div class="d-flex justify-content-start">
                {{ discount.EndDate.toString() | date : "dd/MM/yyyy" }}
            </div>
        </div>
    </div>
    <p-divider></p-divider>
    <div class="row mb-2">
        <div class="col-sm">
            <h4 style="text-align: start">Day of week</h4>
            <div class="d-flex justify-content-start">
                <p-selectButton [options]="weekDays" [(ngModel)]="discount.DayOfWeeks" [multiple]="true"
                    optionLabel="name" optionValue="value" [disabled]="true"></p-selectButton>
            </div>
        </div>
        <div class="col-sm">
            <h4 style="text-align: start">Offer start time</h4>
            <div class="d-flex justify-content-start">
                <span> {{ discount.OfferStartTime| date: 'h:mm a' }}</span>
            </div>
        </div>
        <div class="col-sm">
            <h4 style="text-align: start">Offer end time</h4>
            <div class="d-flex justify-content-start">
                <span> {{ discount.OfferEndTime | date: 'h:mm a'}}</span>
            </div>
        </div>
    </div>
</p-card>
<br />
<p-card>
    <div *ngIf="discount.DiscountType == 0">
        <div *ngIf="discount.IsNormalTier==true">
            <div class="col-sm ">
                <h4 style="text-align: start">Tires</h4>
                <br>
                <div style="display: grid">
                    <div class="justify-content-start">
                        <div *ngFor="let item of discount.NormalTiersValues" class="mx-2">
                            <span class="p-badge p-badge-lg" [style.backgroundColor]="item.Tier.Color">{{
                                item.Tier.EnName
                                }}</span>
                            &nbsp;&nbsp;&nbsp;
                            <span *ngIf="item.UnlimitedOffers==false">{{item.OffersNum }} Offers</span>
                            <span *ngIf="item.UnlimitedOffers==true">&#8734; Offers</span> &nbsp; &nbsp;
                            <img *ngIf="item.RenewabilityType ==1" src="assets/pages/icons/renewable-energy.png"
                                class="small-icon" alt="Monthly Renewable" title="Monthly Renewable">
                            <br>
                            <br>
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <div *ngIf="discount.IsNormalTier==false">
            <div class="col-sm">
                <h4 style="text-align: start">Tires</h4>
                <div class="d-flex">
                    <div *ngFor="let item of discount.
                    VipTiersValues" class="m-2">
                        <p-badge *ngIf="item.Tier?.EnName=='VIP Tier'" severity="danger" size="large" value="VIP Tier"
                            [rounded]="true"></p-badge>
                        &nbsp;&nbsp;&nbsp;
                        <span *ngIf="item.UnlimitedOffers==false">{{item.OffersNum }} Offers</span>
                        <span *ngIf="item.UnlimitedOffers==true">&#8734; Offers</span>
                        <br>
                        <br>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div *ngIf="discount.DiscountType == 1">
        <div class="row mb-2">
            <div class="col-sm">
                <h4 style="text-align: start">Min People</h4>
                <div class="d-flex justify-content-start">
                    <span>{{ discount.groupInfo?.MinPeople }}</span>
                </div>
            </div>
            <div class="col-sm">
                <h4 style="text-align: start">Max People</h4>
                <div class="d-flex justify-content-start">
                    {{ discount.groupInfo?.MaxPeople }}
                </div>
            </div>
        </div>
        <p-divider></p-divider>
        <div class="row mb-2">
            <div class="col-sm">
                <h4 style="text-align: start">#Offer Per User</h4>
                <div class="d-flex justify-content-start">
                    <span *ngIf="discount.groupInfo?.UnlimitedOffers==false">{{ discount.groupInfo?.OfferPerUser
                        }}</span>
                    <span *ngIf="discount.groupInfo?.UnlimitedOffers==true">&#8734;</span>
                </div>
            </div>
            <div class="col-sm">
                <h4 style="text-align: start">Renewability </h4>
                <div class="d-flex justify-content-start">
                    {{ discount.groupInfo?.RenewabilityType | renewbilityType }}
                </div>
            </div>
        </div>
    </div>
</p-card>

<p-card>
    <div class="d-flex justify-content-end">
        <!-- <p-button *ngIf="!checkIfRejected" label="Reject" styleClass="p-button-danger mx-2"(click)="Reject()"></p-button> -->
        <div *ngIf="!checkIfRejected">
            <p-button label="Reject" styleClass="p-button-danger mx-2" (click)="showRejectReasonDialog()"></p-button>
            <p-dialog header="Rejecting reason" [(visible)]="rejectDialogVisibility" [modal]="true"
                [style]="{ width: '50vw' }" [resizable]="false">
                <p-card>
                    <div class="d-flex justify-content-start flex-column">
                        <textarea class="rejecting-reason" [(ngModel)]="discount.NoteDiscount"
                            style="width: 100%; border: none; outline: none"
                            placeholder="Please enter a reason for rejecting this discount..."
                            [formControl]="rejectReasonControl">
                        </textarea>
                        <span *ngIf="rejectReasonControl.invalid && rejectReasonControl.touched"
                            class="text-danger">Please enter a reason to reject this discount.</span>
                    </div>
                </p-card>
                <ng-template pTemplate="footer">
                    <p-button (click)="rejectDialogVisibility = false" label="Cancel"
                        styleClass="p-button-text"></p-button>
                    <p-button (click)="Reject()" label="Reject" styleClass="p-button-text"
                        styleClass="p-button-danger mx-2"></p-button>
                </ng-template>
            </p-dialog>
        </div>
        <p-button *ngIf="!checkIfApproved" label="Accept" (click)="Accept()"
            styleClass="p-button-success mx-2"></p-button>
    </div>
</p-card>