<p-toast></p-toast>
<h4>
      <p-button icon="pi pi-arrow-left" styleClass="p-button-rounded p-button-secondary p-button-text"
            (click)="goBack()"></p-button>
      Broadcast Message Id: {{BroadcastMessage.UserFriendly}}
</h4>
<p-card>
      <div class="flex col-sm mb-2">
            <div class="col-sm">
                  <h3 style="text-align: start">Company</h3>
                  <div class="d-flex justify-content-start">
                        <p-avatar image="{{getImageUrl( BroadcastMessage!.Company!.LogoUrl)}}" styleClass="mr-2"
                              size="large" shape="circle"></p-avatar>
                        <span>{{ BroadcastMessage!.Company!.EnName }}</span>
                  </div>
            </div>
            <div class="col-sm">
                  <h3 style="text-align: start">English Title</h3>
                  <div class="d-flex justify-content-start">
                        <span>{{ BroadcastMessage.EnTitle }}</span>
                  </div>
            </div>
            <div class="col-sm">
                  <h3 style="text-align: start">Arabic Title</h3>
                  <div class="d-flex justify-content-start">

                        <span>{{ BroadcastMessage.ArTitle }}</span>
                  </div>
            </div>
      </div>

      <p-divider></p-divider>
      <div>
            <h4 style="text-align: start">English Content</h4>
            <div class="d-flex justify-content-start">
                  <span> {{ BroadcastMessage.EnContent }}</span>
            </div>
      </div>
      <br />
      <div>
            <h4 style="text-align: start">Arabic Content</h4>
            <div class="d-flex justify-content-start">
                  <span> {{ BroadcastMessage.ArContent }}</span>
            </div>
      </div>
      <p-divider></p-divider>
      <div class="row mb-2">
            <div class="col-sm">
                  <h4 style="text-align: start">Message Type</h4>
                  <div class="d-flex justify-content-start">
                        <p-chip [label]=" BroadcastMessage.Type | BroadcastMessageType"></p-chip>
                  </div>
            </div>
            <div class="col-sm">
                  <h4 style="text-align: start">Phone Number</h4>
                  <div class="d-flex justify-content-start" *ngIf="BroadcastMessage.PhoneNumber?.CountryCode">
                        <img src="assets/demo/flags/flag_placeholder.png"
                              [class]="'flag flag-' +  this.allCountries.getCodeFromMobileCode(BroadcastMessage.PhoneNumber?.CountryCode)?.toLowerCase()"
                              alt="{{ BroadcastMessage.PhoneNumber?.CountryCode }}" />
                        {{ BroadcastMessage.PhoneNumber?.CountryCode}} - {{ BroadcastMessage.PhoneNumber?.Number}}
                  </div>
            </div>
            <div class="col-sm">
                  <h4 style="text-align: start">Whatsapp Number</h4>
                  <div class="d-flex justify-content-start" *ngIf="BroadcastMessage.WhatsappNumber?.CountryCode">
                        <img src="assets/demo/flags/flag_placeholder.png"
                              [class]="'flag flag-' +  this.allCountries.getCodeFromMobileCode(BroadcastMessage.WhatsappNumber?.CountryCode)?.toLowerCase()"
                              alt="{{ BroadcastMessage.WhatsappNumber?.CountryCode }}" />
                        {{ BroadcastMessage.WhatsappNumber.CountryCode}} - {{ BroadcastMessage.WhatsappNumber?.Number}}
                  </div>
            </div>
      </div>
      <br />

      <div class="row mb-2">
            <div class="col-sm">
                  <h4 style="text-align: start">Launch Date</h4>
                  <div class="d-flex justify-content-start" *ngIf="!BroadcastMessage.SendAsSoonAsPossible">
                        <span>{{ BroadcastMessage.LaunchDate.toString() | date : "dd/MM/yyyy" }}</span>
                  </div>
                  <span *ngIf="BroadcastMessage.SendAsSoonAsPossible"
                        style="background: #f69ebc; color: #801136; border-radius: var(--border-radius);  padding: 0.25em 0.5rem;  font-weight: 800; font-size: 15px;letter-spacing: 0.3px;">ASAP</span>
            </div>
            <div class="col-sm" *ngIf="!BroadcastMessage.SendAsSoonAsPossible">
                  <h4 style="text-align: start">Launch Time</h4>
                  <div class="d-flex justify-content-start" *ngIf="BroadcastMessage.LaunchStartTime">
                        {{ BroadcastMessage.LaunchStartTime | AmPmTime }} - {{ BroadcastMessage.LaunchEndTime |
                        AmPmTime}}
                  </div>
            </div>
            <div class="col-sm"></div>
      </div>
</p-card>
<br />
<p-card>
      <div class="row mb-2 ">

            <h4 style="text-align: start">Tires</h4>
            <br />
            <div>
                  <div>
                        <span *ngFor="let item of BroadcastMessage.TargetedTiers">
                              <span class="p-badge p-badge-lg" [style.backgroundColor]="item.Color">{{
                                    item.EnName
                                    }}</span>
                              &nbsp;
                        </span>
                        <br>
                        <br>
                  </div>

            </div>
      </div>


      <div class="row mb-2">
            <div class="col-sm">
                  <h4 style="text-align: start">Targeted Gender</h4>
                  <div class="d-flex justify-content-start">
                        <span>{{ BroadcastMessage.TargetedGender |Gender}}</span>
                  </div>
            </div>

            <br />

      </div>
      <br />
      <br />
      <div class="row mb-2">
            <div class="col-sm">
                  <h4 style="text-align: start">Targeted Min Age</h4>
                  <div class="d-flex justify-content-start">
                        {{ BroadcastMessage.TargetedMinAge }}
                  </div>
            </div>

            <div class="col-sm">
                  <h4 style="text-align: start">Targeted Max Age</h4>
                  <div class="d-flex justify-content-start">
                        {{ BroadcastMessage.TargetedMaxAge }}
                  </div>
            </div>
            <br />
      </div>
      <br />
      <br />
      <div class="row mb-2">

            <br />
            <div class="col-sm">
                  <h4 style="text-align: start">Targeted Country </h4>
                  <div class="d-flex justify-content-start">
                        <img src="assets/demo/flags/flag_placeholder.png"
                              [class]="'flag flag-' +  this.allCountries.getCodeFromName( BroadcastMessage.TargetedCountry?.EnName)"
                              alt="{{  BroadcastMessage.TargetedCountry?.EnName }}" />
                        &nbsp;
                        {{ BroadcastMessage.TargetedCountry?.EnName }}
                  </div>
            </div>
            <div class="col-sm">
                  <h4 style="text-align: start">Targeted Cities </h4>
                  <div class="d-flex justify-content-start">
                        <div *ngFor="let item of BroadcastMessage.TargetedCities" class="mx-2">
                              <p-tag severity="info" styleClass="mr-2" value="{{ item.EnName }}"
                                    [rounded]="true"></p-tag>
                              <!-- <i class="pi pi-check-square"></i>
                              {{ item.EnName }} -->
                        </div>
                  </div>
            </div>
            <br>
      </div>
      <br>
      <div class="row mb-2">
            <br />
            <div class="col-sm">
                  <h4 style="text-align: start">Targeted Nationalities </h4>
                  <div class="d-flex justify-content-start">
                        <div *ngFor="let item of BroadcastMessage.TargetedNationalities" class="mx-2">
                              <p-tag styleClass="mr-2" severity="info" value="{{ item.EnName }}"
                                    [rounded]="true"></p-tag>
                              <!-- <i class="pi pi-check-square"></i>
                              {{ item.EnName }} -->
                        </div>
                  </div>
            </div>
            <br />
      </div>
      <p-divider></p-divider>


</p-card>


<p-card>
      <div class="d-flex justify-content-end">
            <!-- <p-button *ngIf="!checkIfRejected" label="Reject" styleClass="p-button-danger mx-2"(click)="Reject()"></p-button> -->
            <div *ngIf="!checkIfRejected">
                  <p-button label="Reject" styleClass="p-button-danger mx-2"
                        (click)="showRejectReasonDialog()"></p-button>
                  <p-dialog header="Rejecting reason" [(visible)]="rejectDialogVisibility" [modal]="true"
                        [style]="{ width: '50vw' }" [resizable]="false">
                        <p-card>
                              <div class="d-flex justify-content-start flex-column">
                                    <textarea class="rejecting-reason"
                                          [(ngModel)]="BroadcastMessage.NoteBroadcastMessage"
                                          style="width: 100%; border: none; outline: none"
                                          placeholder="Please enter a reason for rejecting this BroadcastMessage..."
                                          [formControl]="rejectReasonControl">
                        </textarea>
                                    <span *ngIf="rejectReasonControl.invalid && rejectReasonControl.touched"
                                          class="text-danger">Please enter a reason to reject this
                                          BroadcastMessage.</span>
                              </div>
                        </p-card>
                        <ng-template pTemplate="footer">
                              <p-button (click)="rejectDialogVisibility = false" label="Cancel"
                                    styleClass="p-button-text"></p-button>
                              <p-button (click)="Reject()" label="Reject" styleClass="p-button-text"
                                    styleClass="p-button-danger mx-2"></p-button>
                        </ng-template>
                  </p-dialog>
            </div>
            <p-button *ngIf="!checkIfApproved" label="Accept" (click)="Accept()"
                  styleClass="p-button-success mx-2"></p-button>
      </div>
</p-card>