import { ComponentFixture, TestBed } from '@angular/core/testing';

import { BroadcastMessageTimingTargetedUserComponentComponent } from './broadcast-message-timing-targeted-user-component.component';

describe('BroadcastMessageTimingTargetedUserComponentComponent', () => {
  let component: BroadcastMessageTimingTargetedUserComponentComponent;
  let fixture: ComponentFixture<BroadcastMessageTimingTargetedUserComponentComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ BroadcastMessageTimingTargetedUserComponentComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(BroadcastMessageTimingTargetedUserComponentComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
