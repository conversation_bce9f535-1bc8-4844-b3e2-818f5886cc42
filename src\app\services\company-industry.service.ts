import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, map } from 'rxjs';
import { environment } from 'src/environments/environment';
import { Dto } from '../Model/Dto';
import { CompanyIndustryModel } from '../Model/CompanyIndustryModel';

@Injectable({
  providedIn: 'root'
})
export class CompanyIndustryService {
  public companyIndustriesDataSource = new BehaviorSubject<Array<CompanyIndustryModel>>([]);
  public companyIndustries = this.companyIndustriesDataSource.asObservable();
  constructor(private httpClient: HttpClient) { }
  getCompanyIndustries() {
    return this.httpClient.get<Dto<CompanyIndustryModel>>(`${environment.apiUrl}` + 'Industry/GetAll')
      .pipe(
        map((res :Dto<CompanyIndustryModel>) => {
          return res.ListResultContent.map(item => ({
            Name: item.EnName,
            icon: '',
            Code: item.Id
          }));
        })
      );
  }
}
