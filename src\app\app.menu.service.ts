import { EventEmitter, Injectable } from '@angular/core';
import { Subject } from 'rxjs';

@Injectable()
export class MenuService {

    private menuSource = new Subject<string>();
    private resetSource = new Subject();

    menuSource$ = this.menuSource.asObservable();
    resetSource$ = this.resetSource.asObservable();

    onMenuStateChange(key: string) {
        this.menuSource.next(key);
    }

    reset() {
        this.resetSource.next(true);
    }

    // Event to notify menu refresh
    menuRefreshEvent = new EventEmitter<void>();
    // Trigger menu refresh
    notifyMenuRefresh() {
        this.menuRefreshEvent.next();
    }
}
