import { CityModel } from "./CityModel";
import { CompanyModel } from "./CompanyModel";
import { PhoneNumberModel } from "./PhoneNumberModel";

export class MallModel {
    Id: string="";
    UserFriendly: number = null;
    ArName: string = "";
    EnName: string = "";
    EnAbout?: string = "";
    ArAbout?: string = "";
    PhoneNumber?: string = "";
    PhoneNumbers?: PhoneNumberModel[]=[];

    City?: CityModel;
    CityId?: string = "";

    AssociatedCompaniesNum?: number = undefined;
    AssociatedCompanies :CompanyModel[]=[];

    //Image
    LogoUrl: string = "";
    Logo: File;
    Location: string = ""; // country , city ..from city property

}
