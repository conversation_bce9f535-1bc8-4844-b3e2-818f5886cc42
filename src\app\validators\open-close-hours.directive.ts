import { FormGroup, ValidationErrors, ValidatorFn } from "@angular/forms";

export const openCloseHourssValidator: ValidatorFn = (group: FormGroup): ValidationErrors | null => {
    // console.log(group);

    const open = group.controls['openTime'];
    // console.log(open);
    const close = group.get('closeTime');

    const open2 = group.controls['openTime2'];
    // console.log(open);
    const close2 = group.get('closeTime2');
    let result=null;
    if ((open.value === null && open.touched) || (close.touched && close.value === null)) {
       // return null;
    } else if ((open2.value === null && open2.touched) || (close2.touched && close2.value === null)) {
       // return null;
    } else if (open.value > close.value) {
        // return { dateValid: true };
        result={ dateValid: true };
    }
    else if (open2.value > close2.value) {
        // return { dateValid: true };
        result ={ dateValid: true };
    } else {
        // return null;
    }
    return result;
}
