<p-toast></p-toast>

<!-- <h4> Main Info: </h4> -->
<div class="row" [formGroup]="tierMainInfoForm">
      <div class="container row col-md-8">
            <div class="flex flex-column gap-3 mb-4">
                  <label for="EnName" [style.color]="CheckValid(tierMainInfoForm.controls.EnName)">English
                        Name</label>
                  <input id="EnName" type="text" pInputText placeholder="Tier English Name"
                        [(ngModel)]="_TierData.EnName" class="p-inputtext-sm w-50" formControlName="EnName" />

            </div>
            <div class="flex flex-column gap-3 mb-4">
                  <label for="ArName" [style.color]="CheckValid(tierMainInfoForm.controls.ArName)"> Arabic
                        Name</label>
                  <input id="ArName" type="text" pInputText placeholder="Tier Arabic Name"
                        [(ngModel)]="_TierData.ArName" class="p-inputtext-sm w-50" formControlName="ArName" />

            </div>
            <div class="flex flex-column gap-3 mb-4">
                  <label for="Rank" [style.color]="CheckValid(tierMainInfoForm.controls.Rank)"> Rank
                  </label>
                  <p-inputNumber [min]="0" [useGrouping]="false" inputId="Rank" class="p-inputtext-sm w-50"
                        [(ngModel)]="_TierData.Rank" formControlName="Rank" />
            </div>
            <div class="flex flex-column mb-4">
                  <label for="Color" [style.color]="CheckValid(tierMainInfoForm.controls.Color)"> Color</label>
                  <div class="col">
                        <p-colorPicker [(ngModel)]="_TierData.Color" formControlName="Color">
                        </p-colorPicker>
                        &nbsp; &nbsp;
                        <span [style]="{'border': '1px solid #ced4da', 'padding': '4px 8px', 'border-radius': '4px'}">
                              {{ _TierData.Color
                              }}</span>
                  </div>
            </div>
            <div class="flex flex-column gap-3 mb-4">
                  <div class="col">
                        <p-inputSwitch [ngModelOptions]="{ standalone: true }" [disabled]="isEditing"
                              [(ngModel)]="_TierData.IsVip"></p-inputSwitch>
                        &nbsp; &nbsp;
                        <span> VIP Tier </span>
                  </div>
            </div>
            <div class="flex flex-column gap-3 mb-4">
                  <div class="col">
                        <p-inputSwitch [ngModelOptions]="{ standalone: true }"
                        [disabled]="isEditing" [(ngModel)]="_TierData.MonthlyRenewablility">
                        </p-inputSwitch>
                        &nbsp; &nbsp;
                        <span> End User services are Monthly renewable </span>
                  </div>
            </div>
      </div>

      <br />
      <br />
      <div class="flex justify-content-end gap-2 mb-3">
            <p-button label="save" (onClick)="save()"></p-button>
            <!-- [disabled]="!this.timingDetailsForm.valid" -->
      </div>
</div>