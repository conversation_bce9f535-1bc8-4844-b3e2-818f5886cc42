import { ChangeDetectorRef, Component, ElementRef, Input, ViewChild } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { FilterModel } from 'src/app/Model/FiltersModel';
import { IndustryModel } from 'src/app/Model/IndustryModel';
import { UserModel } from 'src/app/Model/UserModel';
import { AuthService } from 'src/app/services/auth.service';
import { IndustryService } from 'src/app/services/industry.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-industry-new',
  templateUrl: './industry-new.component.html',
  styleUrls: ['./industry-new.component.scss'],

})
export class IndustryNewComponent {

  pressedSave: boolean = false;
  pressedSaveFilter: boolean = false;
  filters: FilterModel[];
  user: UserModel;
  isEditing: boolean = false;
  enableEditFilter: boolean = false;
  routeState: any;
  _industry: IndustryModel = new IndustryModel();
  selectedFilter: FilterModel = new FilterModel();
  imageUrl: any = '';
  @Input() fileToUpload: any[];
  title: string = 'Add New Industry';

  industryForm = new FormGroup({
    enName: new FormControl('', [Validators.required, Validators.minLength(3)]),
    arName: new FormControl('', [Validators.required, Validators.minLength(3)]),
    //   filters: this.fb.array([], Validators.required),
  });
  filterForm = new FormGroup({
    filterEnName: new FormControl('', [Validators.required, Validators.minLength(3)]),
    filterArName: new FormControl('', [Validators.required, Validators.minLength(3)]),
    //   filters: this.fb.array([], Validators.required),
  });
  constructor(private messageService: MessageService, private industryService: IndustryService, private fb: FormBuilder, private router: Router, private ref: ChangeDetectorRef, private route: ActivatedRoute, private sanitizer: DomSanitizer, private authService: AuthService) {

    if (this.router.getCurrentNavigation()?.extras.state) {

      this.routeState = this.router.getCurrentNavigation()?.extras.state;
      if (this.routeState) {
        this._industry = this.routeState.data
          ? this.routeState.data
          : new IndustryModel();
        if (this._industry.Id == "") {
          this.isEditing = false;
        }
        else {
          this.isEditing = true;
        }
      }
    }
    if (this.route.snapshot.url[this.route.snapshot.url.length - 1].path === 'industry-edit') {
      this.isEditing = true;
      this.title = 'Edit Industry';
    }

  }

  ngOnInit() {
    if (this.isEditing == true && this._industry.Id == "") {
      this.router.navigate(['/industries']);
      return;
    }


    this.user = this.authService.getUserData();
    if (this._industry.Filters.length >= 0) {
    }

    if (this._industry.LogoUrl)
      this.imageUrl = this._industry.LogoUrl ? this.sanitizer.bypassSecurityTrustUrl(environment.imageSrc + this._industry.LogoUrl) : '';
  }



  CheckValid(input: FormControl) {
    if (this.isEditing == true && this._industry.Id == "") {
      this.router.navigate(['/industries']);
      // return;
    }
    if (input.invalid && (this.pressedSave || input.touched)) {
      return 'red';
    }
    return '#515C66';
  }

  CheckValidFilterInput(input: FormControl) {
    if (input.invalid && (this.pressedSaveFilter || input.touched)) {
      return 'red';
    }
    return '#515C66';
  }
  // get Filters() {

  // }

  saveFilter() {
    this.pressedSaveFilter = true;

    if (!this.filterForm.valid) {

      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please Check your filter inputs' });
      return;
    }
    // edit Filter 
    if (this.enableEditFilter == true) {
      this.editFilter();
    } else {
      this.addFilter();
    }

    this.enableEditFilter = false;
    this.selectedFilter = new FilterModel();
    // console.log('newFilter', this.selectedFilter, this.selectedFilter);
    this.pressedSaveFilter = false;
    this.filterForm.reset();
  }


  addFilter() {
    var x = 0;
    // add Filter 
    if (this._industry.Filters.length > 0) {
      x = this._industry.Filters.reduce((a, b) => a.RowId > b.RowId ? a : b).RowId;
    }
    // first Inti To x when new added 
    if (x == undefined) this.selectedFilter.RowId = 1;
    else this.selectedFilter.RowId = ++x;

    this._industry.Filters.push(this.selectedFilter);


  }
  editFilter() {

    var index = 0;

    if (this.selectedFilter.Id == null)
      index = this._industry.Filters.findIndex((x) => x.RowId == this.selectedFilter.RowId);
    else
      index = this._industry.Filters.findIndex((x) => x.Id == this.selectedFilter.Id);
    this._industry.Filters[index].EnName = this.selectedFilter.EnName;
    this._industry.Filters[index].ArName = this.selectedFilter.ArName;
  }


  showSelectedFilter(filter) {
    if (filter.Id == null)
      this.selectedFilter = JSON.parse(JSON.stringify(this._industry.Filters.find((x) => x.RowId == filter.RowId)));
    else
      this.selectedFilter = JSON.parse(JSON.stringify(this._industry.Filters.find((x) => x.Id == filter.Id)));
    this.enableEditFilter = true;
    // this.selectedFilter = filter;


    //this.ref.detectChanges();
  }

  removeFilter(filter) {
    const index = this._industry.Filters.indexOf(filter, 0);
    if (index > -1) {
      this._industry.Filters.splice(index, 1);
      this.ref.detectChanges();
    }
  }

  save() {
    this.pressedSave = true;
    if (!this.industryForm.valid) {

      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please Check your industry inputs' });
      return;
    }
    let form: FormData = new FormData();
    form.append('request', JSON.stringify(this._industry));
    if (this._industry.Logo)
      form.append('Logo', this._industry.Logo);

    if (this.isEditing) {
      this.industryService.EditIndustry(form).subscribe(data => {
        if (data.HasError) {
          this.messageService.add({ severity: 'error', summary: 'Error', detail: data.EnErrorMessage });
        }
        else {
          this.router.navigate(['/industries'])
        }
      });
    }
    else {
      this.industryService.AddIndustry(form).subscribe((data) => {
        if (data.HasError) {
          console.log('post result', data);
        }
        else {
          this.router.navigate(['/industries']);
        }
      })
    }


  }

  uploadFile(files: any) {

    if (files.length === 0) {
      return;
    }
    var file = <File>files[0];
    const reader = new FileReader();
    this._industry.Logo = file;
    reader.readAsDataURL(file);
    reader.onload = (e: any) => {
      this.imageUrl = e.target.result;
    }
    this.fileToUpload.push(file);
    this.imageUrl = URL.createObjectURL(this.fileToUpload[0]);
  }
  clearFileInput() {
    this.fileToUpload = [];
    this.imageUrl = '';
  }

}


