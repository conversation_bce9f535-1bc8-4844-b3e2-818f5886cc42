<div class="container">
    <p-toast></p-toast>
    <div class="row" [formGroup]="mainInfoForm">
        <div class="col-md-4">
            <div class="text-center">
                <div class="image-avatar-container">
                    <label for="companyImage" class="w-100">
                        <img [src]="
                                imageUrl
                                    | placeholder
                                        : 'assets/layout/images/upload-image.png'
                            " class="roundeisSelectedFiled-circle w-100" alt="..." />
                        <p-button icon="pi pi-pencil" class="edit-button" styleClass="p-button-rounded"
                            (click)="file.click()"></p-button>
                    </label>
                    <input type="file" id="companyImage" class="d-none" (change)="uploadFile(file.files)"
                        accept="image/*" #file />
                </div>

                <span class="font-light" *ngIf="pressedNext && !this.imageUrl" class="text-danger">Please upload image
                    for this discount.</span>
            </div>
        </div>
        <div class="col-md-8">
            <div class="flex gap-2" *ngIf="this.user.UserType != 1">
                <p-avatar [image]="
                        companyProfileLogo
                            | placeholder
                                : 'assets/layout/images/upload-image.png'
                    " styleClass="mr-2" size="xlarge" shape="circle"></p-avatar>
                <span class="flex flex-column gap-2 mb-3">
                    <label for="company" [style.color]="
                            CheckValid(mainInfoForm.controls.company)
                        ">Company name</label>
                    <p-dropdown [options]="companies" [(ngModel)]="_discountData.CompanyId" placeholder="select Company"
                        optionLabel="EnName" optionValue="Id" id="company" (onChange)="refreshCompanyRelatedDetails()"
                        formControlName="company"></p-dropdown>
                </span>
            </div>

            <div>
                <span class="flex flex-column gap-2 mb-3">
                    <label for="EnDiscountTitle" [style.color]="
                            CheckValid(mainInfoForm.controls.discountEnTitle)
                        ">English Discount Title</label>
                    <input pInputText id="discountEnTitle" [(ngModel)]="_discountData.EnTitle" class="w-50"
                        formControlName="discountEnTitle" />
                </span>

                <span class="flex flex-column gap-2 mb-3">
                    <label for="ArDiscountTitle" [style.color]="
                            CheckValid(mainInfoForm.controls.discountArTitle)
                        ">Arabic Discount Title</label>
                    <input pInputText id="discountArTitle" [(ngModel)]="_discountData.ArTitle" class="w-50"
                        formControlName="discountArTitle" />
                </span>
            </div>

            <div class="flex flex-column gap-2 mb-3">
                <label for="discountType" [style.color]="
                        CheckValid(mainInfoForm.controls.discountType)
                    ">Discount type</label>
                <p-dropdown [options]="discountType" [(ngModel)]="_discountData.DiscountType" optionLabel="name"
                    optionValue="id" id="discountType" formControlName="discountType"></p-dropdown>
            </div>

            <div class="row">
                <div class="col-2">
                    <div class="flex flex-column gap-2 mb-3">
                        <label for="discountValue" [style.color]="
                                CheckValid(mainInfoForm.controls.discountValue)
                            ">Discount value</label>
                        <p-dropdown [options]="discountValues" [(ngModel)]="_discountData.DiscountValue"
                            optionLabel="name" id="discountValue" optionValue="id"
                            (ngModelChange)="updateOnDiscountChange()" formControlName="discountValue"></p-dropdown>
                    </div>
                </div>


                <div class="col-3">
                    <div class="flex flex-column gap-2 mb-3">
                        <label for="originalPrice" [style.color]="CheckValid( mainInfoForm.controls.originalPrice)
                        ">Original Price</label>
                        <!-- <pre>{{ selectedCompanyCurrency }}</pre> -->
                        <!-- [suffix]="' ' + (selectedCompany.CompanyCountries && selectedCompany.CompanyCountries.length > 0) ? selectedCompany.CompanyCountries[0]?.Country.Currency : '$' + ' '" -->
                        <p-inputNumber [min]="0" [suffix]="' ' + selectedCompanyCurrency" [useGrouping]="false" id="originalPrice"
                            [(ngModel)]="_discountData.OriginalPrice" (ngModelChange)="updateEstimatedSaving()"
                            formControlName="originalPrice" />
                    </div>
                </div>

                <div class="col-3">
                    <div class="flex flex-column gap-2 mb-3">
                        <label for="estimatedSavings" [style.color]=" CheckValid( mainInfoForm.controls.estimatedSavings )
                            ">Estimated savings</label>
                        <!-- <pre>{{ selectedCompanyCurrency }}</pre> -->
                        <!-- [suffix]="' ' + (selectedCompany.CompanyCountries && selectedCompany.CompanyCountries.length > 0) ? selectedCompany.CompanyCountries[0]?.Country.Currency : '$' + ' '" -->
                        <p-inputNumber [min]="0" [suffix]="' ' + selectedCompanyCurrency" [useGrouping]="false"
                            id="estimatedSavings" [(ngModel)]="_discountData.EstimatedSaving"
                            (ngModelChange)="updateOriginalPrice()" formControlName="estimatedSavings" />
                    </div>
                </div>

            </div>

            <div class="row">
                <div class="col-3">
                    <div class="flex flex-column gap-2 mb-3">
                        <label for="protectionType" [style.color]=" CheckValid(mainInfoForm.controls.protectionType)
                            ">Protection type</label>
                        <p-dropdown [options]="ProtectionType" [(ngModel)]="_discountData.ProtectionType"
                            optionLabel="name" id="protectionType" optionValue="id"
                            formControlName="protectionType"></p-dropdown>
                    </div>
                </div>
                <div class="col-3" *ngIf="_discountData.ProtectionType == 1">
                    <div class="flex flex-column gap-2 mb-3">
                        <label for="SwipeCode" [style.color]="CheckValid(mainInfoForm.controls.SwipeCode)">Swip
                            Code</label>
                        <input pInputText id="SwipeCode" [(ngModel)]="_discountData.SwipeCode"
                            [ngModelOptions]="{ standalone: true }" type="number" formControlName="SwipeCode" />
                    </div>
                </div>
            </div>

            <!-- // -->
            <div class="flex flex-column gap-2 mb-3">
                <label for="conditions" [style.color]="CheckValid(mainInfoForm.controls.conditions)">Conditions</label>
                <p-multiSelect [options]="conditions" [(ngModel)]="_discountData.Conditions"
                    defaultLabel="Select Conditions" optionLabel="EnName" display="chip" id="Conditions"
                    formControlName="conditions"></p-multiSelect>
            </div>
            <div class="flex flex-column gap-2 mb-3">
                <label for="terms" [style.color]="CheckValid(mainInfoForm.controls.EnTerms)">English Terms</label>
                <p-card role="region">
                    <textarea id="EnTerms" [(ngModel)]="_discountData.EnTerms"
                        style="width: 100%; border: none; outline: none" formControlName="EnTerms">
                    </textarea>
                </p-card>
                <label for="terms" [style.color]="CheckValid(mainInfoForm.controls.ArTerms)">Arabic Terms</label>
                <p-card role="region">
                    <textarea id="ArTerms" [(ngModel)]="_discountData.ArTerms"
                        style="width: 100%; border: none; outline: none" formControlName="ArTerms">
                </textarea>
                </p-card>
            </div>

            <div class="flex flex-column gap-2 mb-3">
                <label for="filters" [style.color]="CheckValid(mainInfoForm.controls.filters)">Filters</label>
                <p-multiSelect [options]="filters" [(ngModel)]="_discountData.Filters" defaultLabel="Select Filters"
                    optionLabel="EnName" display="chip" id="Filters" formControlName="filters"></p-multiSelect>
            </div>

            <div class="flex flex-column gap-2 mb-3">
                <label for="relatedBranches" [style.color]="
                        CheckValid(mainInfoForm.controls.relatedBranches)
                    ">Related branches</label>
                <p-multiSelect [options]="this.companyBranchs" [(ngModel)]="_discountData.Branches"
                    defaultLabel="Select related branches" optionLabel="EnName" display="chip" id="relatedBranches"
                    formControlName="relatedBranches"></p-multiSelect>
            </div>

            <div class="flex justify-content-end gap-2 mb-3">
                <!-- [disabled]="
                        !this.mainInfoForm.valid || !this.imageUrl
                    " -->
                <p-button label="Next" (onClick)="next()"></p-button>
            </div>
        </div>
    </div>
</div>