import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { catchError, map, Observable, of } from 'rxjs';
import { environment } from 'src/environments/environment';
import { Dto } from '../Model/Dto';
import { FilterModel } from '../Model/FiltersModel';
import { IndustryModel } from '../Model/IndustryModel';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class IndustryService {


  constructor(private httpClient: HttpClient, private authService: AuthService) {

  }

  GetAllFilters() {
    return this.httpClient.get<Dto<FilterModel>>(`${environment.apiUrl}` + 'Industry/GetAllFilters')
      .pipe(
        map((res: any) => {
          if (!res.HasError) {
            return res.ListResultContent;
          }
        })
      );
  }
  
  GetAllIndustry(fullView = false, page: number = null, pageSize: number = null, indusrtyName: string = "",) {
    let params = new HttpParams();
    params = params.set('FullView', fullView);
    if (page) {
      params = params.set('PageNumber', page);
    }
    if (pageSize) {
      params = params.set('PageSize', pageSize);
    }
    if (indusrtyName) {
      params = params.set('Name', indusrtyName);
    }
    return this.httpClient.get<Dto<IndustryModel>>(`${environment.apiUrl}` + 'Industry/GetAll', { params })
      .pipe(
        map((res: any) => {
            return res;
        })
      );
  }

  public DeleteIndustry(industryId: String): Observable<any> {
    var http;
    var url = `${environment.apiUrl}Industry/DeleteIndustry?industryId=${industryId}`;
    http = this.httpClient.delete(url);
    return http.pipe(
      map((res: any) => {

        return res;
      }),
      catchError((error) => {

        return of(false);
      })
    );
  }


  public AddIndustry(data): Observable<Dto<IndustryModel>> {
    var http;
    var url = `${environment.apiUrl}Industry/AddIndustry`;
    http = this.httpClient.post(url, data);
    return http.pipe(
      map((res: Dto<IndustryModel>) => {
          return res;
      }),
      catchError(error => {
        return of(false);
      }));
  }

  public EditIndustry(data): Observable<Dto<IndustryModel>> {
    var http;
    var url = `${environment.apiUrl}Industry/EditIndustry`;
    http = this.httpClient.put(url, data);
    return http.pipe(
      map((res: Dto<IndustryModel>) => {
        return res;
      }),
      catchError(error => {

        return of(false);
      }));
  }

}
