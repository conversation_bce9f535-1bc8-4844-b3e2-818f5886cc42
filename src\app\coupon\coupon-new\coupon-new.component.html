<div>
    <h3>
        <p-button icon="pi pi-arrow-left" styleClass="p-button-rounded p-button-secondary p-button-text"
        (click)="goBack()"></p-button>
        {{ title }}
    </h3>
    <p-tabView [(activeIndex)]="activeIndex">
        <p-tabPanel header="Main info" [ngStyle]="{ color: 'var(--cyan-300-color)' }">
            <p-card role="region">
                <coupon-main-info [_CouponData]="_CouponData" [(activeIndex)]="activeIndex" [editing]="editing"
                    [(fileToUpload)]="fileToUpload"></coupon-main-info>
            </p-card>
        </p-tabPanel>

    </p-tabView>
</div>