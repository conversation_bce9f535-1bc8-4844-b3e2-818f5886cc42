<p-toast></p-toast>
<h4>
    <p-button icon="pi pi-arrow-left" styleClass="p-button-rounded p-button-secondary p-button-text"
          (click)="goBack()"></p-button>
    Coupon Id: {{Coupon.UserFriendly}}
</h4>
<p-card>
    <div class="flex col-sm mb-2">
        <div class="col-sm">
            <h3 style="text-align: start">Company</h3>
            <div class="d-flex justify-content-start">
                <p-avatar image="{{getImageUrl( Coupon!.Company!.LogoUrl)}}" styleClass="mr-2" size="large"
                    shape="circle"></p-avatar>
                <span>{{ Coupon!.Company!.EnName }}</span>
            </div>
        </div>
        <div class="col-sm">
            <h3 style="text-align: start">English Title</h3>
            <div class="d-flex justify-content-start">
                <p-avatar image="{{getImageUrl( Coupon.ImageUrl)}}" styleClass="mr-2" size="large"
                    shape="circle"></p-avatar>
                <span>{{ Coupon.EnTitle }}</span>
            </div>
        </div>
        <div class="col-sm">
            <h3 style="text-align: start">Arabic Title</h3>
            <div class="d-flex justify-content-start">
                <p-avatar image="{{getImageUrl( Coupon.ImageUrl)}}" styleClass="mr-2" size="large"
                    shape="circle"></p-avatar>
                <span>{{ Coupon.ArTitle }}</span>
            </div>
        </div>
    </div>

    <p-divider></p-divider>
    <div class="row mb-2">
        <div class="col-sm">
            <h4 style="text-align: start">Value</h4>
            <div class="d-flex justify-content-start">
                <span>{{ Coupon.CouponValue | CouponValue }}</span>
            </div>
        </div>
        <div class="col-sm">
            <h4 style="text-align: start">Original Price</h4>
            <div class="d-flex justify-content-start">
                {{ Coupon.OriginalPrice }}
            </div>
        </div>
        <div class="col-sm">
            <h4 style="text-align: start">Estimated Saving</h4>
            <div class="d-flex justify-content-start">
                {{ Coupon.EstimatedSaving }}
            </div>
        </div>
    </div>
    <br />
    <div class="row mb-2">
        <div class="col-sm">
            <h4 style="text-align: start">Protection Type</h4>
            <div class="d-flex justify-content-start">
                {{ Coupon.ProtectionType | ProtectionType}}
            </div>
        </div>
        <br />
        <div class="col-sm" *ngIf="Coupon.ProtectionType === 1">
            <h4 style="text-align: start">Protection Code</h4>
            <div class="d-flex justify-content-start">
                {{ Coupon.SwipeCode }}
            </div>
        </div>
    </div>
    <p-divider></p-divider>
    <div class="row mb-2">
        <div class="col-sm">
            <h4 style="text-align: start">Conditions</h4>
            <div class="d-flex justify-content-start">
                <div *ngFor="let item of Coupon.CouponConditions" class="mx-2">
                    <i *ngIf="item.EnName === 'Dine-in'" class="fa-solid fa-utensils"> </i>
                    <i *ngIf="item.EnName === '2 people'" class="fas fa-user-friends"> </i>

                    {{ item.EnName }}
                </div>
            </div>
        </div>
        <div class="col-sm">
            <h4 style="text-align: start">Filters</h4>
            <div class="d-flex justify-content-start">
                <div *ngFor="let item of Coupon.CouponFilters" class="mx-2">
                    <p-tag styleClass="mr-2" severity="info" value="{{ item.EnName }}" [rounded]="true"></p-tag>
                </div>
            </div>
        </div>
    </div>
    <p-divider></p-divider>
    <div>
        <h4 style="text-align: start">English Terms</h4>
        <div class="d-flex justify-content-start">
            <span> {{ Coupon.EnTerms }}</span>
        </div>
    </div>
    <br />
    <div>
        <h4 style="text-align: start">Arabic Terms</h4>
        <div class="d-flex justify-content-start">
            <span> {{ Coupon.ArTerms }}</span>
        </div>
    </div>
    <p-divider></p-divider>
    <div>
        <h4 style="text-align: start">Related branches</h4>
        <div class="d-flex justify-content-start">
            <div *ngFor="let item of Coupon.CouponBranches" class="mx-2">
                <p-tag styleClass="mr-2" severity="info" value="{{ item.EnName }}" [rounded]="true"></p-tag>
            </div>
        </div>
    </div>
</p-card>
<br />
<p-card>
    <div class="row mb-2">

        <div class="col-sm">
            <h4 style="text-align: start">Start Date</h4>
            <div class="d-flex justify-content-start">
                {{ Coupon.StartDate.toString() | date : "dd/MM/yyyy" }}
            </div>
        </div>
        <div class="col-sm">
            <h4 style="text-align: start">End Date</h4>
            <div class="d-flex justify-content-start">
                {{ Coupon.EndDate.toString() | date : "dd/MM/yyyy" }}
            </div>
        </div>
        <div class="col-sm">
            <h4 style="text-align: start">Targeted Users</h4>
            <div class="d-flex justify-content-start">
                <span>{{ Coupon.TargetedUserNum }}</span>
            </div>
        </div>
    </div>
    <p-divider></p-divider>

</p-card>
<br />


<p-card>
    <div class="d-flex justify-content-end">
        <!-- <p-button *ngIf="!checkIfRejected" label="Reject" styleClass="p-button-danger mx-2"(click)="Reject()"></p-button> -->
        <div *ngIf="!checkIfRejected">
            <p-button label="Reject" styleClass="p-button-danger mx-2" (click)="showRejectReasonDialog()"></p-button>
            <p-dialog header="Rejecting reason" [(visible)]="rejectDialogVisibility" [modal]="true"
                [style]="{ width: '50vw' }" [resizable]="false">
                <p-card>
                    <div class="d-flex justify-content-start flex-column">
                        <textarea class="rejecting-reason" [(ngModel)]="Coupon.NoteCoupon"
                            style="width: 100%; border: none; outline: none"
                            placeholder="Please enter a reason for rejecting this Coupon..."
                            [formControl]="rejectReasonControl">
                        </textarea>
                        <span *ngIf="rejectReasonControl.invalid && rejectReasonControl.touched"
                            class="text-danger">Please enter a reason to reject this Coupon.</span>
                    </div>
                </p-card>
                <ng-template pTemplate="footer">
                    <p-button (click)="rejectDialogVisibility = false" label="Cancel"
                        styleClass="p-button-text"></p-button>
                    <p-button (click)="Reject()" label="Reject" styleClass="p-button-text"
                        styleClass="p-button-danger mx-2"></p-button>
                </ng-template>
            </p-dialog>
        </div>
        <p-button *ngIf="!checkIfApproved" label="Accept" (click)="Accept()"
            styleClass="p-button-success mx-2"></p-button>
    </div>
</p-card>