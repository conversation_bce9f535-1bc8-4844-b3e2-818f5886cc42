import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { ConfirmationService, MessageService } from 'primeng/api';
import { MallModel } from 'src/app/Model/MallModel';
import { UserModel } from 'src/app/Model/UserModel';
import { AuthService } from 'src/app/services/auth.service';
import { MallService } from 'src/app/services/mall.service';
import { Countries } from 'src/app/utilities/countries';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-malls-list',
  templateUrl: './malls-list.component.html',
  styleUrls: ['./malls-list.component.scss'],
  providers: [MessageService, ConfirmationService],
})
export class MallsListComponent {
  malls: MallModel[];
  loading: boolean = true;
  user: UserModel;

  //filter
  Name: any;
  Location: any;
  totalRecords: number = 0;
  pageSize: number = 10;
  pageIndex: number = 0;
  paginator: boolean = false;
  //  track the first load
  private isInitialLoad: boolean = false;


  constructor(private mallService: MallService, private router: Router, private messageService: MessageService, private confirmationService: ConfirmationService, private authService: AuthService) {

  }


  ngOnInit() {
    this.loadData({ first: 0, rows: this.pageSize });
  }

  loadData(event: any) {
    // Avoid double loading during the first render
    if (this.isInitialLoad) {
      this.isInitialLoad = false; // Set the flag to false after the first load
      return;
    }

    this.user =  this.authService.getUserData();
    const pageNumber = event.first / event.rows + 1; // Calculate the page number
    const pageSize = event.rows; // Rows per page
    this.paginator = true;
    if (this.user.UserType == 0) {
      this.fetchData(pageNumber, pageSize);
    }
  }

  fetchData(pageNumber, pageSize) {
    this.mallService
      .GetAllMalls(true, pageNumber, pageSize, this.Name, this.Location,)
      .subscribe((data) => {
        this.malls = data.ListResultContent;
        this.totalRecords = data.TotalRecords; // Set total records for pagination
        this.loading = false;
      });

  }

  edit(mall, state: string) {
    this.mallService.getMallById(mall.Id.toString()).subscribe((data) => {
      this.router.navigate(["mall-edit"], {
        state: {
          data: data,
          command: "edit",
        },
      });
    });

  }

  ReceivedFilteredData(event) {
    this.Name = event.Name;
    this.Location = event.Location;
    this.fetchData(1, this.pageSize);

    this.malls = event.ListResultContent;
    this.totalRecords = event.TotalRecords; // Set total records for pagination
    //this.paginator = false;

  }
  delete(id) {
    this.confirmationService.confirm({
      key: "confirm1",
      target: event.target,
      message: "Are You Sure Delete This Mall",
      icon: "pi pi-exclamation-triangle",
      accept: () => {
        this.mallService
          .DeleteMall(id)
          .subscribe((data: any) => {
            if (data['HasError'] == false) {
              this.messageService.add({
                severity: "success",
                summary: "Success Message",
                detail: "Delete Mall Successfully",
              });
              var mall = this.malls.find((x) => x.Id == id);
              const index = this.malls.indexOf(mall, 0);
              // Update the data array (remove the deleted item)
              this.malls = this.malls.filter(currentItem => currentItem.Id !== mall.Id);
            }
            else {
              this.messageService.add({
                severity: "error",
                summary: "Error",
                detail: data['EnErrorMessage'],
              });
            }
          });
      }
      , reject: () => {
        this.messageService.add({
          severity: "error",
          summary: "Rejected",
          detail: "You have rejected",
        });
      },
    });

  }

  getLogoUrl(logo) {
    return logo ? environment.imageSrc + logo : '';
    // return logo ? this.sanitizer.bypassSecurityTrustUrl(environment.imageSrc + logo) : '';
  }
}
