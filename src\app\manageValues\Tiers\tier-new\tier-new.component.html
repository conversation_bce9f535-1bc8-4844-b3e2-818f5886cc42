<!-- <p-toast></p-toast> -->
<div>

      <h3>
            <p-button icon="pi pi-arrow-left" styleClass="p-button-rounded p-button-secondary p-button-text"
                  routerLink="/tiers">
            </p-button>
            {{title}}
      </h3>
      <!-- <p-messages *ngIf="this.newTier.HasError" [value]="msgs"></p-messages> -->
      <!-- <p-tabView [activeIndex]="1"> -->
      <p-tabView [(activeIndex)]="activeIndex" (onChange)="onTabChange($event)">
            <p-tabPanel header="Main Info" [ngStyle]="{ color: 'var(--cyan-300-color)' }">
                  <p-card role="region">
                        <tier-main-info [(_TierData)]="newTier" [(activeIndex)]="activeIndex"
                              [isEditing]="isEditing"></tier-main-info>
                  </p-card>
            </p-tabPanel>
            <p-tabPanel header="Tier Statistics" [disabled]="tabsDisabled" *ngIf="isEditing">
                  <p-card role="region">
                        <tier-statistics [(_TierData)]="newTier" [(activeIndex)]="activeIndex"></tier-statistics>
                  </p-card>
            </p-tabPanel>

      </p-tabView>
</div>