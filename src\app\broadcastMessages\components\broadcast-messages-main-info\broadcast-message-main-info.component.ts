import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CompanyModel } from 'src/app/Model/CompanyModel';
import { BroadcastMessageModel } from 'src/app/Model/BroadcastMessageModel';
import { MessageService } from 'primeng/api';
import { environment } from 'src/environments/environment';
import { DomSanitizer } from '@angular/platform-browser';
import { ToastrService } from 'ngx-toastr';
import { TranslateService } from '@ngx-translate/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CompanyService } from 'src/app/services/company.service';
import { UserModel } from 'src/app/Model/UserModel';
import { BroadcastMessageTypesEnum } from 'src/app/enum/broadcast-message-type-enum';
import { Countries } from 'src/app/utilities/countries';
import { TierService } from 'src/app/services/tier.service';
import { EndpointUsedInEnum } from 'src/app/enum/endpoint-used-in-enum';
import { PhoneNumberModel } from 'src/app/Model/PhoneNumberModel';
import { AuthService } from 'src/app/services/auth.service';



@Component({
    selector: 'broadcast-message-main-info',
    templateUrl: './broadcast-message-main-info.component.html',
    styleUrls: ['./broadcast-message-main-info.component.scss'],
  
})
export class BroadcastMessageMainInfoComponent implements OnInit {
    currentDate: string;
    @Input() _BroadcastMessageData: BroadcastMessageModel;
    BroadcastMessageValues: { id: string; name: string }[] = [];
    companies: CompanyModel[] = [];
    @Input() editing: boolean;
    @Input() activeIndex: number;
    @Output() activeIndexChange: EventEmitter<number> = new EventEmitter();
    pressedNext = false;
    companyProfileimage: any;
    selectedCompany: CompanyModel = new CompanyModel();
    imageUrl: any = '';
    user: UserModel;

    mainInfoForm = new FormGroup({
        //BroadcastMessageImage: new FormControl([], [Validators.required]),
        company: new FormControl('', [Validators.required]),
        EnTitle: new FormControl('', [Validators.required, Validators.minLength(3)]),
        ArTitle: new FormControl('', [Validators.required, Validators.minLength(3)]),
        EnContent: new FormControl('', [Validators.required, Validators.minLength(3)]),
        ArContent: new FormControl('', [Validators.required, Validators.minLength(3)]),
        type: new FormControl('', [Validators.required]),
        mainPhone: new FormControl('',),
        mainPhoneCode: new FormControl(''),
        whatsappNumber: new FormControl('',),
        whatsappNumberCode: new FormControl(''),

    });
    types = [];
    allCountries: Countries = new Countries();

    countries = [];
    constructor(private messageService: MessageService, private companyService: CompanyService, private sanitizer: DomSanitizer, private toastr: ToastrService, private translate: TranslateService, private route: ActivatedRoute, private router: Router, private tierService: TierService, private authService: AuthService) {
        this.currentDate = new Date().toISOString().slice(0, 10);
        // Convert enum to an array of objects for types
        this.types =
            Object.keys(BroadcastMessageTypesEnum)
                .filter(key => isNaN(Number(key)))  // Filter out numeric keys (enum reverse keys)
                .map(key => ({
                    label: key,
                    value: BroadcastMessageTypesEnum[key]
                }));
    }
    ngOnInit(): void {
        this.allCountries.AllCOUNTRIES = [
            { name: '', mobileCode: null, code: '', utc: '', timezone: '' }, // Add empty option dynamically
            ... this.allCountries.AllCOUNTRIES,
        ];
        this.user =  this.authService.getUserData();
        if (this.user.UserType == 1) {
            this.mainInfoForm.get('company').clearValidators();
        }

        this._BroadcastMessageData.CompanyId = null;

        this.companyService.GetAllId( EndpointUsedInEnum.BroadcastMessage).subscribe((data) => {
            this.companies = data;
            if (this._BroadcastMessageData.Company) { // edit BroadcastMessage
                this.selectedCompany = this.companies.find(x => x.Id == this._BroadcastMessageData.Company.Id);
                this._BroadcastMessageData.CompanyId = this._BroadcastMessageData.Company.Id;
                this._BroadcastMessageData.Countries = this.selectedCompany.CompanyCountries;
                this.companyProfileimage = this.selectedCompany.LogoUrl ? this.sanitizer.bypassSecurityTrustUrl(environment.imageSrc + this.selectedCompany.LogoUrl) : '';
                this._BroadcastMessageData.TargetedCities.map(key => ({
                    Id: key.Id,
                    ArName: key.ArName,
                    EnName: key.EnName,

                }));;;
                // this._BroadcastMessageData.Tiers = this.selectedCompany.CompanyTiers.map(key => ({
                //     Id: key.Id,
                //     ArName:key.ArName,
                //     EnName: key.EnName,
                //     Rank:key.Rank
                // }));;
            }else{
                 // BY Defualt init CompanyId & Countries
            this._BroadcastMessageData.CompanyId = this.companies[0].Id;
            this._BroadcastMessageData.Company = this.companies[0];
            this._BroadcastMessageData.Countries = this.companies[0].CompanyCountries;
            this.companyProfileimage = this.companies[0].LogoUrl ? this.sanitizer.bypassSecurityTrustUrl(environment.imageSrc + this.companies[0].LogoUrl) : '';
            }
            if (this.user.UserType == 1) {
                this.selectedCompany = this.companies[0];
                this.companyProfileimage = environment.imageSrc + this.selectedCompany.LogoUrl;
                this._BroadcastMessageData.CompanyId = this.selectedCompany.Id;
                this._BroadcastMessageData.Company = this.selectedCompany;
                this._BroadcastMessageData.Countries = this.selectedCompany.CompanyCountries;
                // this._BroadcastMessageData.Tiers = this.selectedCompany.CompanyTiers.map(key => ({
                //     Id: key.Id,
                //     ArName:key.ArName,
                //     EnName: key.EnName,
                //     Rank:key.Rank
                // }));;
            }

            if (this.route.snapshot.url[this.route.snapshot.url.length - 1].path === 'broadcast-message-edit') {
                if (!this._BroadcastMessageData.Id) {
                    this.router.navigate(['/broadcast-messages-inbox']);
                    return;
                }
            }
        });


        // if (this._BroadcastMessageData.Company && this._BroadcastMessageData.Company.LogoUrl) {
        //     this.companyProfileimage = environment.imageSrc + this._BroadcastMessageData.Company.LogoUrl;
        // }
        if (this._BroadcastMessageData.PhoneNumber == null) {
            this._BroadcastMessageData.PhoneNumber = new PhoneNumberModel();
        }
        if (this._BroadcastMessageData.WhatsappNumber == null) {
            this._BroadcastMessageData.WhatsappNumber = new PhoneNumberModel();
        }
        //by default Tiers  & Subscriptions
        this.tierService.getAllTiers().subscribe((data) => {

            this._BroadcastMessageData.Tiers = data.map(key => ({
                Id: key.Id,
                ArName: key.ArName,
                EnName: key.EnName,
                Rank: key.Rank,
                Color: key.Color
            }));;;
            // this.countryService.Countries.subscribe((data) => {
            //     this.countries = data;
            // });
        });
        this.setValidatorsPhoneNumber();
    }

    refreshCompanyRelatedDetails() {
        let selectedCompany = this.companies.find(x => x.Id == this._BroadcastMessageData.CompanyId);
        this._BroadcastMessageData.CompanyId = selectedCompany.Id;
        this._BroadcastMessageData.Company = selectedCompany;
        this._BroadcastMessageData.Tiers = this.selectedCompany.CompanyTiers;
        this._BroadcastMessageData.Countries = this.selectedCompany.CompanyCountries;
        // this.companyProfileimage  = environment.imageSrc + selectedCompany.imageUrl.split("wwwroot\\")[1];
        this.companyProfileimage = selectedCompany.LogoUrl ? this.sanitizer.bypassSecurityTrustUrl(environment.imageSrc + selectedCompany.LogoUrl) : '';
        // this.companyProfileimage = selectedCompany.imageUrl;
        this.selectedCompany = selectedCompany; 
    }
    next() {
        // Mark all controls as touched to trigger validation
        this.mainInfoForm.markAllAsTouched();
        this.setValidatorsPhoneNumber();

        if (!this.mainInfoForm.valid) {
            this._BroadcastMessageData.hasError = true;
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Check your input' });
            return;
        }

        this.pressedNext = true;
        this._BroadcastMessageData.hasError = false;
        if (this._BroadcastMessageData.PhoneNumber?.Number)
            this._BroadcastMessageData.PhoneNumber.Number = this._BroadcastMessageData.PhoneNumber.Number.toString();
        if (this._BroadcastMessageData.WhatsappNumber?.Number)
            this._BroadcastMessageData.WhatsappNumber.Number = this._BroadcastMessageData.WhatsappNumber.Number.toString();
        this.activeIndex++;
        this.activeIndexChange.emit(this.activeIndex);
    }


    CheckValid(input: FormControl) {
        if (input.invalid && (this.pressedNext || input.touched)) {
            return 'red';
        }
        return '#515C66';
    }

    setValidatorsPhoneNumber() {

        this.mainInfoForm.get('mainPhoneCode')?.valueChanges.subscribe(value => {
            const mainPhone = this.mainInfoForm.get('mainPhone');
            if (value) {
                mainPhone?.setValidators([Validators.required]);
            } else {
                mainPhone?.clearValidators();
            }

            mainPhone?.updateValueAndValidity({ emitEvent: false }); // Update control validity
        });
        this.mainInfoForm.get('mainPhone')?.valueChanges.subscribe(value => {
            const mainPhoneCode = this.mainInfoForm.get('mainPhoneCode');
            if (value) {
                mainPhoneCode?.setValidators([Validators.required]);
            } else {
                mainPhoneCode?.clearValidators();
            }

            mainPhoneCode?.updateValueAndValidity({ emitEvent: false }); // Update control validity
        });

        this.mainInfoForm.get('whatsappNumberCode')?.valueChanges.subscribe(value => {
            const whatsappNumber = this.mainInfoForm.get('whatsappNumber');
            if (value) {
                whatsappNumber?.setValidators([Validators.required]);
            } else {
                whatsappNumber?.clearValidators();
            }

            whatsappNumber?.updateValueAndValidity({ emitEvent: false }); // Update control validity
        });

        this.mainInfoForm.get('whatsappNumber')?.valueChanges.subscribe(value => {
            const whatsappNumberCode = this.mainInfoForm.get('whatsappNumberCode');
            if (value) {
                whatsappNumberCode?.setValidators([Validators.required]);
            } else {
                whatsappNumberCode?.clearValidators();
            }

            whatsappNumberCode?.updateValueAndValidity({ emitEvent: false }); // Update control validity
        });
        return null;
    }

}
