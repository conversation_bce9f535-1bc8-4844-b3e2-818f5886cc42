import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { CountryTierUpgradingMethodsEnum } from 'src/app/enum/country-tier-upgrading-methods-enum';
import { Country } from 'src/app/Model/Country';
import { TiersModel } from 'src/app/Model/TiersModel';
import { TierService } from 'src/app/services/tier.service';

@Component({
  selector: 'country-available-tiers',
  templateUrl: './country-available-tiers.component.html',
  styleUrls: ['./country-available-tiers.component.scss'],

})
export class CountryAvailableTiersComponent implements OnInit {
  @Input() _CountryData: Country;
  @Input() isEditing: boolean;
  @Input() activeIndex: number;
  @Output() activeIndexChange: EventEmitter<number> = new EventEmitter();
  routeState: any;
  pressedNext = false;
  CountryTierUpgradingMethods = Object.keys(CountryTierUpgradingMethodsEnum)
    .filter(key => !isNaN(Number(CountryTierUpgradingMethodsEnum[key as keyof typeof CountryTierUpgradingMethodsEnum])))
    .map(key => ({
      label: key,
      value: CountryTierUpgradingMethodsEnum[key as keyof typeof CountryTierUpgradingMethodsEnum],
      checked: true
    }));
  TierStatusMessage: string = '';
  selectedMethods: number[] = [];
  Tiers: TiersModel[] = [];

  isPointsPerYear: boolean = true;
  isPricePerYear: boolean = true;
  isTierDiscount: boolean = true;
  constructor(private router: Router, private messageService: MessageService, private ref: ChangeDetectorRef
    , private tierService: TierService) { }

  ngOnInit(): void {
    this.refreshUpgradingMethods()
    if (this._CountryData.CountryTierUpgradingMethods.length == 0) {
      this.selectedMethods.push(CountryTierUpgradingMethodsEnum.Cash);
      this.selectedMethods.push(CountryTierUpgradingMethodsEnum.Points);
    }
    //by default Tiers  
    if (this._CountryData.CountryTiers.length > 0)
      this._CountryData.CountryTiers = this._CountryData.CountryTiers.map(
        tier => ({
          Id: tier.Id,
          ArName: tier.ArName,
          EnName: tier.EnName,
          Rank: tier.Rank,
          TotalEndUsers: tier.TotalEndUsers,
          PricePerYear: tier.PricePerYear,
          PointsPerYear: tier.PointsPerYear,
          Checked: false
        }));
    this.tierService.getAllTiers().subscribe((data) => {
      this.Tiers = data.map(tier => ({
        Id: tier.Id,
        ArName: tier.ArName,
        EnName: tier.EnName,
        Rank: tier.Rank,
        Checked: this._CountryData.CountryTiers.some(ct => ct.Id === tier.Id),
        TotalEndUsers: this._CountryData.CountryTiers.find(ct => ct.Id === tier.Id)?.TotalEndUsers,
        PointsPerYear: this._CountryData.CountryTiers.find(ct => ct.Id === tier.Id)?.PointsPerYear,
        PricePerYear: this._CountryData.CountryTiers.find(ct => ct.Id === tier.Id)?.PricePerYear,
      }));
    });
    this.refreshUpgradingMethods()
    // this.refreshTierUpgradingMethods();
  }
  checkMethod(method) {

  }
  next() {
    this._CountryData.CountryTierUpgradingMethods = this.selectedMethods;
    this._CountryData.CountryTiers = this.Tiers.filter(x => x.Checked);
    if (this._CountryData.CountryTierUpgradingMethods.length < 1) {
      this._CountryData.HasError = true
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'You Must Choose One Of Upgrading Methods At Least' });
      window.scroll({
        top: 0,
        left: 0,
        behavior: 'smooth'
      });
      return;
    }
    if (this.Tiers.filter(x => x.Checked).length < 1) {
      this._CountryData.HasError = true
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'You Must Choose One Of Tier At Least' });
      window.scroll({
        top: 0,
        left: 0,
        behavior: 'smooth'
      });
      this.TierStatusMessage = 'Pick One At Least';
      return;
    }

    if (this.isPointsPerYear && this.Tiers.filter(x => x.Checked && (x.PointsPerYear == null)).length > 0) {
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please check your inputs' });
      window.scroll({
        top: 0,
        left: 0,
        behavior: 'smooth'
      });
      this.TierStatusMessage = 'Please add values for all Tier Points Per Year';
      return;
    }
    if (this.isPricePerYear && this.Tiers.filter(x => x.Checked && (x.PricePerYear == null)).length > 0) {
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please check your inputs' });
      window.scroll({
        top: 0,
        left: 0,
        behavior: 'smooth'
      });
      this.TierStatusMessage = 'Please add values for all Tier Price Per Year';
      return;
    }
    this.TierStatusMessage = '';
    if (this._CountryData.IsTierDiscountEnabled && !this._CountryData.TierDiscountValue) {
      this._CountryData.HasError = true
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please Insert Tiers Discount Value' });
      window.scroll({
        top: 0,
        left: 0,
        behavior: 'smooth'
      });
      return;
    }
    // if (this._CountryData.HasError == true) {
    //   this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please Check the tabs data !' });
    //   window.scroll({
    //     top: 0,
    //     left: 0,
    //     behavior: 'smooth'
    //   });
    //   return;
    // }
    else {
      this.pressedNext = true;
      this._CountryData.HasError = false;
      // The activeIndex was set to a direct value because during editing, the navigate tabs, a problem in navigation when pressing the buttons (next, back).
      this.activeIndex = 4;
      this.activeIndexChange.emit(this.activeIndex);
    }
  }

  checkTier(tier) {
    if (!tier.Checked) {
      tier.PointsPerYear = 0; // Set points to 0 when unchecked
      tier.PricePerYear = 0;
    }
  }
  refreshUpgradingMethods() {
    if (this._CountryData.CountryTierUpgradingMethods.length > 0) {
      this.selectedMethods = this._CountryData.CountryTierUpgradingMethods;
      this.isPointsPerYear = this.selectedMethods?.includes(0);
      this.isPricePerYear = this.selectedMethods?.includes(1);
    }

  }
  checkUpgradingMethods(method) {
    if (method.value == 0) { this.isPointsPerYear = this.selectedMethods?.includes(method.value); }
    else if (method.value == 1) { this.isPricePerYear = this.selectedMethods?.includes(method.value); }
  }

  onChangeIsTierDiscountEnabled() {
    if (!this._CountryData.IsTierDiscountEnabled) {
      this._CountryData.TierDiscountValue = 0;
    }
  }
  back() {
    // The activeIndex was set to a direct value because during editing, the navigate tabs, a problem in navigation when pressing the buttons (next, back).
    this.activeIndex = 2;
    this.activeIndexChange.emit(this.activeIndex);
  }
}
