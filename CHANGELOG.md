# Changelog

## 15.0.0
**Migration Guide**
- Upgrade to PrimeNG 15
- Upgrade to Angular 15

**Implemented New Features and Enhancements:**
- Update to PrimeNG 15
- Update to Angular 15

## 14.0.1
**Fixed bugs:**
- Fixed a bug causes menu malfunction in menu service.

## 14.0.0
**Migration Guide**
- Upgrade to PrimeNG 14
- Upgrade to Angular 14
  
**Implemented New Features and Enhancements:**
- Update to PrimeNG 14
- Update to Angular 14
- Add primary color variable to theme variables.

## 13.1.0
**Migration Guide**

- Upgrade to PrimeFlex 3.1.2
- Upgrade to Angular 13 and PrimeNG 13.1.0
## 12.2.0
**Migration Guide**
- Update theme files and layout files.

**Implemented New Features and Enhancements:**

- Styles of new PrimeNG components

## 12.0.0
**Migration Guide**
- Update your project to Angular 12.
- Update app.menuitem.component.ts
- Update theme files and layout files.

**Implemented New Features and Enhancements:**

- Upgrade to Angular and PrimeNG 12
- Styles of new PrimeNG components

## 11.0.0
**Migration Guide**
- Update your project to Angular 11.
- Update app.topbar.component.ts.
- Update theme files and layout files.

**Implemented New Features and Enhancements:**

- Upgrade to Angular and PrimeNG 11
- Styles of new PrimeNG components

## 10.1.0

**Migration Guide**
- Update app files(app.menuitem.component.ts etc..).
- Update theme files and layout files.

**Implemented New Features and Enhancements:**

- New Design for Menu, Topbar and Config
- New Design for Template Pages (Error, Login, Not Found, Access Denied and Help)
- Implementation of Dim, Dark and Light Modes


## 10.0.0

**Migration Guide**
- Update your project to Angular 10.
- Update theme files and layout files.

**Implemented New Features and Enhancements:**

- Upgrade to Angular and PrimeNG 10
- Migrate to PrimeOne Design Architecture

## 9.0.0
###### * Angular 9 and PrimeNG 9

**Migration Guide**
- Update dependencies with <a href="https://angular.io/cli/update">ng update</a>.
- Update theme.css and layout.css files.
- Update app.menu.component.ts while retaining your MenuModel.
- Include app.menuitem.component.ts under app folder and define it app.module.ts with declarations property.
- Include app.menu.service.ts under app folder and define it app.module.ts with providers property.

## 8.0.0

**Migration Guide**
- Update your project to Angular 8 with ng update. View the official update guide -<a href="https://update.angular.io/">https://update.angular.io/</a>- for more information.
- Update app.component.ts.
- Update app.menu.component.ts.
- Update layout css files.
- Update theme css files.

## 7.0.0

**Migration Guide**
- Update layout css files.
- Update theme css files.

## 6.0.0 to 6.1.0
###### * Adds support for new features in PrimeNG 6.1.x

**Migration Guide**
- Update theme css files.
- Update layout css files.

## 6.0.0
###### * Brings support for Angular 6 and RxJS 6, adds theming for new components in PrimeNG such as the new TreeTable and improves behaviors of the layout menus.

**Migration Guide**
- Add PrimeIcons package.
- Update app.module.ts.
- Update app.component.ts.
- Update app.menu.component.ts.
- Update app.topbar.component.ts.
- Update layout css files.
- Update theme css files.

## 5.2.0
###### * Adds support for PrimeNG 5.2.0 (e.g. TurboTable), replaces nanoscroller with PrimeNG ScrollPanel.

**Migration Guide**
- Remove nanoscroller as it is replaced by ScrollPanel component of PrimeNG.
- Update app.component.ts and app.component.html.
- Update app.menu.component.ts.
- Update layout css files.
- Update theme css files.

## 5.0.0

**Migration Guide**
- Update theme css files.
- Added utils page.
- Includes version updates to PrimeNG 5 and Angular 5.

## 4.2.0

**Migration Guide**
- Update theme css files.

## 4.1.0

**Migration Guide**
- Update layout css files.
- Update theme css files.
- Update AppSubmenu component in app.menu.component.ts.

## 4.0.1

**Migration Guide**
- Update theme css.

## 4.0

**Migration Guide**
- Includes version updates to PrimeNG 4 and Angular 4, no other changes.

## 1.0.1

**Migration Guide**
- Update AppSubmenu component in app.menu.component.ts by replacing the itemClick method implementation.
- Update layout css files, there are no changes on themes.
- Update app.component.ts by changing onTopbarMenuButtonClick method implementation to add <i>event.preventDefault()</i> at the end.
