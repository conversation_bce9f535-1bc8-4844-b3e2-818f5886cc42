<div class="container">
    <p-toast></p-toast>
    <div class="row" [formGroup]="mainInfoForm">
        <div class="col-md-4">
            <div class="text-center">
                <div class="image-avatar-container">
                    <label for="companyImage" class="w-100">
                        <img [src]="
                                imageUrl
                                    | placeholder
                                        : 'assets/layout/images/upload-image.png'
                            " class="roundeisSelectedFiled-circle w-100" alt="..." />
                        <p-button icon="pi pi-pencil" class="edit-button" styleClass="p-button-rounded"
                            (click)="file.click()"></p-button>
                    </label>
                    <input type="file" id="companyImage" class="d-none" (change)="uploadFile(file.files)"
                        accept="image/*" #file />
                </div>

                <span class="font-light" *ngIf="pressedNext && !this.imageUrl" class="text-danger">Please upload image
                    for this Coupon.</span>
            </div>
        </div>
        <div class="col-md-8">
            <div class="flex gap-2" *ngIf="this.user.UserType != 1">
                <p-avatar [image]="
                        companyProfileimage
                            | placeholder
                                : 'assets/layout/images/upload-image.png'
                    " styleClass="mr-2" size="xlarge" shape="circle"></p-avatar>
                <span class="flex flex-column gap-2 mb-3">
                    <label for="company" [style.color]="
                            CheckValid(mainInfoForm.controls.company)
                        ">Company name</label>
                    <p-dropdown [options]="companies" [(ngModel)]="_CouponData.CompanyId" placeholder="select Company"
                        optionLabel="EnName" optionValue="Id" id="company" (onChange)="refreshCompanyRelatedDetails()"
                        formControlName="company"></p-dropdown>
                </span>
            </div>

            <div>
                <span class="flex flex-column gap-2 mb-3">
                    <label for="EnCouponTitle" [style.color]="
                            CheckValid(mainInfoForm.controls.CouponEnTitle)
                        ">English Coupon Title</label>
                    <input pInputText id="CouponEnTitle" [(ngModel)]="_CouponData.EnTitle" class="w-50"
                        formControlName="CouponEnTitle" />
                </span>

                <span class="flex flex-column gap-2 mb-3">
                    <label for="ArCouponTitle" [style.color]="
                            CheckValid(mainInfoForm.controls.CouponArTitle)
                        ">Arabic Coupon Title</label>
                    <input pInputText id="CouponArTitle" [(ngModel)]="_CouponData.ArTitle" class="w-50"
                        formControlName="CouponArTitle" />
                </span>
            </div>

            <div class="row">
                <div class="col-2">
                    <div class="flex flex-column gap-2 mb-3">
                        <label for="CouponValue" [style.color]="
                                CheckValid(mainInfoForm.controls.CouponValue)
                            ">Coupon value</label>
                        <p-dropdown [options]="CouponValues" [(ngModel)]="_CouponData.CouponValue" optionLabel="name"
                            id="CouponValue" optionValue="id" (ngModelChange)="updateOnCouponChange()"
                            formControlName="CouponValue"></p-dropdown>
                    </div>
                </div>


                <div class="col-3">
                    <div class="flex flex-column gap-2 mb-3">
                        <label for="originalPrice" [style.color]="CheckValid( mainInfoForm.controls.originalPrice)
                        ">Original Price</label>
                        <!-- <pre>{{ selectedCompanyCurrency }}</pre> -->
                        <!-- [suffix]="' ' + (selectedCompany.CompanyCountries && selectedCompany.CompanyCountries.length > 0) ? selectedCompany.CompanyCountries[0]?.Country.Currency : '$' + ' '" -->
                        <p-inputNumber [min]="0" [suffix]="' ' + selectedCompanyCurrency" [useGrouping]="false" id="originalPrice"
                            [(ngModel)]="_CouponData.OriginalPrice" (ngModelChange)="updateEstimatedSaving()"
                            formControlName="originalPrice" />
                    </div>
                </div>

                <div class="col-3">
                    <div class="flex flex-column gap-2 mb-3">
                        <label for="estimatedSavings" [style.color]=" CheckValid( mainInfoForm.controls.estimatedSavings )
                            ">Estimated savings</label>
                        <!-- <pre>{{ selectedCompanyCurrency }}</pre> -->
                        <!-- [suffix]="' ' + (selectedCompany.CompanyCountries && selectedCompany.CompanyCountries.length > 0) ? selectedCompany.CompanyCountries[0]?.Country.Currency : '$' + ' '" -->
                        <p-inputNumber [min]="0" [suffix]="' ' + selectedCompanyCurrency" [useGrouping]="false"
                            id="estimatedSavings" [(ngModel)]="_CouponData.EstimatedSaving"
                            (ngModelChange)="updateOriginalPrice()" formControlName="estimatedSavings" />
                    </div>
                </div>

            </div>

            <div class="row">
                <div class="col-3">
                    <div class="flex flex-column gap-2 mb-3">
                        <label for="protectionType" [style.color]=" CheckValid(mainInfoForm.controls.protectionType)
                            ">Protection type</label>
                        <p-dropdown [options]="protectionType" [(ngModel)]="_CouponData.ProtectionType"
                            optionLabel="name" id="protectionType" optionValue="id"
                            formControlName="protectionType"></p-dropdown>
                    </div>
                </div>
                <div class="col-3" *ngIf="_CouponData.ProtectionType == 1">
                    <div class="flex flex-column gap-2 mb-3">
                        <label for="SwipeCode">Swip Code</label>
                        <input pInputText id="SwipeCode" [(ngModel)]="_CouponData.SwipeCode"
                            [ngModelOptions]="{ standalone: true }" type="number" />
                    </div>
                </div>
            </div>
            <div class="flex flex-column gap-2 mb-3">
                <label for="conditions" [style.color]="CheckValid(mainInfoForm.controls.conditions)">Conditions</label>
                <p-multiSelect [options]="conditions" [(ngModel)]="_CouponData.CouponConditions"
                    defaultLabel="Select Coupon Conditions" optionLabel="EnName" display="chip" id="Conditions"
                    formControlName="conditions"></p-multiSelect>
            </div>
            <div class="flex flex-column gap-2 mb-3">
                <label for="terms" [style.color]="CheckValid(mainInfoForm.controls.EnTerms)">English Terms</label>
                <p-card role="region">
                    <textarea id="EnTerms" [(ngModel)]="_CouponData.EnTerms"
                        style="width: 100%; border: none; outline: none" formControlName="EnTerms">
                    </textarea>
                </p-card>
                <label for="terms" [style.color]="CheckValid(mainInfoForm.controls.ArTerms)">Arabic Terms</label>
                <p-card role="region">
                    <textarea id="ArTerms" [(ngModel)]="_CouponData.ArTerms"
                        style="width: 100%; border: none; outline: none" formControlName="ArTerms">
                </textarea>
                </p-card>
            </div>

            <div class="flex flex-column gap-2 mb-3">
                <label for="filters" [style.color]="CheckValid(mainInfoForm.controls.filters)">Filters</label>
                <p-multiSelect [options]="filters" [(ngModel)]="_CouponData.CouponFilters" defaultLabel="Select Coupon Filters"
                    optionLabel="EnName" display="chip" id="Filters" formControlName="filters"></p-multiSelect>
            </div>

            <div class="flex flex-column gap-2 mb-3">
                <label for="relatedBranches" [style.color]="
                        CheckValid(mainInfoForm.controls.relatedBranches)
                    ">Related branches</label>
                <p-multiSelect [options]="this.companyBranchs" [(ngModel)]="_CouponData.CouponBranches"
                    defaultLabel="Select related branches" optionLabel="EnName" display="chip" id="relatedBranches"
                    formControlName="relatedBranches"></p-multiSelect>
            </div>

            <div class="flex flex-column gap-2 mb-3">
                <div class="row">
                    <div class="col-12 row" formGroupName="startEndDates">
                        <div class="col-6">
                            <div class="flex flex-column gap-2 mb-3">
                                <label
                                    [style.color]="CheckValid(mainInfoForm.controls.startEndDates.controls.startDate)"
                                    for="StartDate">Start date</label>
                                <input type="date" pInputText id="StartDate" [(ngModel)]="_CouponData.StartDate"
                                    [min]="currentDate" [ngModel]="
                                        _CouponData.StartDate | date : 'yyyy-MM-dd'
                                    " class="w-50" formControlName="startDate" />
                            </div>
                        </div>

                        <div class="col-6">
                            <div class="flex flex-column gap-2 mb-3">
                                <label [style.color]="CheckValid(mainInfoForm.controls.startEndDates.controls.endDate)"
                                    for="EndDate">End date</label>
                                <input type="date" id="EndDate" pInputText [(ngModel)]="_CouponData.EndDate"
                                    [min]="currentDate" [ngModel]="
                                        _CouponData.EndDate | date : 'yyyy-MM-dd' " class="w-50"
                                    formControlName="endDate" />
                            </div>
                        </div>

                        <!-- <div class="col-12" *ngIf="!mainInfoForm.controls.startEndDates.valid"> -->
                        <div class="col-12"
                            *ngIf="mainInfoForm.controls.startEndDates.invalid && (this.pressedNext || mainInfoForm.controls.startEndDates.touched)">
                            <h6 [style.color]="CheckValid(mainInfoForm.controls.startEndDates)">End date must be
                                greater than start date!</h6>
                            <!-- <h6 [style.color]="CheckValid(mainInfoForm.controls.startEndDates)">Please enter valid start date and end date!</h6> -->
                        </div>
                    </div>

                </div>

            </div>

            <div class="flex flex-column gap-2 mb-3">

                <span class="flex flex-column gap-2 mb-3">
                    <label for="targetedUsers" [style.color]="
                            CheckValid(mainInfoForm.controls.targetedUsers)
                        ">targeted Users</label>
                    <input pInputText id="targetedUsers" [(ngModel)]="_CouponData.TargetedUserNum" class="w-50"
                        formControlName="targetedUsers"  type="number"/>
                </span>

            </div>
            <div class="flex justify-content-end gap-2 mb-3">
                <!-- [disabled]="
                        !this.mainInfoForm.valid || !this.imageUrl
                    " -->
                <p-button label="Save" (onClick)="save()"></p-button>
            </div>
        </div>
    </div>
</div>