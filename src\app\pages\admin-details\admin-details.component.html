<h3>Personal Info </h3>
<p-card class="justify-content-center">
      <div class="row d-flex justify-content-center">
            <div class="col-md-2" style="margin-right: 9%;">
                  <div class="text-center">
                        <div class="image-avatar-container">
                              <label for="image" class="w-100">
                                    <img src="{{getImageUrl( currentUser?.ProfilePhotoUrl)}}" alt="currentUser? Profile"
                                          class="p-mr-3" style="border-radius: 50%; width: 100px; height: 100px;" />
                              </label>
                        </div>
                        <br>
                        <button pButton label="Reset Password" class="p-button-rounded p-button-info mt-2"
                              (click)="displayresetPasswordModel = true">
                        </button>
                  </div>
            </div>

            <div class="col-md-8">
                  <div class="flex flex-column gap-3 mb-4">
                        <div class="field">
                              <label class="text-gray-600  fw-bold">Name</label>
                              <div class="text-gray-700">{{currentUser?.Name}}</div>
                        </div>

                        <div class="field">
                              <label class="text-gray-600  fw-bold">Email</label>
                              <div class="text-gray-700">{{currentUser?.Email}}</div>
                        </div>

                        <div class="field">
                              <label class="text-gray-600  fw-bold">Admin type</label>
                              <div class="text-gray-800">
                                    <p-tag severity="success" *ngIf="currentUser?.AdminType==1" styleClass="mr-3"
                                          icon="pi pi-user" value="{{currentUser?.AdminType | AdminType}}"></p-tag>
                                    <p-tag severity="warning" *ngIf="currentUser?.AdminType==0" styleClass="mr-3"
                                          icon="fas fa-user-shield"
                                          value="{{currentUser?.AdminType | AdminType}}"></p-tag>
                              </div>
                        </div>

                        <div class="field" *ngIf="currentUser?.AdminType!=0 && currentUser?.AdministeredCountries">
                              <label class="text-gray-600  fw-bold">Countries</label>
                              <div class="flex gap-2">
                                    <p-chip *ngFor="let country of currentUser?.AdministeredCountries"
                                          [label]="country.EnName">
                                    </p-chip>
                              </div>
                        </div>
                        <!-- userType: company Admin & adminType: Sub Admin  -->
                        <div class="field"
                              *ngIf="currentUser?.AdminType!=0 && user?.UserType==1 && currentUser?.AdministeredBranches">
                              <label class="text-gray-600  fw-bold">Branches</label>
                              <div class="flex gap-2">
                                    <p-chip *ngFor="let branch of currentUser?.AdministeredBranches"
                                          [label]="branch.EnName">
                                    </p-chip>
                              </div>
                        </div>
                        <!-- only for Website Admins -->
                        <div class="field" *ngIf="user?.UserType==0">
                              <label class="text-gray-600  fw-bold">Max Services Discount</label>
                              <div class="text-gray-700">{{currentUser?.MaxServiceDiscount}}% </div>
                        </div>

                        <div class="field">
                              <label class="text-gray-600  fw-bold">Last Login Time </label>
                              <div class="text-gray-700">
                                    <i class="pi pi-clock me-2"></i>
                                    {{currentUser?.LastLoginTime | date:'yyyy-MM-dd HH:mm:ss'}}
                              </div>
                        </div>
                  </div>
            </div>
      </div>
</p-card>
<p-dialog header="Reset Password" [(visible)]="displayresetPasswordModel" modal="modal" [style]="{width: '30vw'}"
      [breakpoints]="{'960px': '75vw'}" [draggable]="false" closable="true">

      <div class="d-flex gap-2 align-items-center mb-4 justify-content-center" [formGroup]="restPasswordForm">
            <label for="adminPassword">Admin password : </label>
            <p-password [toggleMask]="true" formControlName="newPassword" id="adminPassword"
                  placeholder="Admin Password" #newPasswordInput>
            </p-password>
      </div>
      <span *ngIf="restPasswordForm.get('newPassword')?.invalid && restPasswordForm.get('newPassword')?.touched"
            [style.color]="CheckValid(restPasswordForm.controls.newPassword)">
            Password must contain at least one lowercase letter, one uppercase letter, one digit, and one special
            character ($@$!%*?&), and be at least 8 characters long.
      </span>
      <ng-template pTemplate="footer">
            <button pButton icon="pi pi-check" [disabled]="restPasswordForm.invalid" (click)="resetPassword()"
                  label="Ok" class="p-button-outlined"></button>
      </ng-template>
</p-dialog>

<div class="loader" *ngIf="loading">
      <div class="loader-inner">
            <div class="loader-line-wrap">
                  <div class="loader-line"></div>
            </div>
            <div class="loader-line-wrap">
                  <div class="loader-line"></div>
            </div>
            <div class="loader-line-wrap">
                  <div class="loader-line"></div>
            </div>
            <div class="loader-line-wrap">
                  <div class="loader-line"></div>
            </div>
            <div class="loader-line-wrap">
                  <div class="loader-line"></div>
            </div>
      </div>