<div>
    <p-confirmDialog header="Confirmation" key="confirm1" icon="pi pi-exclamation-triangle"
        message="Are you sure you want to proceed?" [style]="{ width: '350px' }" acceptButtonStyleClass="p-button-text"
        rejectButtonStyleClass="p-button-text"></p-confirmDialog>
    <p-toast></p-toast>
    <!-- <p-table #dt [value]="discounts" dataKey="Id" [rowHover]="true" [rows]="10" [showCurrentPageReport]="true"
        responsiveLayout="scroll" [rowsPerPageOptions]="[10, 25, 50]" [loading]="loading" [paginator]="true"
        currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries" [filterDelay]="0"
        styleClass="p-datatable-gridlines "> -->
    <p-table #dt [value]="discounts" [paginator]="paginator" [rows]="pageSize" [totalRecords]="totalRecords"
        [lazy]="true" [loading]="loading" (onLazyLoad)="loadData($event)" [showCurrentPageReport]="true"
        [currentPageReportTemplate]="'Showing {first} to {last} of {totalRecords} records'" responsiveLayout="scroll"
        [rowHover]="true" styleClass="p-datatable-gridlines">
        <ng-template pTemplate="caption">
            <app-grid-headers [myDt]="dt" addNewTxt="Add New Discount" goRoute="/discount-new" gridTitle="Discounts"
                [title]="title" (SearchEvent)="ReceivedFilteredData($event)"></app-grid-headers>
            <!-- <app-discount-grid-headers [title]="title" goRoute="//discount-new"
                (SearchEvent)="ReceivedFilteredData($event)"> -->
            <!-- </app-discount-grid-headers> -->
        </ng-template>
        <ng-template pTemplate="header">
            <tr>
                <th>
                    <div class="flex justify-content-between align-items-center">
                        {{ "ID" | translate }}
                    </div>
                </th>
                <th *ngIf="user.UserType == 0">
                    <div class="flex justify-content-between align-items-center">
                        {{ "Company" | translate }}
                    </div>
                </th>
                <th>
                    <div class="flex justify-content-between align-items-center">
                        {{ "Title" | translate }}
                    </div>
                </th>
                <th>
                    <div class="flex justify-content-between align-items-center">
                        {{ "Type" | translate }}
                        <!-- <p-columnFilter type="text" field="DiscountType" display="menu"
                            class="ml-auto"></p-columnFilter> -->
                    </div>
                </th>
                <th>
                    <div class="flex justify-content-between align-items-center">
                        {{ "Period" | translate }}
                    </div>
                </th>
                <th>
                    <div class="flex justify-content-between align-items-center">
                        {{ "Start Date" | translate }}
                    </div>
                </th>
                <th>
                    <div class="flex justify-content-between align-items-center">
                        {{ "End Date" | translate }}
                    </div>
                </th>
                <th>
                    <div class="flex justify-content-between align-items-center">
                        {{ "Updated Date" | translate }}
                    </div>
                </th>
                <th>
                    <div class="flex justify-content-between align-items-center">
                        {{ "Users" | translate }}
                    </div>
                </th>
                <!-- <th pSortableColumn="lastAction">
                    <div class="flex justify-content-between align-items-center">
                        {{ "Last Action" | translate }}
                        <p-sortIcon field="lastAction"></p-sortIcon>
                    </div>
                </th> -->

                <th>
                    <div class="flex justify-content-between align-items-center">
                        {{ "Status" | translate }}
                    </div>
                </th>
                <th>
                    <div class="flex justify-content-between align-items-center">
                        {{ "Edit" | translate }}
                    </div>
                </th>
                <!-- <th style="width: 5rem"></th> -->
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-disc class="flex justify-content-between align-items-center">
            <!-- {{ disc | json }} -->
            <tr  [ngClass]="{'inactive-table-row': disc.Active === false}">
                <td>

                    <div class="d-flex align-items-center">
                        <p-inputSwitch *ngIf="canActiveDiscount(disc)" [(ngModel)]="disc.Active"
                            (onChange)="changeDiscountLastActionLog(disc.Id, disc.Active)"></p-inputSwitch>
                        <span class="ms-2">{{ disc.UserFriendly }}</span>
                        <br />
                    </div>
                    <i class="pi pi-info-circle" (click)="showDialog(disc)"></i>

                </td>
                <td *ngIf="user.UserType == 0">
                    <span class="p-column-title">Company</span>
                    <p-avatar *ngIf="disc.Company.LogoUrl" image="{{imageSrc + disc.Company.LogoUrl}}" styleClass="mr-2"
                        size="medium" shape="circle"></p-avatar>
                    <!-- <p-avatar *ngIf="disc.Company.LogoUrl" image="{{imageSrc + disc.Company.LogoUrl.split('wwwroot\\')[1]}}"  styleClass="mr-2" size="xlarge" shape="circle"></p-avatar> -->
                    <!-- <p-avatar ref="noLogoCompany" [label]="disc.Company.EnName[0]" image="https://primefaces.org/cdn/primeng/images/demo/avatar/amyelsner.png" styleClass="mr-2" size="large" [style]="{ 'background-color': '#2196F3', color: '#ffffff' }" shape="circle"></p-avatar> -->
                    {{ disc.Company.EnName }}
                </td>

                <td>
                    <span class="p-column-title">Title</span>
                    <p-avatar *ngIf="disc.LogoUrl" image="{{imageSrc + disc.LogoUrl}}" styleClass="mr-2" size="medium"
                        shape="circle"></p-avatar>
                    {{ disc.EnTitle }}
                </td>
                <td>
                    <p-chip [label]="   disc.DiscountType | discountType "></p-chip>
                    <!-- <span class="p-column-title">Discount Type</span>
                    {{ disc.DiscountType | discountType }} -->
                </td>
                <td>
                    <span class="p-column-title">Discount Period</span>
                    {{ disc.DiscountPeriod | discountPeriod }}
                </td>
                <td>
                    <span class="p-column-title">Start Date</span>
                    {{ disc.StartDate | date : "dd/MM/yyyy"}}
                </td>
                <td>
                    <span class="p-column-title">End Date</span>
                    {{ disc.EndDate | date : "dd/MM/yyyy"}}
                </td>
                <td>
                    <span class="p-column-title">Updated Date</span>
                    {{ disc.UpdatedDate | date : "dd/MM/yyyy"}}
                </td>
                <td>
                    <div *ngIf="disc.DiscountType ===0; else groupContent">

                        <ng-container *ngIf="disc.IsNormalTier ===true">
                            <span *ngFor="let tier of disc.NormalTiersValues">
                                <p-avatar image="assets/pages/icons/{{tier.Tier.EnName}}.png" styleClass="mr-2"
                                    size="xsmall" shape="circle" alt="{{tier.Tier.EnName}}"></p-avatar>

                                <span *ngIf="tier.UnlimitedOffers==false">{{tier.OffersNum }} Offers</span>
                                <span *ngIf="tier.UnlimitedOffers==true">&#8734; Offers</span>

                                <img *ngIf="tier.RenewabilityType ==1" src="assets/pages/icons/renewable-energy.png"
                                    class="small-icon" alt="Monthly Renewable" title="Monthly Renewable">

                                <br>
                                <br>
                            </span>
                        </ng-container>
                        <ng-container *ngIf="disc.IsNormalTier ===false">
                            <span *ngFor="let tier of disc.VipTiersValues ">
                                <p-avatar image="assets/pages/icons/{{tier.Tier.EnName}}.png" styleClass="mr-2"
                                    size="xsmall" shape="rectangle" alt="{{tier.Tier.EnName}}"></p-avatar>
                                <span *ngIf="tier.UnlimitedOffers==false">{{tier.OffersNum }} Offers</span>
                                <span *ngIf="tier.UnlimitedOffers==true">&#8734; Offers</span>
                            </span>
                        </ng-container>

                    </div>
                    <ng-template #groupContent>
                        <span *ngIf=" disc.groupInfo">
                            {{ disc.groupInfo.MinPeople }} - {{disc.groupInfo.MaxPeople}} People <br>
                            {{disc.groupInfo.OfferPerUser}} offers
                            <img *ngIf="disc.groupInfo.RenewabilityType ==1"
                                src="assets/pages/icons/renewable-energy.png" class="small-icon" alt="Monthly Renewable"
                                title="Monthly Renewable">
                        </span>
                    </ng-template>


                </td>

                <!-- <td style="min-width: 12rem;">

                    <span [ngClass]="iconForLastActionLog(disc.DiscountStatuses[0].Status)" class="me-2"
                        [ngStyle]="colorOfLastActionLog(disc.DiscountStatuses[0].Status)"> </span>

                    <span
                        [class]="'discount-action action-' + disc.DiscountStatuses[0].Status">{{ActionLogEnumName(disc.DiscountStatuses[0].Status)}}</span><br>
                    <span *ngIf="disc.DiscountStatuses[0].Admin?.Name!=null"> by
                        {{disc.DiscountStatuses[0].Admin?.Name}} </span>
                </td> -->

                <td style="min-width: 12rem;">

                    <span class="me-2" [ngStyle]="colorOfStatus(disc.Status)"> </span>

                    <span [class]="'discount-status status-' + disc.Status">{{StatusName(disc.Status)}}</span>
                <td>
                    <span class="p-column-title">Edit</span>
                    <div class="d-flex align-items-center justify-content-between">

                        <p-button [disabled]="!disc.Active" *ngIf="canEditDiscount(disc)" icon="pi pi-pencil"
                            styleClass="p-button-rounded" class="mx-1"
                            (click)="edit({discount: disc, state: 'edit'})"></p-button>

                        <p-button *ngIf="user.UserType == 0" icon="pi pi-trash"
                            styleClass="p-button-rounded p-button-danger" class="mx-1"
                            (click)="Delete(disc.Id)"></p-button>

                        <p-button *ngIf="user.UserType == 0" icon="pi pi-wrench" styleClass="p-button-rounded"
                            (click)="rejectOrAccept(disc)" class="mx-1"></p-button>
                        <p-button *ngIf="user.UserType == 1" icon="pi pi-eye" styleClass="p-button-rounded"
                            (click)="rejectOrAccept(disc)" class="mx-1"></p-button>
                    </div>
                </td>
            </tr>
            <p-dialog header="Discount History" [(visible)]="disc.VisibleDialog" [breakpoints]="{ '960px': '75vw' }"
                [style]="{ width: '50vw' }" [draggable]="false" [resizable]="true" [id]="disc.Id">
                <table>
                    <tr *ngFor="let item of disc.DiscountStatuses">
                        <th>
                            <i [ngClass]="iconForLastActionLog(item!.Status)" class="me-2"
                                [ngStyle]="colorOfLastActionLog(item!.Status)"></i>
                        </th>
                        <th>
                            <span [class]="'discount-action action- action-' + item.Status">
                                {{ item.Status | discountStatus }}
                            </span>
                        </th>
                        &nbsp;&nbsp;&nbsp;
                        <th>
                            {{ item.Admin?.Name }}
                        </th>
                        &nbsp;&nbsp;&nbsp;
                        <th>
                            {{
                            item.CreationDate
                            | localTime
                            }}
                            <!-- {{
                        item.CreationDate.toString()
                        | date : "dd/MM/yyyy - hh:mm"
                        }} -->
                        </th>
                        &nbsp;&nbsp;&nbsp;
                        <th>
                            {{ item.Note }}
                        </th>
                    </tr>
                </table>
            </p-dialog>
        </ng-template>
        <ng-template pTemplate="emptymessage">
            <tr>
                <td class="text-center" colspan="7">
                    {{ "No Discount found." | translate }}
                </td>
            </tr>
        </ng-template>
    </p-table>
</div>