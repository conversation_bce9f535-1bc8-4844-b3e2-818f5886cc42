<div>
    <p-confirmDialog header="Confirmation" key="confirm1" icon="pi pi-exclamation-triangle"
        message="Are you sure you want to proceed?" [style]="{ width: '350px' }" acceptButtonStyleClass="p-button-text"
        rejectButtonStyleClass="p-button-text"></p-confirmDialog>
    <p-toast></p-toast>
    <p-table #dt [value]="BroadcastMessages" [paginator]="paginator" [rows]="pageSize" [totalRecords]="totalRecords"
        [lazy]="true" [loading]="loading" (onLazyLoad)="loadData($event)" [showCurrentPageReport]="true"
        [currentPageReportTemplate]="'Showing {first} to {last} of {totalRecords} records'">

        <ng-template pTemplate="caption">
            <app-grid-headers [myDt]="dt" addNewTxt="Add New Broadcast Message" goRoute="/broadcast-message-new"
                gridTitle="Broadcast Messages" [title]="title"
                (SearchEvent)="ReceivedFilteredData($event)"></app-grid-headers>
        </ng-template>

        <ng-template pTemplate="header">
            <tr>
                <th>
                    <div class="flex justify-content-between align-items-center">
                        {{ "Ad ID" | translate }}
                    </div>
                </th>
                <th *ngIf="user.UserType == 0">
                    <div class="flex justify-content-between align-items-center">
                        {{ "Company" | translate }}
                    </div>
                </th>
                <th>
                    <div class="flex justify-content-between align-items-center">
                        {{ "Title" | translate }}
                    </div>
                </th>
                <th>
                    <div class="flex justify-content-between align-items-center">
                        {{ "Status" | translate }}
                    </div>
                </th>
                <th>
                    <div class="flex justify-content-between align-items-center">
                        {{ "Type" | translate }}
                    </div>
                </th>
                <th>
                    <div class="flex justify-content-between align-items-center">
                        {{ "Launch Date" | translate }}
                    </div>
                </th>

                <th>
                    <div class="flex justify-content-between align-items-center">
                        {{ "Updated Date" | translate }}
                    </div>
                </th>
                <th>
                    <div class="flex justify-content-between align-items-center">
                        {{ "Edit" | translate }}
                    </div>
                </th>
                <!-- <th style="width: 5rem"></th> -->
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-BroadcastMessage>
            <tr  [ngClass]="{'inactive-table-row': BroadcastMessage.Active === false}">

                <td>
                    <span class="p-column-title" style="text-align: center">ID</span>
                    <div class="d-flex align-items-center">
                        <p-inputSwitch *ngIf="canActiveBroadcastMessage(BroadcastMessage)"
                            [(ngModel)]="BroadcastMessage.Active"
                            (onChange)="changeBroadcastMessageLastActionLog(BroadcastMessage.Id, BroadcastMessage.Active)"></p-inputSwitch>
                        <span class="ms-2">{{ BroadcastMessage.UserFriendly }}</span>
                        <br />
                    </div>
                    <i class="pi pi-info-circle" (click)="showDialog(BroadcastMessage)"></i>
                </td>

                <td *ngIf="user.UserType == 0">
                    <div class="flex">
                        <p-avatar *ngIf="BroadcastMessage.Company.LogoUrl"
                            image="{{imageSrc + BroadcastMessage.Company.LogoUrl}}" styleClass="mr-2" size="medium"
                            shape="circle"></p-avatar>
                        <!-- <p-avatar *ngIf="BroadcastMessages.Company.LogoUrl" image="{{imageSrc + BroadcastMessages.Company.LogoUrl.split('wwwroot\\')[1]}}"  styleClass="mr-2" size="xlarge" shape="circle"></p-avatar> -->
                        <!-- <p-avatar ref="noLogoCompany" [label]="BroadcastMessages.Company.EnName[0]" image="https://primefaces.org/cdn/primeng/images/demo/avatar/amyelsner.png" styleClass="mr-2" size="large" [style]="{ 'background-color': '#2196F3', color: '#ffffff' }" shape="circle"></p-avatar> -->
                        {{ BroadcastMessage.Company.EnName }}
                    </div>
                </td>

                <td>
                    <div class="flex">
                        <p-avatar *ngIf="BroadcastMessage.ImageUrl" image="{{imageSrc + BroadcastMessage.ImageUrl}}"
                            styleClass="mr-2" size="medium" shape="circle"></p-avatar>
                        {{ BroadcastMessage.EnTitle }}
                    </div>
                </td>

                <td style="min-width: 12rem;">
                    <div class="flex">
                        <span class="me-2" [ngStyle]="colorOfStatus(BroadcastMessage.Status)"> </span>

                        <span
                            [class]="'broadcast-message-status status-' + BroadcastMessage.Status">{{StatusName(BroadcastMessage.Status)}}</span>
                    </div>
                </td>

                <td style="min-width: 12rem;">
                    <div class="flex">
                        <p-chip [label]="transformedTypeLabel(BroadcastMessage.Type)"></p-chip>
                    </div>
                </td>

                <td>
                    <div *ngIf="!BroadcastMessage.SendAsSoonAsPossible ; else notSend">
                        <!-- <span class="p-column-title">Launch Date</span> -->
                        {{ BroadcastMessage.LaunchDate | date : "dd/MM/yyyy"}}
                    </div>
                    <ng-template #notSend>
                        <span
                            style="background: #f69ebc; color: #801136; border-radius: var(--border-radius);  padding: 0.25em 0.5rem;  font-weight: 800; font-size: 15px;letter-spacing: 0.3px;">ASAP</span>
                    </ng-template>

                </td>

                <td>
                    <span class="p-column-title">Updated Date</span>
                    {{ BroadcastMessage.UpdatedDate | date : "dd/MM/yyyy"}}
                </td>

                <td>
                    <span class="p-column-title">Edit</span>
                    <div class="d-flex align-items-center justify-content-between">

                        <p-button [disabled]="!BroadcastMessage.Active"
                            *ngIf="canEditBroadcastMessage(BroadcastMessage)" icon="pi pi-pencil"
                            styleClass="p-button-rounded" class="mx-1"
                            (click)="edit({BroadcastMessage: BroadcastMessage, state: 'edit'})"></p-button>

                        <p-button *ngIf="user.UserType == 0" icon="pi pi-trash"
                            styleClass="p-button-rounded p-button-danger" class="mx-1"
                            (click)="Delete(BroadcastMessage.Id)"></p-button>

                        <p-button *ngIf="user.UserType == 0 " icon="pi pi-wrench" styleClass="p-button-rounded"
                            (click)="rejectOrAccept(BroadcastMessage)" class="mx-1"></p-button>
                        <p-button *ngIf="user.UserType == 1 " icon="pi pi-eye" styleClass="p-button-rounded"
                            (click)="rejectOrAccept(BroadcastMessage)" class="mx-1"></p-button>
                    </div>
                </td>
            </tr>
            <p-dialog header="Broadcast Message History" [(visible)]="BroadcastMessage.VisibleDialog"
                [breakpoints]="{ '960px': '75vw' }" [style]="{ width: '50vw' }" [draggable]="false" [resizable]="true"
                [id]="BroadcastMessage.Id">
                <table>
                    <tr *ngFor="let item of BroadcastMessage.BroadcastMessageLog">
                        <th>
                            <i [ngClass]="iconForLastActionLog(item!.Status)" class="me-2"
                                [ngStyle]="colorOfLastActionLog(item!.Status)"></i>
                        </th>
                        <th>
                            <span [class]="'broadcast-message-action action-' + item.Status">
                                {{ item.Status | BroadcastMessageLog }}
                            </span>

                        </th>
                        &nbsp;&nbsp;&nbsp;
                        <th>
                            {{ item.Admin?.Name }}
                        </th>
                        &nbsp;&nbsp;&nbsp;
                        <th>
                            {{
                            item.CreationDate
                            | localTime
                            }}
                            <!-- {{
                        item.CreationDate.toString()
                        | date : "dd/MM/yyyy - hh:mm"
                        }} -->
                        </th>
                        &nbsp;&nbsp;&nbsp;
                        <th>
                            {{ item.Note }}
                        </th>
                    </tr>
                </table>
            </p-dialog>
        </ng-template>
        <ng-template pTemplate="emptymessage">
            <tr>
                <td class="text-center" colspan="10">
                    {{ "No Broadcast Messages found." | translate }}
                </td>
            </tr>
        </ng-template>
    </p-table>
</div>