// import { FormControl, ValidationErrors, ValidatorFn } from "@angular/forms";

// export const urlValidator: ValidatorFn = (control: FormControl): ValidationErrors | null => {
//     let pattern = new RegExp('^(https?:\\/\\/)?'+ // protocol
//         '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|'+ // domain name
//         '((\\d{1,3}\\.){3}\\d{1,3}))'+ // OR ip (v4) address
//         '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*'+ // port and path
//         '(\\?[;&a-z\\d%_.~+=-]*)?'+ // query string
//         '(\\#[-a-z\\d_]*)?$','i'); // fragment locator
//     return !!pattern.test(control.value) || control.value === '' ? null : { urlInvalid: true };
// }

import { FormControl, ValidationErrors, ValidatorFn } from "@angular/forms";

export function urlValidator(type: string = ''): ValidatorFn {
    return (control: FormControl): ValidationErrors | null => {
        switch (type) {
            case 'pdf':
                let pdfPattern = new RegExp('^(https?:\\/\\/)?' + // protocol
                    '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|' + // domain name
                    '((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
                    '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*' + // port and path
                    '.pdf$', 'i'); // ends with .pdf extension
                return !!pdfPattern.test(control.value) || control.value === '' ? null : { urlInvalid: true };

            // case 'gmaps':
            //     let gmapsPattern = new RegExp('^https?\:\/\/(www\.|maps\.)?google(\.[a-z]+){1,2}\/maps\/?\?([^&]+&)*(ll=-?[0-9]{1,2}\.[0-9]+,-?[0-9]{1,2}\.[0-9]+|q=[^&]+)+($|&)', 'i')
            //     return !!gmapsPattern.test(control.value) || control.value === '' ? null : { urlInvalid: true };

            default:
                let pattern = new RegExp('^(https?:\\/\\/)?' + // protocol
                    '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|' + // domain name
                    '((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
                    '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*' + // port and path
                    '(\\?[;&a-z\\d%_.~+=-]*)?' + // query string
                    '(\\#[-a-z\\d_]*)?$', 'i'); // fragment locator
                return !!pattern.test(control.value) || control.value === '' ? null : { urlInvalid: true };
        }
    }
}
