import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { AdminType } from 'src/app/enum/admin-type';
import { Gender } from 'src/app/enum/gender';
import { CompanyModel } from 'src/app/Model/CompanyModel';
import { Country } from 'src/app/Model/Country';
import { TiersModel } from 'src/app/Model/TiersModel';
import { CompanyIndustryService } from 'src/app/services/company-industry.service';
import { CompanyService } from 'src/app/services/company.service';
import { CountryService } from 'src/app/services/country.service';
import { TierService } from 'src/app/services/tier.service';
import { Countries } from 'src/app/utilities/countries';

@Component({
  selector: 'app-grid-item-input-dropdown',
  templateUrl: './grid-item-input-dropdown.component.html',
  styleUrls: ['./grid-item-input-dropdown.component.scss']
})
export class GridItemInputDropdownComponent implements OnInit {
  public industries: { Name: any; icon: string; Code: any; }[] = [];

  countries: Country[];
  companies: CompanyModel[];
  genders = [];
  tiers: TiersModel[];
  adminTypes = [];
  allCountries: Countries = new Countries();

  @Output() selectedIndustryEventEmitter: EventEmitter<{ Name: any; icon: string; Code: any; }> = new EventEmitter();
  @Output() selectedGenderEventEmitter: EventEmitter<Gender> = new EventEmitter();
  @Output() selectedCountryEventEmitter: EventEmitter<{ Country }> = new EventEmitter();
  @Output() selectedTierEventEmitter: EventEmitter<{ TiersModel }> = new EventEmitter();
  @Output() selectedAdminTypeEventEmitter: EventEmitter<AdminType> = new EventEmitter();
  @Output() selectedCompanyEventEmitter: EventEmitter<{ CompanyModel }> = new EventEmitter();

  @Input() label: string = "";
  @Input() icon: string = "";
  @Input() placeholder: string;
  @Input() listType: string;
  //: { [key: string]: Object; }[] 

  constructor(private companyIndustryService: CompanyIndustryService, private countryService: CountryService, private tierService: TierService, private companyService: CompanyService) {

  }

  ngOnInit(): void {
    // console.log('listType', this.listType)
    if (this.listType == 'Industry') {
      this.companyIndustryService.getCompanyIndustries()
        .subscribe(mappedResponse => {
          this.industries = mappedResponse;
        });
    }
    else if (this.listType == 'Gender') {
      this.genders =
        Object.keys(Gender)
          .filter(key => isNaN(Number(key)))
          .map(key => ({
            label: key,
            value: Gender[key]
          }));
      this.genders = [{ label: '', value: null }, ... this.genders,];
    }
    else if (this.listType == 'AdminType') {
      this.adminTypes =
        Object.keys(AdminType)
          .filter(key => isNaN(Number(key)))
          .map(key => ({
            label: key,
            value: AdminType[key]
          }));
      this.adminTypes = [{ label: '', value: null }, ... this.adminTypes];
    }
    else if (this.listType == 'Country') {
      this.countryService.Countries.subscribe((data) => {
        this.countries = data;
        console.log('countries', this.countries)
      });
    }
    else if (this.listType == 'Company') {
      this.companyService.GetAllId().subscribe((data) => {
        this.companies = data;
      });
    }
    else if (this.listType == 'Tier') {
      this.tierService.getAllTiers().subscribe((data) => {
        this.tiers = data;
      });
    }
  }

  onIndustrySelection(event) {
    this.selectedIndustryEventEmitter.emit(event.value);
  }
  onGenderSelection(event) {
    this.selectedGenderEventEmitter.emit(event.value);
  }
  onAdminTypeSelection(event) {
    this.selectedAdminTypeEventEmitter.emit(event.value);
  }
  onCountrySelection(event) {
    this.selectedCountryEventEmitter.emit(event.value);
  }
  onCompanySelection(event) {
    this.selectedCompanyEventEmitter.emit(event.value);
  }
  onTierSelection(event) {
    this.selectedTierEventEmitter.emit(event.value);
  }
}
