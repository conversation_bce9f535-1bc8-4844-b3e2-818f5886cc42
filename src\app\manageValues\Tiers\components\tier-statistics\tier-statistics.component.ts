import { Component, EventEmitter, Input, Output } from '@angular/core';
import { MessageService } from 'primeng/api';
import { TiersModel } from 'src/app/Model/TiersModel';
import { Countries } from 'src/app/utilities/countries';

@Component({
  selector: 'tier-statistics',
  templateUrl: './tier-statistics.component.html',
  styleUrls: ['./tier-statistics.component.scss'],

})
export class TierStatisticsComponent {
  @Input() _TierData: TiersModel;
  @Input() isEditing: boolean;
  @Input() activeIndex: number;
  @Output() activeIndexChange: EventEmitter<number> = new EventEmitter();
  pressedNext = false;
  tierDataArray: any[] = [];
  allCountries: Countries = new Countries();
  constructor(private messageService: MessageService) {
  }

  ngOnInit(): void {
    // Convert the object to an array with key-value pairs
    this.tierDataArray = [
      { TotalCountries: this._TierData.TotalCountries, TotalUsers: this._TierData.TotalUsers }
    ];
  }

}
