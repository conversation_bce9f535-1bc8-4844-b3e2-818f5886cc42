import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HTTP_INTERCEPTORS, HttpClient, HttpClientModule } from '@angular/common/http';
import { BrowserModule } from '@angular/platform-browser';
import { CommonModule, DatePipe, HashLocationStrategy, LocationStrategy } from '@angular/common';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { AppRoutingModule } from './app-routing.module';
import { AccordionModule } from "primeng/accordion";
import { AutoCompleteModule } from "primeng/autocomplete";
import { AvatarModule } from "primeng/avatar";
import { AvatarGroupModule } from "primeng/avatargroup";
import { BadgeModule } from "primeng/badge";
import { BreadcrumbModule } from "primeng/breadcrumb";
import { ButtonModule } from "primeng/button";
import { CalendarModule } from "primeng/calendar";
import { CardModule } from "primeng/card";
import { CarouselModule } from "primeng/carousel";
import { CascadeSelectModule } from "primeng/cascadeselect";
import { ChartModule } from "primeng/chart";
import { CheckboxModule } from "primeng/checkbox";
import { ChipModule } from "primeng/chip";
import { ChipsModule } from "primeng/chips";
import { CodeHighlighterModule } from "primeng/codehighlighter";
import { ConfirmDialogModule } from "primeng/confirmdialog";
import { ConfirmPopupModule } from "primeng/confirmpopup";
import { ColorPickerModule } from "primeng/colorpicker";
import { ContextMenuModule } from "primeng/contextmenu";
import { DataViewModule } from "primeng/dataview";
import { DialogModule } from "primeng/dialog";
import { DividerModule } from "primeng/divider";
import { DropdownModule } from "primeng/dropdown";
import { FieldsetModule } from "primeng/fieldset";
import { FileUploadModule } from "primeng/fileupload";
import { GalleriaModule } from "primeng/galleria";
import { ImageModule } from "primeng/image";
import { InplaceModule } from "primeng/inplace";
import { InputNumberModule } from "primeng/inputnumber";
import { InputMaskModule } from "primeng/inputmask";
import { InputSwitchModule } from "primeng/inputswitch";
import { InputTextModule } from "primeng/inputtext";
import { InputTextareaModule } from "primeng/inputtextarea";
import { KnobModule } from "primeng/knob";
import { LightboxModule } from "primeng/lightbox";
import { ListboxModule } from "primeng/listbox";
import { MegaMenuModule } from "primeng/megamenu";
import { MenuModule } from "primeng/menu";
import { MenubarModule } from "primeng/menubar";
import { MessagesModule } from "primeng/messages";
import { MessageModule } from "primeng/message";
import { MultiSelectModule } from "primeng/multiselect";
import { OrderListModule } from "primeng/orderlist";
import { OrganizationChartModule } from "primeng/organizationchart";
import { OverlayPanelModule } from "primeng/overlaypanel";
import { PaginatorModule } from "primeng/paginator";
import { PanelModule } from "primeng/panel";
import { PanelMenuModule } from "primeng/panelmenu";
import { PasswordModule } from "primeng/password";
import { PickListModule } from "primeng/picklist";
import { ProgressBarModule } from "primeng/progressbar";
import { RadioButtonModule } from "primeng/radiobutton";
import { RatingModule } from "primeng/rating";
import { RippleModule } from "primeng/ripple";
import { ScrollPanelModule } from "primeng/scrollpanel";
import { ScrollTopModule } from "primeng/scrolltop";
import { SelectButtonModule } from "primeng/selectbutton";
import { SidebarModule } from "primeng/sidebar";
import { SkeletonModule } from "primeng/skeleton";
import { SlideMenuModule } from "primeng/slidemenu";
import { SliderModule } from "primeng/slider";
import { SplitButtonModule } from "primeng/splitbutton";
import { SplitterModule } from "primeng/splitter";
import { StepsModule } from "primeng/steps";
import { TabMenuModule } from "primeng/tabmenu";
import { TableModule } from "primeng/table";
import { TabViewModule } from "primeng/tabview";
import { TagModule } from "primeng/tag";
import { TerminalModule } from "primeng/terminal";
import { TieredMenuModule } from "primeng/tieredmenu";
import { TimelineModule } from "primeng/timeline";
import { ToastModule } from "primeng/toast";
import { ToggleButtonModule } from "primeng/togglebutton";
import { ToolbarModule } from "primeng/toolbar";
import { TooltipModule } from "primeng/tooltip";
import { TreeModule } from "primeng/tree";
import { TreeTableModule } from "primeng/treetable";
import { VirtualScrollerModule } from "primeng/virtualscroller";
import { FullCalendarModule } from "@fullcalendar/angular";
import { AppComponent } from "./app.component";
import { AppMainComponent } from "./app.main.component";
import { AppConfigComponent } from "./app.config.component";
import { AppMenuComponent } from "./app.menu.component";
import { AppMenuitemComponent } from "./app.menuitem.component";
import { AppRightPanelComponent } from "./app.rightpanel.component";
import { AppBreadcrumbComponent } from "./app.breadcrumb.component";
import { AppTopBarComponent } from "./app.topbar.component";
import { AppFooterComponent } from "./app.footer.component";

import { AppNotfoundComponent } from "./pages/app.notfound.component";
import { AppErrorComponent } from "./pages/app.error.component";
import { AppAccessdeniedComponent } from "./pages/app.accessdenied.component";
import { AppLoginComponent } from "./pages/app.login.component";
import { ConfigService } from "./demo/service/app.config.service";
import { MenuService } from "./app.menu.service";
import { AppBreadcrumbService } from "./app.breadcrumb.service";
import { SidemenuComponent } from "./sidemenu/sidemenu.component";
import { TranslateLoader, TranslateModule } from "@ngx-translate/core";
import { TranslateHttpLoader } from "@ngx-translate/http-loader";
import { ToastrModule } from "ngx-toastr";
import { CompanylistComponent } from "./company/companylist/companylist.component";
import { DiscountlistComponent } from './discount/discountlist/discountlist.component';
import { DiscountnewComponent } from './discount/discountnew/discountnew.component';
import { MainInfoComponent } from './discount/components/main-info/main-info.component';
import { TimingDetailsComponent } from './discount/components/timing-details/timing-details.component';
import { GridHeadersComponent } from './components/grid-headers/grid-headers.component';
import { CompanyNewComponent } from './company/company-new/company-new.component';
import { OverviewComponent } from './company/components/overview/overview.component';
import { BranchesComponent } from './company/components/branches/branches.component';
import { ServicesListComponent } from './company/components/services-list/services-list.component';
import { SubscriptionsComponent } from './company/components/subscriptions/subscriptions.component';
import { GridItemButtonComponent } from './components/grid-headers/grid-item-button/grid-item-button.component';
import { GridItemInputDropdownComponent } from './components/grid-headers/grid-item-input-dropdown/grid-item-input-dropdown.component';
import { GridItemInputTextComponent } from './components/grid-headers/grid-item-input-text/grid-item-input-text.component';
import { TranslatePipeDB } from './pipe/translate-db.pipe';
import { OpenHoursComponent } from './company/components/open-hours/open-hours.component';
import { DiscountTypePipe } from './pipe/discount-type.pipe';
import { DiscountPeriodPipe } from './pipe/discount-period.pipe';
import { DicountApprovmentComponent } from './discount/dicount-approvment/dicount-approvment.component';
import { DiscountStatusPipe } from './pipe/discount-status.pipe';
import { DiscountValuePipe } from './pipe/discount-value.pipe';
import { ProtectionTypePipe } from './pipe/protection-type.pipe';
import { RenewabilityTypePipe } from './pipe/renewbility-type.pipe';
import { PlaceholderPipe } from './pipe/placeholder.pipe';
import { IndustryNewComponent } from './manageValues/industry/industry-new/industry-new.component';
import { IndustriesListComponent } from './manageValues/industry/industries-list/industries-list.component';
import { ErrorInterceptor } from './interceptor/http-error.interceptor';
import { MallNewComponent } from './manageValues/malls/mall-new/mall-new.component';
import { MallsListComponent } from './manageValues/malls/malls-list/malls-list.component';
import { LocalTimePipe } from './pipe/LocalTimePipe';
import { ConfirmationDialogComponent } from './components/confirmation-dialog/confirmation-dialog.component';
import { CouponNewComponent } from './coupon/coupon-new/coupon-new.component';
import { CouponPreviewComponent } from './coupon/coupon-preview/coupon-preview.component';
import { CouponlistComponent } from './coupon/coupon-list/coupon-list.component';
import { CouponLogPipe } from './pipe/coupon-log.pipe';
import { CouponValuePipe } from './pipe/coupon-value.pipe';
import { CouponMainInfoComponent } from './coupon/components/main-info/coupon-main-info.component';
import { BroadcastMessagesListComponent } from './broadcastMessages/broadcast-messages-list/broadcast-messages-list.component';
import { BroadcastMessageMainInfoComponent } from './broadcastMessages/components/broadcast-messages-main-info/broadcast-message-main-info.component';
import { BroadcastMessageNewComponent } from './broadcastMessages/broadcast-message-new/broadcast-message-new.component';
import { BroadcastMessagePreviewComponent } from './broadcastMessages/broadcast-message-preview/broadcast-message-preview.component';
import { BroadcastMessageLogPipe } from './pipe/broadcast-message-log.pipe';
import { BroadcastMessageTypePipe } from './pipe/broadcast-message-type.pipe';
import { BroadcastMessageTimingTargetedUserComponentComponent } from './broadcastMessages/components/broadcast-message-timing-targeted-user-component/broadcast-message-timing-targeted-user-component.component';
import { GenderPipe } from './pipe/gender.pipe';
import { AmPmTimePipe } from './pipe/am-pm-time-pipe';
import { ApiTokenInterceptor } from './interceptor/api-token.interceptor';
import { UserTypePipe } from './pipe/userType.pipe';
import { CountrylistComponent } from './manageValues/country/countryList/countryList.component';
import { CountryNewComponent } from './manageValues/country/country-new/country-new.component';
import { CountryMainInfoComponent } from './manageValues/country/components/main-info/country-main-info.component';
import { CountryCitiesComponent } from './manageValues/country/components/country-cities/country-cities.component';
import { CountryAvailableCategoriesComponent } from './manageValues/country/components/country-available-categories/country-available-categories.component';
import { CountryAvailableTiersComponent } from './manageValues/country/components/country-available-tiers/country-available-tiers.component';
import { CountrySubscriptionsPricesComponent } from './manageValues/country/components/country-subscriptions-prices/country-subscriptions-prices.component';
import { TiersListComponent } from './manageValues/Tiers/tiers-list/tiers-list.component';
import { TierNewComponent } from './manageValues/Tiers/tier-new/tier-new.component';
import { TierStatisticsComponent } from './manageValues/Tiers/components/tier-statistics/tier-statistics.component';
import { TierMainInfoComponent } from './manageValues/Tiers/components/tier-main-info/tier-main-info.component';
import { MallMainInfoComponent } from './manageValues/malls/components/mall-main-info/mall-main-info.component';
import { MallLinkedComapniesComponent } from './manageValues/malls/components/mall-linked-comapnies/mall-linked-comapnies.component';
import { AdminManagementListComponent } from './manageValues/adminManagement/admin-management-list/admin-management-list.component';
import { WebsiteAdminsListComponent } from './manageValues/adminManagement/websiteAdmins/website-admins-list/website-admins-list.component';
import { WebsiteAdminNewComponent } from './manageValues/adminManagement/websiteAdmins/website-admin-new/website-admin-new.component';
import { MessageService } from 'primeng/api';
import { CompanyAdminsListComponent } from './manageValues/adminManagement/companyAdmins/company-admins-list/company-admins-list.component';
import { CompanyAdminsNewComponent } from './manageValues/adminManagement/companyAdmins/company-admins-new/company-admins-new.component';
import { AdminTypePipe } from './pipe/admin-type.pipe';
import { AdminDetailsComponent } from './pages/admin-details/admin-details.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { DashboardCompanyComponent } from './dashboard-company/dashboard-company.component';
import { LoaderComponent } from './components/loader/loader.component';
import { SubscriptionListComponent } from './manageValues/Subscription/subscription-list/subscription-list.component';
import { SubscriptionNewComponent } from './manageValues/Subscription/subscription-new/subscription-new.component';
import { SubscriptionMainInfoComponent } from './manageValues/Subscription/Components/subscription-main-info/subscription-main-info.component';
import { SubscriptionStatisticsComponent } from './manageValues/Subscription/Components/subscription-statistics/subscription-statistics.component';
import { EndUsersListComponent } from './endUsers/end-users-list/end-users-list.component';

export function HttpLoaderFactory(http: HttpClient) {
    return new TranslateHttpLoader(http, "../assets/i18n/", ".json");
}
@NgModule({
    imports: [
        CommonModule,
        BrowserModule,
        FormsModule,
        AppRoutingModule,
        HttpClientModule,
        BrowserAnimationsModule,
        AccordionModule,
        AutoCompleteModule,
        AvatarGroupModule,
        AvatarModule,
        BadgeModule,
        BreadcrumbModule,
        ButtonModule,
        CalendarModule,
        CardModule,
        CarouselModule,
        CascadeSelectModule,
        ChartModule,
        CheckboxModule,
        ChipModule,
        ChipsModule,
        CodeHighlighterModule,
        ConfirmDialogModule,
        ConfirmPopupModule,
        ColorPickerModule,
        ContextMenuModule,
        DataViewModule,
        DialogModule,
        DividerModule,
        DropdownModule,
        FieldsetModule,
        FullCalendarModule,
        FileUploadModule,
        GalleriaModule,
        ImageModule,
        InplaceModule,
        InputNumberModule,
        InputMaskModule,
        InputSwitchModule,
        InputTextModule,
        InputTextareaModule,
        KnobModule,
        LightboxModule,
        ListboxModule,
        MegaMenuModule,
        MenuModule,
        MenubarModule,
        MessageModule,
        MessagesModule,
        MultiSelectModule,
        OrderListModule,
        OrganizationChartModule,
        OverlayPanelModule,
        PaginatorModule,
        PanelModule,
        PanelMenuModule,
        PasswordModule,
        PickListModule,
        ProgressBarModule,
        RadioButtonModule,
        RatingModule,
        RippleModule,
        ScrollPanelModule,
        ScrollTopModule,
        SelectButtonModule,
        SidebarModule,
        SkeletonModule,
        SlideMenuModule,
        SliderModule,
        SplitButtonModule,
        SplitterModule,
        StepsModule,
        TableModule,
        TabMenuModule,
        TabViewModule,
        TagModule,
        TerminalModule,
        TieredMenuModule,
        TimelineModule,
        ToastModule,
        ToggleButtonModule,
        ToolbarModule,
        TooltipModule,
        TreeModule,
        TreeTableModule,
        VirtualScrollerModule,
        ReactiveFormsModule,
        ToastrModule.forRoot(),
        TranslateModule.forRoot({
            loader: {
                provide: TranslateLoader,
                useFactory: HttpLoaderFactory,
                // useClass: TranslationService,
                // useFactory : HttpLoaderFactory,
                deps: [HttpClient],
            },
        }),
    ],
    declarations: [
        AppComponent,
        AppMainComponent,
        AppConfigComponent,
        AppMenuComponent,
        AppMenuitemComponent,
        AppRightPanelComponent,
        AppBreadcrumbComponent,
        AppTopBarComponent,
        AppFooterComponent,
        AppLoginComponent,
        AppNotfoundComponent,
        AppErrorComponent,
        AppAccessdeniedComponent,
        BroadcastMessagesListComponent,
        BroadcastMessageMainInfoComponent,
        BroadcastMessageNewComponent,
        BroadcastMessagePreviewComponent,

        SidemenuComponent,
        // CompanyprofileComponent,
        TranslatePipeDB,
        CompanylistComponent,
        CountrylistComponent,
        //CompanynewComponent2,
        DiscountlistComponent,
        DiscountnewComponent,
        MainInfoComponent,
        TimingDetailsComponent,
        GridHeadersComponent,
        CouponMainInfoComponent,
        TimingDetailsComponent,
        CompanyNewComponent,
        CountryNewComponent,
        CountryMainInfoComponent,
        OverviewComponent,
        BranchesComponent,
        ServicesListComponent,
        SubscriptionsComponent,
        GridItemInputDropdownComponent,
        GridItemInputTextComponent,
        GridItemButtonComponent,
        OpenHoursComponent,
        DiscountTypePipe,
        DiscountPeriodPipe,
        DicountApprovmentComponent,
        DiscountStatusPipe,
        CouponLogPipe,
        BroadcastMessageLogPipe,
        BroadcastMessageTypePipe,
        GenderPipe,
        UserTypePipe,
        AdminTypePipe,
        CouponValuePipe,
        DiscountValuePipe,
        LocalTimePipe,
        AmPmTimePipe,
        ProtectionTypePipe,
        RenewabilityTypePipe,
        PlaceholderPipe,
        IndustryNewComponent,
        IndustriesListComponent,
        MallNewComponent,
        MallsListComponent,
        ConfirmationDialogComponent,
        CouponNewComponent,
        CouponPreviewComponent,
        CouponlistComponent,
        BroadcastMessageTimingTargetedUserComponentComponent,
        CountryCitiesComponent,
        CountryAvailableCategoriesComponent,
        CountryAvailableTiersComponent,
        CountrySubscriptionsPricesComponent,
        TiersListComponent,
        TierNewComponent,
        TierStatisticsComponent,
        TierMainInfoComponent,
        MallMainInfoComponent,
        MallLinkedComapniesComponent,
        AdminManagementListComponent,
        WebsiteAdminsListComponent,
        WebsiteAdminNewComponent,
        CompanyAdminsListComponent,
        CompanyAdminsNewComponent,
        AdminDetailsComponent,
        DashboardComponent,
        DashboardCompanyComponent,
        LoaderComponent,
        SubscriptionListComponent,
        SubscriptionNewComponent,
        SubscriptionMainInfoComponent,
        SubscriptionStatisticsComponent,
        EndUsersListComponent,
    ],
    providers: [
        MessageService,// Add this here
        //  { provide: LocationStrategy, useClass: HashLocationStrategy },

        MenuService,
        AppBreadcrumbService,
        ConfigService,
        DatePipe,
        BroadcastMessageTypePipe,
        GenderPipe,
        // Register ApiTokenInterceptor first
        {
            provide: HTTP_INTERCEPTORS,
            useClass: ApiTokenInterceptor,
            multi: true
        },
        // Register ErrorInterceptor after
        {
            provide: HTTP_INTERCEPTORS,
            useClass: ErrorInterceptor,
            multi: true
        }
    ],
    bootstrap: [AppComponent],
})
export class AppModule { }
