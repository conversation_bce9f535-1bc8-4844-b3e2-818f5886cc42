import { WeekDay } from "@angular/common";
import { CompanyBranchModel } from "./CompanyBranchModel";

export class CompanyBranchWorkHours {

    /**
     *
     */
    constructor() {
        //   this.CompanyBranchId = "";
        this
    }
    CompanyBranch?: CompanyBranchModel;
    Active: boolean = true;
    OpenTime: string = "";
    CloseTime: string = "";
    OpenTime2: string = "";
    CloseTime2: string = "";
    DayChar: string = "";
    IsSplite: boolean = false;
    Day: WeekDay;
    CompanyBranchId: string = "";


}
