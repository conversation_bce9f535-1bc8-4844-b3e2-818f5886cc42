<p-toast></p-toast>
<div class="grid">
      <div class="col-12">
            <div>
                  <p-confirmDialog header="Confirmation" key="confirm1" icon="pi pi-exclamation-triangle"
                        message="Are you sure you want to proceed?" [style]="{ width: '350px' }"
                        acceptButtonStyleClass="p-button-text" rejectButtonStyleClass="p-button-text"></p-confirmDialog>


                  <p-table #dt [value]="Subscriptions" [paginator]="paginator" [rows]="pageSize"
                        [totalRecords]="totalRecords" [lazy]="true" [loading]="loading" (onLazyLoad)="loadData($event)"
                        [showCurrentPageReport]="true"
                        [currentPageReportTemplate]="'Showing {first} to {last} of {totalRecords} records'"
                        responsiveLayout="scroll" [rowHover]="true" styleClass="p-datatable-gridlines">

                        <ng-template pTemplate="caption">
                              <app-grid-headers [myDt]="Subscriptions" (SearchEvent)='ReceivedFilteredData($event)'
                                    addNewTxt="Add New Subscription" goRoute="/subscription-new"
                                    gridTitle="SubscriptionsManagement" [title]="title">
                              </app-grid-headers>
                        </ng-template>
                        <ng-template pTemplate="header">
                              <tr>
                                    <th>
                                                {{ "Subscription Name" | translate }}

                                          </th>
                                    <th>
                                                {{ "Num of Countries" | translate }}

                                          </th>
                                    <th>

                                                {{ "Num of Companies" | translate }}

                                          </th>
                                    <th>

                                                {{ "Edit" | translate }}

                                          </th>
                              </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-Subscription>
                              <tr>
                                    <td>
                                          <div class="flex">
                                                {{ Subscription.EnName }}
                                          </div>
                                    </td>
                                    <td>
                                          <div class="flex">
                                                {{ Subscription.TotalCountries }}
                                          </div>
                                    </td>
                                    <td>
                                          <div class="flex">
                                                {{ Subscription.TotalCompanies }}
                                          </div>
                                    </td>
                                    <td>
                                          <div class="flex">
                                          <div class="d-flex align-items-center justify-content-between">
                                                <p-button icon="pi pi-pencil" styleClass="p-button-rounded" class="mx-1"
                                                      (click)=" edit({ Subscription: Subscription, state: 'edit' })"></p-button>
                                                <p-button icon="pi pi-trash"
                                                      styleClass="p-button-rounded p-button-danger" class="mx-1"
                                                      (click)="delete(Subscription)"></p-button>
                                          </div>
                                          </div>
                                    </td>
                              </tr>
                        </ng-template>
                        <ng-template pTemplate="emptymessage">
                              <tr>
                                    <td class="text-center" colspan="7">
                                          {{ "No Subscriptions found." | translate }}
                                    </td>
                              </tr>
                        </ng-template>
                  </p-table>
                  <!-- Reusable Confirmation Dialog Component -->
                  <app-confirmation-dialog #confirmationDialog
                        (confirmResult)="handleConfirmationAction($event)"></app-confirmation-dialog>
            </div>
      </div>
</div>