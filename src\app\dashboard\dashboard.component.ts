import { Component } from '@angular/core';
import { DashboardService } from '../services/dashboard.service';
import { AppConfig } from '../demo/domain/appconfig';
import { ConfigService } from '../demo/service/app.config.service';
import { Subscription } from 'rxjs';
import { Country } from '../Model/Country';
import { CountryService } from '../services/country.service';
import { TimeConverter } from '../enum/time-enum';
import { UserModel } from '../Model/UserModel';
import { AuthService } from '../services/auth.service';
import { Router } from '@angular/router';

@Component({
  selector: 'dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent {
  countries: Country[];
  countryId: string = '';
  Country: Country | null = null;
  years: { label: string; value: number }[] = [];
  Year: { label: string; value: number } | null = null;

  config: AppConfig;
  subscription: Subscription;
  loading: boolean = true;
  lineOptions: any;
  lineData: any;

  pieData: any;
  pieOptions: any;
  barData: any;
  barOptions: any;
  ColumnOptions: any;

  EndUserWeeklyStatistics: any;
  EndUsersYearly: any;
  EndUserJoiningMethodStatistics: any;
  EndUserAgeStatistics: any;
  EndUserGenderStatistics: any;
  EndUserTierStatistics: any;
  EndUserLocationStatistics: any;
  EndUserNationalityStatistics: any;

  CompanyWeeklyStatistics: any;
  CompaniesYearly: any;

  CouponRedemptionWeeklyStatistics: any;
  CouponsRedemptionYearly: any;

  CouponWeeklyStatistics: any;
  CouponsYearly: any;

  DiscountRedemptionWeeklyStatistics: any;
  DiscountRedemptionYearly: any;

  DiscountWeeklyStatistics: any;
  DiscountYearly: any;

  BroadcastMessagesWeeklyStatistics: any;
  BroadcastMessagesYearly: any;

  BroadcastMessagesSentWeeklyStatistics: any;
  BroadcastMessagesSentYearly: any;

  selectedCard: string | null = 'endUsers';
  user: UserModel;

  constructor(private dashboardService: DashboardService, public configService: ConfigService, private countryService: CountryService, private authService: AuthService, private router: Router) { this.user = authService.getUserData(); }

  ngOnInit() {
    if (this.user.UserType == 1) {
      this.router.navigate(['/company-dashboard']);
      return;
    }
    this.countryService.Countries.subscribe((data) => {
      this.countries = data;
    });
    this.loadData();
    this.years = TimeConverter.generateYears();
  }

  loadData() {
    this.config = this.configService.config;
    this.updateChartOptions();
    this.dashboardService.getGetGeneralDashboard(this.countryId, this.Year).subscribe((data: any) => {
      var ResultContent = data.ResultContent;
      if (!ResultContent) {
        console.log('errorinContent')
        return;
      }
      this.EndUserWeeklyStatistics = ResultContent.EndUserStatistics.WeeklyStatistics;
      this.CompanyWeeklyStatistics = ResultContent.CompanyStatistics.WeeklyStatistics;
      this.CouponRedemptionWeeklyStatistics = ResultContent.CouponRedemptionStatistics.WeeklyStatistics;
      this.CouponWeeklyStatistics = ResultContent.CouponStatistics.WeeklyStatistics;
      this.DiscountRedemptionWeeklyStatistics = ResultContent.DiscountRedemptionStatistics.WeeklyStatistics;
      this.DiscountWeeklyStatistics = ResultContent.DiscountStatistics.WeeklyStatistics;
      this.BroadcastMessagesWeeklyStatistics = ResultContent.DiscountRedemptionStatistics.WeeklyStatistics;
      this.BroadcastMessagesSentWeeklyStatistics = ResultContent.DiscountStatistics.WeeklyStatistics;
      // Yearly

      this.EndUsersYearly = this.dashboardService.formatBarData(ResultContent.EndUserStatistics.YearlyTotals);
      this.CompaniesYearly = this.dashboardService.formatBarData(ResultContent.CompanyStatistics.YearlyTotals);
      this.CouponsRedemptionYearly = this.dashboardService.formatBarData(ResultContent.CouponRedemptionStatistics.YearlyTotals);
      this.CouponsYearly = this.dashboardService.formatBarData(ResultContent.CouponStatistics.YearlyTotals);
      this.DiscountRedemptionYearly = this.dashboardService.formatBarData(ResultContent.DiscountRedemptionStatistics.YearlyTotals);
      this.DiscountYearly = this.dashboardService.formatBarData(ResultContent.DiscountStatistics.YearlyTotals);

      this.EndUserJoiningMethodStatistics = this.dashboardService.formatBarData(ResultContent.EndUserStatistics.JoiningMethodStatistics);
      this.EndUserAgeStatistics = this.dashboardService.formatBarData(ResultContent.EndUserStatistics.AgeStatistics);
      this.EndUserTierStatistics = this.dashboardService.formatPieData(ResultContent.EndUserStatistics.TierStatistics);
      this.EndUserGenderStatistics = this.dashboardService.formatPieData(ResultContent.EndUserStatistics.GenderStatistics);
      this.EndUserGenderStatistics = this.dashboardService.formatPieData(ResultContent.EndUserStatistics.GenderStatistics);
      this.EndUserLocationStatistics = this.dashboardService.formatPieData(ResultContent.EndUserStatistics.LocationStatistics);
      this.EndUserNationalityStatistics = this.dashboardService.formatBarData(ResultContent.EndUserStatistics.NationalityStatistics);
      // this.BroadcastMessagesYearly=this.BroadcastMessagesSentYearly = this.dashboardService.formatBarData(ResultContent.BroadcastMessagesStatistics?.YearlyTotals); 
      this.loading = false;
    });

    this.lineData = {
      labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],
      datasets: [
        {
          label: 'First Dataset',
          data: [65, 59, 80, 81, 56, 55, 40],
          fill: false,
          backgroundColor: '#a0d2fa',
          borderColor: '#a0d2fa',
          tension: .4
        },
        {
          label: 'Second Dataset',
          data: [28, 48, 40, 19, 86, 27, 90],
          fill: false,
          backgroundColor: '#1c80cf',
          borderColor: '#1c80cf',
          tension: .4
        }
      ]
    };

    this.BroadcastMessagesYearly = this.BroadcastMessagesSentYearly = this.lineData;
    this.pieData = {
      labels: ['A', 'B', 'C'],
      datasets: [
        {
          data: [300, 50, 100],
          backgroundColor: [
            "#009688",
            "#3f51b5",
            "#9c27b0"
          ],
          hoverBackgroundColor: [
            "#009688",
            "#3f51b5",
            "#9c27b0"
          ]
        }
      ]
    };

    this.barData = {
      labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],
      datasets: [
        {
          label: 'My First dataset',
          backgroundColor: '#1c80cf',
          borderColor: '#1c80cf',
          data: [65, 59, 80, 81, 56, 55, 40]
        },
        {
          label: 'My Second dataset',
          backgroundColor: '#a0d2fa',
          borderColor: '#a0d2fa',
          data: [28, 48, 40, 19, 86, 27, 90]
        }
      ]
    };
    
  }

  selectCard(cardName: string): void {
    this.selectedCard = cardName;
  }

  changeCountry() {
    this.countryId = this.Country ? this.Country.Id : null;
    this.loading = true;
    this.loadData();
  }

  changeYear() {
    this.loading = true;
    this.loadData();
  }

  // THEME 
  updateChartOptions() {
    if (this.config.dark) {
      this.applyDarkTheme();
    } else {
      this.applyLightTheme();
    }
  }

  applyDarkTheme() {

    this.barOptions = {
      responsive: true,
      indexAxis: 'y',
      plugins: {
        legend: {
          labels: {
            color: '#ebedef'
          }
        }
      },
      scales: {
        x: {
          ticks: {
            color: '#ebedef'
          },
          grid: {
            color: 'rgba(160, 167, 181, .3)',
          }
        },
        y: {
          ticks: {
            color: '#ebedef'
          },
          grid: {
            color: 'rgba(160, 167, 181, .3)',
          }
        },
      }
    };
    this.ColumnOptions = {
      responsive: true,
      indexAxis: 'x',
      plugins: {
        legend: {
          labels: {
            color: '#ebedef'
          }
        }
      },
      scales: {
        x: {
          ticks: {
            color: '#ebedef'
          },
          grid: {
            color: 'rgba(160, 167, 181, .3)',
          }
        },
        y: {
          ticks: {
            color: '#ebedef'
          },
          grid: {
            color: 'rgba(160, 167, 181, .3)',
          }
        },
      }
    };
    this.pieOptions = {
      plugins: {
        legend: {
          labels: {
            color: '#ebedef'
          }
        }
      }
    };
  }

  applyLightTheme() {
    this.barOptions = {
      indexAxis: 'y',
      responsive: true,
      plugins: {
        legend: {
          labels: {
            fontColor: '#A0A7B5'
          }
        }
      },
      scales: {
        x: {
          ticks: {
            color: '#A0A7B5'
          },
          grid: {
            color: 'rgba(160, 167, 181, .3)',
          }
        },
        y: {
          ticks: {
            color: '#A0A7B5'
          },
          grid: {
            color: 'rgba(160, 167, 181, .3)',
          }
        },
      }
    };
    this.ColumnOptions = {
      indexAxis: 'x',
      responsive: true,
      plugins: {
        legend: {
          labels: {
            fontColor: '#A0A7B5'
          }
        }
      },
      scales: {
        x: {
          ticks: {
            color: '#A0A7B5'
          },
          grid: {
            color: 'rgba(160, 167, 181, .3)',
          }
        },
        y: {
          ticks: {
            color: '#A0A7B5'
          },
          grid: {
            color: 'rgba(160, 167, 181, .3)',
          }
        },
      }
    };
    this.pieOptions = {
      plugins: {
        legend: {
          labels: {
            color: '#495057'
          }
        }
      }
    };
  }

}
