export enum EnumTime {
  "08:00 AM - 09:00 AM" = "08:00 AM - 09:00 AM",
  "09:00 AM - 10:00 AM" = "09:00 AM - 10:00 AM",
  "10:00 AM - 11:00 AM" = "10:00 AM - 11:00 AM",
  "11:00 AM - 12:00 PM" = "11:00 AM - 12:00 PM",
  "12:00 PM - 01:00 PM" = "12:00 PM - 01:00 PM",
  "01:00 PM - 02:00 PM" = "01:00 PM - 02:00 PM",
  "02:00 PM - 03:00 PM" = "02:00 PM - 03:00 PM",
  "03:00 PM - 04:00 PM" = "03:00 PM - 04:00 PM",
  "04:00 PM - 05:00 PM" = "04:00 PM - 05:00 PM",
  "05:00 PM - 06:00 PM" = "05:00 PM - 06:00 PM",
  "06:00 PM - 07:00 PM" = "06:00 PM - 07:00 PM",
  "07:00 PM - 08:00 PM" = "07:00 PM - 08:00 PM"
}


export interface TimeConversion {
  StartTime: string;
  EndTime: string;
}

export class TimeConverter {

  static convertToTimeObject(slot: string): TimeConversion {
    const [start, end] = slot.split(' - ');
    return {
      StartTime: this.convertTo24Hour(start),
      EndTime: this.convertTo24Hour(end)
    };
  }

  private static convertTo24Hour(time: string): string {
    const [timePart, period] = time.split(' ');
    let [hour, minute] = timePart.split(':').map(Number);
    if (period === 'PM' && hour !== 12) {
      hour += 12;
    } else if (period === 'AM' && hour === 12) {
      hour = 0;
    }
    return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
  }

  static findTimeSlot(
    startTime: string,
    endTime: string,
    timeSlots: string[]
  ): string | null {
    for (const slot of timeSlots) {
      const { StartTime, EndTime } = this.convertToTimeObject(slot);
      if (StartTime === this.toHHMM(startTime) && EndTime === this.toHHMM(endTime)) {
        return slot;
      }
    }
    return null; // Return null if no matching slot is found
  }

  static toHHMM(time: string): string {
    return time.slice(0, 5); // Extract the first 5 characters: "HH:mm" like "12:00:00" , Output: "12:00"
  }


  static generateYears(startYear: number = 2024) {
    var yearsList: { label: string; value: number }[] = [];
    const currentYear = new Date().getFullYear();
    for (let year = startYear; year <= currentYear; year++) {
      yearsList.push({ label: year.toString(), value: year });
    }
    return yearsList;
  }
}