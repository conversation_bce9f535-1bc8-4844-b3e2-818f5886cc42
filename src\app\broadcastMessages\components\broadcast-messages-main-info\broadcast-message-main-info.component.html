<div class="container">
      <p-toast></p-toast>
      <div class="row" [formGroup]="mainInfoForm">

            <div class="col-md-8">
                  <div class="flex gap-2" *ngIf="this.user.UserType != 1">
                        <p-avatar [image]="companyProfileimage
                              | placeholder: 'assets/layout/images/upload-image.png'" styleClass="mr-2" size="xlarge"
                              shape="circle"></p-avatar>
                        <span class="flex flex-column gap-2 mb-3">
                              <label for="company" [style.color]="
                              CheckValid(mainInfoForm.controls.company)">Company name</label>
                              <p-dropdown [options]="companies" [(ngModel)]="_BroadcastMessageData.CompanyId"
                                    placeholder="select Company" optionLabel="EnName" optionValue="Id" id="company"
                                    (onChange)="refreshCompanyRelatedDetails()" formControlName="company"></p-dropdown>
                        </span>
                  </div>

                  <div>
                        <span class="flex flex-column gap-2 mb-3">
                              <label for="EnTitle" [style.color]="
                              CheckValid(mainInfoForm.controls.EnTitle)">English BroadcastMessage Title</label>
                              <input pInputText id="EnTitle" [(ngModel)]="_BroadcastMessageData.EnTitle" class="w-50"
                                    formControlName="EnTitle" />
                        </span>

                        <span class="flex flex-column gap-2 mb-3">
                              <label for="ArTitle" [style.color]="
                              CheckValid(mainInfoForm.controls.ArTitle)">Arabic BroadcastMessage Title</label>
                              <input pInputText id="ArTitle" [(ngModel)]="_BroadcastMessageData.ArTitle" class="w-50"
                                    formControlName="ArTitle" />
                        </span>
                  </div>
                  <div class="flex flex-column gap-2 mb-3">
                        <label for="EnContent" [style.color]="CheckValid(mainInfoForm.controls.EnContent)">English
                              Content</label>
                        <p-card role="region">
                              <textarea id="EnContent" [(ngModel)]="_BroadcastMessageData.EnContent"
                                    style="width: 100%; border: none; outline: none" formControlName="EnContent">
                              </textarea>
                        </p-card>
                        <label for="ArContent"
                              [style.color]="CheckValid(mainInfoForm.controls.ArContent)">ArabicContent</label>
                        <p-card role="region">
                              <textarea id="ArContent" [(ngModel)]="_BroadcastMessageData.ArContent"
                                    style="width: 100%; border: none; outline: none" formControlName="ArContent">
                  </textarea>
                        </p-card>
                  </div>

                  <div class="flex flex-column gap-2 mb-3">
                        <label for="type" [style.color]="CheckValid(mainInfoForm.controls.type)">Type</label>
                        <p-dropdown [options]="types" [(ngModel)]="_BroadcastMessageData.Type" placeholder="select Type"
                              formControlName="type"></p-dropdown>
                  </div>
                  <div class="flex flex-column gap-2 mb-3">
                        <label [style.color]="CheckValid(mainInfoForm.controls.mainPhone)" for="phoneNumber">Phone
                              Number</label>
                        <div class="p-inputgroup w-50">
                              <span class="p-inputgroup-addon py-0 pe-0">
                                    <i class="pi pi-phone me-2" [ngStyle]="{ color: 'var(--green-500)' }"></i>
                                    <p-dropdown [style.color]="CheckValid(mainInfoForm.controls.mainPhoneCode)"
                                          [options]="allCountries.AllCOUNTRIES" id="allCOUNTRIES"
                                          optionLabel="mobileCode" optionValue="mobileCode"
                                          class="p-inputtext-sm w-50 d-flex" [filter]="true" filterBy="mobileCode"
                                          formControlName="mainPhoneCode"
                                          [(ngModel)]="_BroadcastMessageData.PhoneNumber.CountryCode">

                                          <ng-template let-option pTemplate="item">
                                                <img src="assets/demo/flags/flag_placeholder.png"
                                                      [class]="'flag flag-' + option.code.toLowerCase()"
                                                      alt="{{ option.name }}" />
                                                <span>{{ option.mobileCode }}</span>
                                          </ng-template>
                                    </p-dropdown>
                              </span>
                              <p-inputNumber [useGrouping]="false" inputId="mainPhoneNumber" class="p-inputtext-sm w-50"
                                    [(ngModel)]="_BroadcastMessageData.PhoneNumber.Number"
                                    formControlName="mainPhone" />
                        </div>

                  </div>
                  <div class="flex flex-column gap-2 mb-3">
                        <label for="whatsappPhoneNumber"
                              [style.color]="CheckValid(mainInfoForm.controls.whatsappNumber)">Whatsapp
                              Phone Number</label>
                        <div class="p-inputgroup w-50">
                              <span class="p-inputgroup-addon py-0 pe-0">
                                    <i class="pi pi-whatsapp me-2" [ngStyle]="{ color: 'var(--green-500)' }"></i>
                                    <p-dropdown
                                          [style.color]="CheckValid(mainInfoForm.controls.whatsappNumber) || CheckValid(mainInfoForm.controls.whatsappNumberCode) "
                                          [style.color]="CheckValid(mainInfoForm.controls.whatsappNumberCode) "
                                          [options]="allCountries.AllCOUNTRIES" id="allCOUNTRIES"
                                          optionLabel="mobileCode" optionValue="mobileCode"
                                          [(ngModel)]="_BroadcastMessageData.WhatsappNumber.CountryCode"
                                          class="p-inputtext-sm w-50 d-flex" [filter]="true" filterBy="mobileCode"
                                          formControlName="whatsappNumberCode">
                                          <ng-template let-option pTemplate="item">
                                                <img src="assets/demo/flags/flag_placeholder.png"
                                                      [class]="'flag flag-' + option.code.toLowerCase()"
                                                      alt="{{ option.name }}" />
                                                <span>{{ option.mobileCode }}</span>
                                          </ng-template>
                                    </p-dropdown>
                              </span>
                              <p-inputNumber [useGrouping]="false" inputId="whatsappPhoneNumber"
                                    class="p-inputtext-sm w-50 d-flex" formControlName="whatsappNumber"
                                    [(ngModel)]="_BroadcastMessageData.WhatsappNumber.Number" />
                        </div>
                  </div>


            </div>
      </div>
      <div class="flex justify-content-end gap-2 mb-3">
            <p-button label="Next" (click)="next()"></p-button>
      </div>
</div>