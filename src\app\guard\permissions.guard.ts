import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, Router } from '@angular/router';
import { UserType } from '../enum/user-type';
import { AdminType } from '../enum/admin-type';
import { WebsiteAdminPageEnum } from '../enum/website-admin-pages-enum';
import { CompanyAdminPageEnum } from '../enum/company-admin-pages-enum';
import { AuthService } from '../services/auth.service';

@Injectable({
      providedIn: 'root',
})
export class PermissionsGuard implements CanActivate {
      user: any;

      constructor(private router: Router, private authService: AuthService) {
            const userData = this.authService.getUserData();
            this.user = userData ? userData : null;
      }

      canActivate(route: ActivatedRouteSnapshot): boolean {
            // console.log('user',this.user, route.data)
            if (!this.user) {
                  // Redirect if the user is not logged in
                  this.router.navigate(['/login']);
                  return false;
            }

            const requiredPermission = route.data['permission'];
            const isAuthorized = this.checkPermissions(requiredPermission);
            // console.log('requiredPermission', requiredPermission)
            // console.log('isAuthorized', isAuthorized)

            if (isAuthorized) {
                  return true; // User has the required permission
            }

            // Redirect unauthorized users
            this.router.navigate(['/access']);
            return false;
      }

      checkPermissions(requiredPermission: any): boolean {
            if (requiredPermission==null) {
                  return false;
            }

            // Check if the user is a WebsiteAdmin or CompanyAdmin
            if (this.user.UserType === UserType.WebsiteAdmin) {
                  return this.checkWebsiteAdminPermissions(requiredPermission);
            } else if (this.user.UserType === UserType.CompanyAdmin) {
                  return this.checkCompanyAdminPermissions(requiredPermission);
            }

            return false;
      }

      checkWebsiteAdminPermissions(requiredPermission: any): boolean {
            // Check for SuperAdmin or SubAdmin roles
            if (this.user.AdminType === AdminType.SuperAdmin) {
                  // SuperAdmins have all permissions
                  return Object.values(WebsiteAdminPageEnum).includes(requiredPermission);
            } else if (this.user.AdminType === AdminType.SubAdmin) {
                  // SubAdmins have limited permissions
                  return this.user.AdministeredPages.includes(requiredPermission);
            }

            return false;
      }

      checkCompanyAdminPermissions(requiredPermission: any): boolean {
            // Check for SuperAdmin or SubAdmin roles
            if (this.user.AdminType === AdminType.SuperAdmin) {
                  // SuperAdmins have all permissions
                  return Object.values(CompanyAdminPageEnum).includes(requiredPermission);
            } else if (this.user.AdminType === AdminType.SubAdmin) {
                  // SubAdmins have limited permissions
                  return this.user.AdministeredPages.includes(requiredPermission);
            }

            return false;
      }
}