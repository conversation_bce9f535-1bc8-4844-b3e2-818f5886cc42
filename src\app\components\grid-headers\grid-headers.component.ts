import {
    Component,
    EventEmitter,
    Input,
    Output,
} from "@angular/core";

@Component({
    selector: "app-grid-headers",
    templateUrl: "./grid-headers.component.html",
    styleUrls: ["./grid-headers.component.scss"],
})
export class GridHeadersComponent {
    constructor() { }

    titleOrName: string = '';
    industryName: string = '';
    genderName: string = '';
    residance: string = '';
    cityName: string = '';
    industry: { Name: any; icon: string; Code: any; };
    country: { Name: any; Id: any; };
    Company: { Name: any; Id: any; };
    tier: { Name: any; Id: any; };
    gender: { label: '', value: null };
    AdminType: { label: '', value: null };
    startDate: Date;
    endDate: Date;
    SendAsSoonAsPossible = false;


    @Input() myDt: any;
    @Input() gridTitle: String;
    @Input() goRoute: String;
    @Input() addNewTxt: String;
    @Input() title: string;
    // @Input() model: String;

    @Output() SearchEvent: EventEmitter<{}> = new EventEmitter();
    ngOnInit() {
        // console.log('myDt', this.myDt, this.gridTitle);
    }
    search() {
        if (this.gridTitle == 'Companies') {
            this.SearchEvent.emit({ 'companyName': this.titleOrName, 'cityName': this.cityName, 'industry': this.industry });
        }
        else if (this.gridTitle == 'Discounts') {
            // var endDate = this.endDate ? this.endDate : new Date(new Date().getFullYear(), 11, 31);
            this.SearchEvent.emit({ 'companyOrDiscountName': this.titleOrName, 'startDate': this.startDate, 'endDate': this.endDate });
        }
        else if (this.gridTitle == 'Industries') {
            // var endDate = this.endDate ? this.endDate : new Date(new Date().getFullYear(), 11, 31);
            this.SearchEvent.emit({ 'Name': this.industryName, });
        }
        else if (this.gridTitle == 'Coupons') {
            // var endDate = this.endDate ? this.endDate : new Date(new Date().getFullYear(), 11, 31);
            this.SearchEvent.emit({ 'companyOrCouponName': this.titleOrName, 'startDate': this.startDate, 'endDate': this.endDate });
        }
        else if (this.gridTitle == 'Broadcast Messages') {
            // var endDate = this.endDate ? this.endDate : new Date(new Date().getFullYear(), 11, 31);
            this.SearchEvent.emit({ 'CompanyNameOrBroadcastMessageTitle': this.titleOrName, 'startDate': this.startDate, 'endDate': this.endDate, 'SendAsSoonAsPossible': this.SendAsSoonAsPossible });
        }
        else if (this.gridTitle == 'malls') {
            this.SearchEvent.emit({ 'Name': this.titleOrName, 'Location': this.cityName });
        } else if (this.gridTitle == 'EndUsersManagement') {
            this.SearchEvent.emit({ 'Name': this.titleOrName, 'Gender': this.gender, 'Residance': this.residance, 'TierId': this.tier?.Id });
        }
        else if (this.gridTitle == 'CompanyAdminsManagement' || this.gridTitle == 'WebsiteAdminsManagement') {
            this.SearchEvent.emit({ 'Name': this.titleOrName, 'AdminType': this.AdminType, 'CompanyId': this.Company?.Id, 'CountryId': this.country?.Id });
        }
        else if (this.gridTitle == 'SubscriptionsManagement' || 'TiersManagement' || 'CountriesManagement') {
            this.SearchEvent.emit({ 'Name': this.titleOrName });
        }

    }

    titleNameReceivedValue(value: string) {
        this.titleOrName = value;
    }

    industryNameReceivedValue(value: string) {
        this.industryName = value;
    }
    cityNameReceivedValue(value: string) {
        this.cityName = value;
    }
    residanceReceivedValue(value: string) {
        this.residance = value;
    }
    industryRecievedValue(event) {
        this.industry = event;
    }
    genderRecievedValue(event) {
        this.gender = event;
    }
    companyRecievedValue(event) {
        this.Company = event;
    }
    adminTypeRecievedValue(event) {
        this.AdminType = event;
    }
    clearIndustry() {
        this.industry = null;
    }
    clearGender() {
        this.gender = null;
    }
    clearCompany() {
        this.Company = null;
    }
    clearAdminType() {
        this.AdminType = null;
    }
    countryRecievedValue(event) {
        this.country = event;
    }
    clearCountry() {
        this.country = null;
    }
    tierRecievedValue(event) {
        this.tier = event;
    }
    clearTier() {
        this.tier = null;
    }
    clearStartDate() {
        this.startDate = null;
    }
    clearEndDate() {
        this.endDate = null;
    }
}
