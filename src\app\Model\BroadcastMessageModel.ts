
import { BroadcastMessageStatusEnum } from "../enum/broadcast-message-status-enum";
import { BroadcastMessageTypesEnum } from "../enum/broadcast-message-type-enum";
import { Gender } from "../enum/gender";
import { BroadcastMessageLog } from "./BroadcastMessageLog";
import { BroadcastMessageStatus } from "./BroadcastMessageStatus";
import { CityModel } from "./CityModel";
import { CompanyModel } from "./CompanyModel";
import { Country } from "./Country";
import { NationalityModel } from "./NationalityModel";
import { PhoneNumberModel } from "./PhoneNumberModel";
import { TiersModel } from "./TiersModel";

export class BroadcastMessageModel {
      constructor() {
      }
      Id: string = null;
      UserFriendly: number = null;
      EnTitle: string = "";
      ArTitle: string = "";
      EnContent: string = "";
      ArContent: string = "";
      Company?: CompanyModel;
      Type: BroadcastMessageTypesEnum;

      SendAsSoonAsPossible: boolean = true;
      LaunchDate: Date;
      LaunchStartTime: string = null; // Store time as a string (e.g., "14:30" for 2:30 PM)
      LaunchEndTime: string = null;

      Active: boolean = false;
      Status: BroadcastMessageStatusEnum;

      BroadcastMessagesStatuses?: BroadcastMessageStatus[]; // Not used
      BroadcastMessagesLog?: BroadcastMessageLog[];

      CompanyId?: string;
      PhoneNumber: PhoneNumberModel;
      WhatsappNumber: PhoneNumberModel;

      //Targeted Users
      TargetedTiers?: TiersModel[] = [];
      TargetedGender: Gender = null;
      TargetedMinAge: number = null;
      TargetedMaxAge: number = null;
      TargetedCountry?: Country;
      TargetedCities: CityModel[] = [];
      TargetedNationalities: NationalityModel[] = [];

      Note?: string = "";

      //region NotMapped

      VisibleDialog = false;
      //
      hasError: boolean = false;
      Tiers: TiersModel[] = [];
      Countries: Country[] = [];


}
