<!-- <p-toast></p-toast> -->
<div>

      <h3>
            <p-button icon="pi pi-arrow-left" styleClass="p-button-rounded p-button-secondary p-button-text"
                  routerLink="/malls">
            </p-button>
            {{title}}
      </h3>
      <!-- <p-messages *ngIf="this.newMall.HasError" [value]="msgs"></p-messages> -->
      <!-- <p-tabView [activeIndex]="1"> -->
      <p-tabView [(activeIndex)]="activeIndex" (onChange)="onTabChange($event)">
            <p-tabPanel header="Main Info" [ngStyle]="{ color: 'var(--cyan-300-color)' }">
                  <p-card role="region">
                        <mall-main-info [(_MallData)]="newMall" [(activeIndex)]="activeIndex"
                              [isEditing]="isEditing"></mall-main-info>
                  </p-card>
            </p-tabPanel>
            <p-tabPanel header="Linked Comapnies" [disabled]="tabsDisabled" *ngIf="isEditing">
                  <p-card role="region">
                        <mall-linked-comapnies [(_MallData)]="newMall" [(activeIndex)]="activeIndex"></mall-linked-comapnies>
                  </p-card>
            </p-tabPanel>

      </p-tabView>
</div>