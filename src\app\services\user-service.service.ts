import { UserModel } from './../Model/UserModel';
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { map } from 'rxjs/operators';
import { Dto } from '../Model/Dto';
import { Observable } from 'rxjs/internal/Observable';
import { environment } from 'src/environments/environment';

@Injectable({
	providedIn: 'root'
})
export class UserService {

	constructor(protected httpClient: HttpClient,) { }

	public CheckCompanyAdminExist(data): Observable<Dto<UserModel>> {
		var http;
		var url = `${environment.apiUrl}CompanyAdmin/CheckCompanyAdminExist`;
		http = this.httpClient.post(url, data);
		return http.pipe(
			map((res: any) => {

				return res;
			})
		);
	}

	public CheckUserEmailExist(data): Observable<Dto<UserModel>> {
		var http;
		var url = `${environment.apiUrl}User/CheckEmailExist`;
		http = this.httpClient.post(url, data);
		return http.pipe(
			map((res: any) => {
				return res;
			})
		);
	}

}
