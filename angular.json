{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"poseidon-cli": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/poseidon-ng", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "aot": true, "assets": ["src/favicon.ico", "src/assets", "src/upload.php"], "styles": ["src/styles.scss", "node_modules/@fortawesome/fontawesome-free/css/all.min.css", "node_modules/bootstrap/dist/css/bootstrap.min.css", "node_modules/ngx-toastr/toastr.css"], "scripts": ["node_modules/prismjs/prism.js", "node_modules/prismjs/components/prism-typescript.js", "node_modules/bootstrap/dist/js/bootstrap.min.js", "node_modules/jquery/dist/jquery.js", "node_modules/@fortawesome/fontawesome-free/js/all.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "poseidon-cli:build"}, "configurations": {"production": {"browserTarget": "poseidon-cli:build:production"}, "development": {"browserTarget": "poseidon-cli:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "poseidon-cli:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "scripts": [], "styles": ["src/styles.scss"], "assets": ["src/favicon.ico", "src/assets", "src/upload.php"]}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["tsconfig.app.json", "tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}}, "defaultProject": "poseidon-cli", "cli": {"analytics": false}}