<div class="grid">
    <div class="col-12">
        <div *ngIf="user.UserType == 0">
            <p-confirmDialog header="Confirmation" key="confirm1" icon="pi pi-exclamation-triangle"
                message="Are you sure you want to proceed?" [style]="{ width: '350px' }"
                acceptButtonStyleClass="p-button-text" rejectButtonStyleClass="p-button-text"></p-confirmDialog>
            <p-toast></p-toast>

            <p-table #dt [value]="companies" [paginator]="paginator" [rows]="pageSize" [totalRecords]="totalRecords"
                [lazy]="true" [loading]="loading" (onLazyLoad)="loadData($event)" [showCurrentPageReport]="true"
                [currentPageReportTemplate]="'Showing {first} to {last} of {totalRecords} records'"
                responsiveLayout="scroll" [rowHover]="true" styleClass="p-datatable-gridlines">

                <ng-template *ngIf="user.UserType == 0" pTemplate="caption">
                    <app-grid-headers [myDt]="companies" (SearchEvent)='ReceivedFilteredData($event)'
                        addNewTxt="Add New Company" goRoute="/company-new" gridTitle="Companies" [title]="title">
                    </app-grid-headers>
                </ng-template>
                <ng-template pTemplate="header">
                    <tr>
                        <th>
                            <div class="flex justify-content-between align-items-center">
                                {{ "Active" | translate }}
                            </div>
                        </th>
                        <th>
                            <div class="flex justify-content-between align-items-center my-center-text">
                                {{ "ID" | translate }}
                            </div>
                        </th>
                        <th>
                            <div class="flex justify-content-between align-items-center">
                                {{ "Company Name" | translate }}
                            </div>
                        </th>
                        <th>
                            <div class="flex justify-content-between align-items-center">
                                {{ "Services" | translate }}
                            </div>
                        </th>
                        <th>
                            <div class="flex justify-content-between align-items-center">
                                {{ "Company Industry" | translate }}
                                <!-- <p-sortIcon field="About"></p-sortIcon>
                        <p-columnFilter
                            type="text"
                            field="About"
                            display="menu"
                            class="ml-auto"
                        ></p-columnFilter> -->
                            </div>
                        </th>
                        <th>
                            <div class="flex justify-content-between align-items-center">
                                {{ "Main Branch Location" | translate }}
                                <!-- <p-sortIcon field="About"></p-sortIcon>
                        <p-columnFilter
                            type="text"
                            field="About"
                            display="menu"
                            class="ml-auto"
                        ></p-columnFilter> -->
                            </div>
                        </th>
                        <th>
                            <div class="flex justify-content-between align-items-center">
                               # {{ "Branches" | translate }}
                                <!-- <p-sortIcon field="About"></p-sortIcon>
                        <p-columnFilter
                            type="text"
                            field="About"
                            display="menu"
                            class="ml-auto"
                        ></p-columnFilter> -->
                            </div>
                        </th>
                        <th>
                            <div class="flex justify-content-between align-items-center">
                                {{ "Edit" | translate }}
                                <!-- <p-sortIcon field="Edit"></p-sortIcon>
                        <p-columnFilter
                            type="text"
                            field="Edit"
                            display="menu"
                            class="ml-auto"
                        ></p-columnFilter> -->
                            </div>
                        </th>
                        <!-- <th style="width: 5rem"></th> -->
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-companies>
                    <tr  [ngClass]="{'inactive-table-row': companies.Active === false}">
                        <td>
                            <span class="p-column-title">Active</span>
                            <p-inputSwitch (onChange)="editActiveCompany(companies.Id)"
                                [(ngModel)]="companies.Active"></p-inputSwitch>
                        </td>
                        <td>
                            <div class="flex">
                                {{ companies.UserFriendly }}
                            </div>
                        </td>
                        <td>
                            <div class="flex">
                                <p-avatar *ngIf="companies.LogoUrl" [alt]="companies.EnName"
                                    image="{{getLogoUrl( companies.LogoUrl)}}" styleClass="mr-2" size="medium"
                                    shape="circle"></p-avatar>
                                {{ companies.EnName }}
                            </div>
                        </td>
                        <td>
                            <div class="flex">
                                <div *ngIf="companies.CompanySubscriptions.length > 0 ">
                                    <span *ngFor="let subscription of companies.CompanySubscriptions">
                                        <!-- {{ subscription.EnName }}  -->
                                        <p-chip [label]="subscription.EnName"></p-chip>
                                        <br>
                                    </span>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="flex">
                            <p-chip *ngFor="let industry of companies.CompanyIndustries"
                                [label]="industry.EnName"></p-chip>
                                </div>
                        </td>
                        <td>
                            <div class="flex">
                            <img *ngIf="!companies.MainBranchLocation" src="assets/pages/icons/online2.png"
                                class="small-icon" alt="Online" title="Online">
                            {{ companies.MainBranchLocation || "Online"}}
                            </div>
                        </td>
                        <td>
                            <div class="flex">
                            {{ companies.BranchesNum }}
                            </div>
                        </td>
                        <td>
                            <span class="p-column-title">Edit</span>
                            <div class="d-flex align-items-center justify-content-between">
                                <p-button [disabled]="!companies.Active" icon="pi pi-pencil"
                                    styleClass="p-button-rounded" class="mx-1"
                                    (click)=" edit({ company: companies, state: 'edit' })"></p-button>
                                <p-button *ngIf="user.UserType == 0" icon="pi pi-trash"
                                    styleClass="p-button-rounded p-button-danger" class="mx-1"
                                    (click)="delete(companies.Id)"></p-button>
                            </div>
                        </td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td class="text-center" colspan="7">
                            {{ "No Companies found." | translate }}
                        </td>
                    </tr>
                </ng-template>
            </p-table>
            <!-- Reusable Confirmation Dialog Component -->
            <app-confirmation-dialog #confirmationDialog
                (confirmResult)="handleConfirmationAction($event)"></app-confirmation-dialog>

        </div>
    </div>
</div>
<div class="loader" *ngIf="user.UserType != 0">
    <div class="loader-inner">
        <div class="loader-line-wrap">
            <div class="loader-line"></div>
        </div>
        <div class="loader-line-wrap">
            <div class="loader-line"></div>
        </div>
        <div class="loader-line-wrap">
            <div class="loader-line"></div>
        </div>
        <div class="loader-line-wrap">
            <div class="loader-line"></div>
        </div>
        <div class="loader-line-wrap">
            <div class="loader-line"></div>
        </div>
    </div>
</div>