<div>
    <h3>
        <p-button icon="pi pi-arrow-left" styleClass="p-button-rounded p-button-secondary p-button-text"
            (click)="goBack()"></p-button>
        {{ title }}
    </h3>
    <p-messages  *ngIf="_BroadcastMessageData.hasError" [value]="msgs"></p-messages>
    <p-tabView [(activeIndex)]="activeIndex" (onChange)="onTabChange($event)">
        <p-tabPanel header="Main info" [ngStyle]="{ color: 'var(--cyan-300-color)' }">
            <p-card role="region">
                <broadcast-message-main-info [_BroadcastMessageData]="_BroadcastMessageData"
                    [(activeIndex)]="activeIndex" [editing]="editing"></broadcast-message-main-info>
            </p-card>
        </p-tabPanel>
        <!-- [disabled]="tabsDisabled" -->
        <p-tabPanel header="Timing & Targeted Users">
            <p-card role="region">
                <broadcast-message-timing-targeted-user-component [(_BroadcastMessageData)]="_BroadcastMessageData" [editing]="editing"
                    [(activeIndex)]="activeIndex"></broadcast-message-timing-targeted-user-component>
            </p-card>
        </p-tabPanel>

    </p-tabView>
</div>