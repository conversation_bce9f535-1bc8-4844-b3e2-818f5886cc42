<div class="open-hours" [formGroup]="openHoursGroup">
    <div formArrayName="items">
        <div class="row mb-1 open-hours-item" *ngFor="let item of workHours; let i = index" [formGroupName]="i">
            <!-- {{ item | json }} <br /> -->
            <!-- <p-button [label]="item.dayChar" [styleClass]="item.acitve ? '' : 'p-button-outlined'" class="day-button" (click)=""></p-button> -->
            <div class="col-1 py-1">
                <button class="btn day-button" [ngClass]="item.Active ? 'btn-primary' : 'btn-outline-primary'"
                    type="button" (click)="item.Active = !item.Active">
                    {{ item.DayChar }}
                </button>
            </div>

            <div class="col-11 d-flex align-items-center py-1" *ngIf="item.Active">
                <div clprotonass="d-flex align-items-center">
                    <input type="time" pInputText [(ngModel)]="item.OpenTime" style="width: 110px" class="mx-2"
                        formControlName="openTime" />
                    -
                    <input type="time" pInputText [(ngModel)]="item.CloseTime" id="offerTime" style="width: 110px"
                        class="mx-2" formControlName="closeTime" />
                </div>

                <p-button label="splite service" styleClass="p-button-link" icon="pi pi-angle-right" iconPos="right"
                    *ngIf="!item.IsSplite" (click)="item.IsSplite = true"></p-button>

                <span *ngIf="item.IsSplite" class="mx-2">and</span>

                <div class="d-flex align-items-center" *ngIf="item.IsSplite">
                    <input type="time" pInputText [(ngModel)]="item.OpenTime2" style="width: 110px" class="mx-2"
                        formControlName="openTime2" />
                    -
                    <input type="time" pInputText [(ngModel)]="item.CloseTime2" id="offerTime" style="width: 110px"
                        class="mx-2" formControlName="closeTime2" />
                    <p-button label="close splite" styleClass="p-button-link" icon="pi pi-angle-left" iconPos="right"
                        (click)="
                            item.IsSplite = !item.IsSplite
                        "></p-button>
                </div>
            </div>
        </div>
    </div>
    <span class="text-danger" *ngIf="openHoursGroup.invalid">Please check that your times are valid (Open time must be
        less than close time).</span>
</div>