import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { EventEmitter, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { ToastrService } from 'ngx-toastr';
import { DataSharingService } from './DataSharing.service';
import { UserModel } from '../Model/UserModel';
import { catchError, map, Observable, of, tap, throwError } from 'rxjs';
import { Dto } from '../Model/Dto';
import { RegisterViewModel } from '../Model/RegisterViewModel';
import { environment } from 'src/environments/environment';
import { LoginDTO } from '../PayloadDTO/login-dto.model';
import { AdminType } from '../enum/admin-type';
import { UserType } from '../enum/user-type';


@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private tokenKey = 'token';
  private RefreshTokenKey = 'RefreshToken';
  private currentUser = 'currentUser';

  userUpdated = new EventEmitter<void>();

  constructor(
    private translate: TranslateService,
    private toastr: ToastrService,
    protected httpClient: HttpClient,
    private router: Router,
    private dsService: DataSharingService) { }


  public Register(registerUser: UserModel): Observable<Dto<RegisterViewModel>> {
    return this.httpClient.post<any>(`${environment.apiUrl}User/Register`, registerUser)
      .pipe(
        map((res: Dto<RegisterViewModel>) => {
          return res;
        }));
  }

  public Login(loginUser: UserModel) {
    const loginData: LoginDTO = {
      Email: loginUser.Email,
      Password: loginUser.Password
    };
    return this.httpClient.post<any>(`${environment.apiUrl}User/Login`, loginData)
      .pipe(
        map((res: Dto<UserModel>) => {
          if (!res.HasError) {
            this.storeUser(res.ResultContent.User);
            this.storeTokens(res.ResultContent.Token.toString(), res.ResultContent.RefreshToken.toString());
            return true;
          }
          else {
            if (this.translate.currentLang === 'ar') {
              // this.toastr.error(res.ArErrorMessage.toString(), this.translate.instant('error'));
              /*this.toastr.success('everything is broken', 'Major Error', {
                timeOut: 3000,
              });*/
            }
            else {
              // this.toastr.error(res.EnErrorMessage.toString(), this.translate.instant('error'));
            }
            return false;
          }
        }), catchError(error => {
          console.log(error.error);
          if (this.translate.currentLang === 'ar') {
            this.toastr.error(error.error.ArErrorMessage.toString(), this.translate.instant('error'));
            return of(false);
          }
          else {
            this.toastr.error(error.error.EnErrorMessage.toString(), this.translate.instant('error'));
            return of(false);
          }
          // return of(false);
        })

      );
  }

  // Store tokens in localStorage
  private storeTokens(token: string, RefreshToken: string) {
    localStorage.setItem(this.tokenKey, token);
    // localStorage.setItem('token', token.toString());
    localStorage.setItem(this.RefreshTokenKey, RefreshToken);
  }
  private storeUser(currentUser) {
    localStorage.setItem(this.currentUser, JSON.stringify(
      // currentUser
      {
        Email: currentUser.Email,
        UserName: currentUser.Name,
        //  Verified: false,
        UserType: currentUser.UserType,
        AdminType: currentUser.AdminType,
        AdministeredPages: currentUser.AdministeredPages,
        ProfilePhotoUrl: currentUser.ProfilePhotoUrl
      }
    )
    );
  }
  // Refresh the access token
  refreshAccessToken() {
    const RefreshToken = localStorage.getItem(this.RefreshTokenKey);
    if (!RefreshToken) {
      return throwError(() => new Error('No refresh token available.'));
    }
    return this.httpClient
      .post<any>('/refresh', { 'RefreshToken': RefreshToken })
      .pipe(
        tap((response) => {
          this.storeTokens(response.AccessToken, response.RefreshToken);
        }),
        catchError(this.handleError)
      );
  }

  private handleError(error: HttpErrorResponse) {
    console.error('Auth error:', error);
    return throwError(() => error);
  }

  // Get stored access token
  getAccessToken() {
    return localStorage.getItem(this.tokenKey);
  }

  getUserData(): UserModel {
    const userData = localStorage.getItem(this.currentUser);
    return userData ? JSON.parse(userData) : null;
  }

  updateCurrentUser(Email: string, userName: string, UserType: UserType, AdminType: AdminType, newPages: number[], ProfilePhotoUrl: string) {
    const user = this.getUserData();
    if (user) {
      user.AdministeredPages = newPages;
      user.UserType = UserType;
      user.AdminType = AdminType;
      user.Email = Email;
      user.ProfilePhotoUrl = ProfilePhotoUrl;
      user.UserName = userName;
      localStorage.setItem(this.currentUser, JSON.stringify(user));
      this.userUpdated.emit();
    }
  }

  resetPassword(data) {
    var http;
    var url = `${environment.apiUrl}User/ResetPassword`;
    http = this.httpClient.patch(url, data);
    return http.pipe(
      map((res) => { return res; }), catchError(error => { return of(false); }));
  }
  getPersonalDetails() {
    return this.httpClient.get<Dto<UserModel>>(`${environment.apiUrl}User/GetPersonalDetails`)
      .pipe(
        map((res: any) => {
          return res['ResultContent'];
        })
      );
  }

  // Clear tokens from storage (e.g., on logout)
  logout() {
    localStorage.removeItem(this.tokenKey);
    localStorage.removeItem(this.RefreshTokenKey);
    localStorage.removeItem(this.currentUser);
  }
}
