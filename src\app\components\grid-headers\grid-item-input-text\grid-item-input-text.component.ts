import { Component, EventEmitter, Input, Output } from "@angular/core";

@Component({
    selector: "app-grid-item-input-text",
    templateUrl: "./grid-item-input-text.component.html",
    styleUrls: ["./grid-item-input-text.component.scss"],
})
export class GridItemInputTextComponent {
    @Input() label: string = "";
    @Input() icon: string = "";
    @Input() placeholder: string;
    @Input() myDt: any;
    // @Input() myValue: string;
    myValue: string;
    @Output() myValueChange: EventEmitter<string> = new EventEmitter();
    // testPrint() {
    //     console.log(this.myValue);
    // }

    emitValueToParent() {
        //console.log(this.myValue);
        this.myValueChange.emit(this.myValue);
    }
}
