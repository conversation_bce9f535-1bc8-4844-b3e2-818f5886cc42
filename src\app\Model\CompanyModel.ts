import { UserModel } from './UserModel';
import { ActivityScope } from "../enum/activity-scope";
import { CompanyBranchModel } from './CompanyBranchModel';
import { IndustryModel } from './IndustryModel';
import { FilterModel } from './FiltersModel';
import { CompanyCountryModel } from './CompanyCountryModel';
import { CityModel } from './CityModel';
import { TiersModel } from './TiersModel';
import { Country } from './Country';
import { SubscriptionModel } from './SubscriptionModel';
import { PhoneNumberModel } from './PhoneNumberModel';

export class CompanyModel {
    /**
     *
     */
    constructor() {
    }
    Id: string = "";
    ArName: string = "";
    EnName: string = "";
    // Location: string = "";
    PhoneNumber: PhoneNumberModel;
    WhatsappNumber: PhoneNumberModel;
    CompanyWebSite: string = "";
    CompanyPin: string = "";
    CompanyAdmin?: UserModel;
    CompanyBranches?: CompanyBranchModel[] = [];
    CompanyFilters?: FilterModel[] = [];
    CompanyIndustries?: IndustryModel[] = [];
    CompanyCountries?: Country[];
    CompanyTiers?: TiersModel[] = [];
    CompanySubscriptions?: SubscriptionModel[] = [];
    selectedCompanyCurrency: string = "$";
    ActivityScope: ActivityScope;
    Country?: Country;
    EnAbout: string = "";
    ArAbout: string = "";
    Active: boolean = true;
    ServiceListUrl: string = "";
    CompanySize: string = "";
    LogoUrl: string = "";
    Logo: File;

    PriceOfSubscription: number = 0;
    DiscountGranted: number = 0;
    Total: number = 0;

    Discounts: boolean = false;
    Reviewed: boolean = false;
    // FreeTier: boolean = false;
    // SilverTier: boolean = false;
    // GoldTire: boolean = false;


    //Not Mapped
    AllCities: CityModel[];
    Filters: FilterModel[];
    Industries: IndustryModel[];
    Countries: Country[];
    Tiers: TiersModel[] = [];
    Subscriptions: SubscriptionModel[] = [];
    Currency: string = 'Dollar';
    Code?: {
        name,
        code,
        timezone,
        ut,
        mobileCode,
    };

    hasError: boolean = false;
}