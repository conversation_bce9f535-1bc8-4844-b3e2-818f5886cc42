<p-toast></p-toast>
<div class="row" [formGroup]="overviewForm">
    <div class="col-4">
        <div class="text-center">
            <div class="image-avatar-container">
                <label for="companyImage" class="w-100">
                    <img [src]="
                            imageUrl
                                | placeholder
                                    : 'assets/layout/images/upload-image.png'
                        " class="roundeisSelectedFiled-circle w-100" alt="..." />
                    <p-button icon="pi pi-pencil" class="edit-button" styleClass="p-button-rounded"
                        (click)="file.click()"></p-button>
                    <!-- [style.color]="CheckValid(overviewForm.controls.imageUrl)" -->
                </label>

                <input type="file" id="companyImage" class="d-none" [disabled]="user.UserType != 0"
                    (change)="uploadFile(file.files)" accept="image/*" #file formControlName="imageUrl" />
            </div>
            <label *ngIf="CheckValid(overviewForm.controls.imageUrl)=='red'" for="logoImage"
                [style.color]="CheckValid(overviewForm.controls.imageUrl)">Please upload Logo Image</label>
        </div>
    </div>

    <div class="col-8">
        <div class="flex flex-column gap-2 mb-3">
            <label for="arabicName" [style.color]="CheckValid(overviewForm.controls.companyArName)">Arabic company
                name</label>
            <input id="arabicName" type="text" pInputText placeholder="Arabic Name" [(ngModel)]="_OverViewData.ArName"
                class="p-inputtext-sm w-50" formControlName="companyArName" />
        </div>

        <div class="flex flex-column gap-2 mb-3">
            <label for="englishName" [style.color]="CheckValid(overviewForm.controls.companyEnName)">English company
                name</label>
            <input id="englishName" type="text" pInputText placeholder="English Name" [(ngModel)]="_OverViewData.EnName"
                class="p-inputtext-sm w-50" formControlName="companyEnName" />
        </div>

        <div class="flex flex-column gap-2 mb-3">
            <label for="adminName" [style.color]="CheckValid(overviewForm.controls.name)">Admin name</label>
            <input id="adminName" type="text" pInputText placeholder="Admin Name"
                [(ngModel)]="_OverViewData.CompanyAdmin.Name" class="p-inputtext-sm w-50" formControlName="name" />
        </div>

        <form>

        </form>
        <div class="flex flex-column gap-2 mb-3">
            <label for="adminEmail" [style.color]="CheckValid(overviewForm.controls.email)">Admin email</label>
            <!-- <form autocomplete="off"> -->
            <!-- (focusout)="onEmailInputFocusOut()" -->
            <input id="adminEmail" (focusout)="onEmailInputFocusOut()" type="email" pInputText placeholder="Admin Email" [email]="true"
                [(ngModel)]="_OverViewData.CompanyAdmin.Email" class="p-inputtext-sm w-50" formControlName="email"
                #emailInput />
            <span *ngIf="overviewForm.get('email').hasError('invalidEmailError')"
                [style.color]="CheckValid(overviewForm.controls.email)">
                Please enter a valid Email .
            </span>
            <!-- autocomplete='false' -->
            <!-- </form> -->
        </div>
        <div class="flex flex-column gap-2 mb-3">
            <label *ngIf="!isEditing" for="adminPassword"
                [style.color]="CheckValid(overviewForm.controls.password)">Admin password
            </label>
            <label *ngIf="isEditing" for="adminPassword">Admin password
            </label>
            <p-password [(ngModel)]="_OverViewData.CompanyAdmin.Password" class="p-inputtext-sm w-50"
                [toggleMask]="true" formControlName="password" id="adminPassword" placeholder="Admin Password"
                #passwordInput></p-password>
            <span *ngIf="overviewForm.get('password').hasError('pattern')"
                [style.color]="CheckValid(overviewForm.controls.password)">
                Password must contain at least one lowercase letter, one uppercase letter, one digit, and one
                special
                character ($@$!%*?&), and be at least 8 characters long.
            </span>

        </div>

        <div class="flex flex-column gap-2 mb-3">
            <label for="Industry" [style.color]="CheckValid(overviewForm.controls.Industry)">Industry</label>
            <p-multiSelect [options]="industries" [(ngModel)]="_OverViewData.Industries"
                defaultLabel="Select Industries" optionLabel="EnName" display="chip" id="Industry"
                [disabled]="user.UserType != 0" class="p-inputtext-sm" formControlName="Industry"></p-multiSelect>
        </div>
        <div class="flex flex-column gap-2 mb-3">
            <label for="filters">Filters</label>
            <p-multiSelect [options]="filters" [(ngModel)]="_OverViewData.Filters" defaultLabel="Select filters"
                optionLabel="EnName" display="chip" id="filters" formControlName="filters"></p-multiSelect>
        </div>

        <div class="flex flex-column gap-2 mb-3">
            <label for="mainPhoneNumber" [style.color]="CheckValid(overviewForm.controls.mainPhone)">Main phone number
            </label>

            <div class="p-inputgroup w-50">
                <span class="p-inputgroup-addon py-0 pe-0">
                    <!-- (onChange)="updateMainPhoneNumber()" -->
                    <i class="pi pi-phone me-2" [ngStyle]="{ color: 'var(--green-500)' }"></i>
                    <p-dropdown [options]="allCountries.AllCOUNTRIES" id="allCOUNTRIES" optionLabel="mobileCode"
                        optionValue="mobileCode" class="p-inputtext-sm w-50 d-flex" [filter]="true"
                        filterBy="mobileCode" formControlName="mainPhoneCode"
                        [(ngModel)]="_OverViewData.PhoneNumber.CountryCode">

                        <ng-template let-option pTemplate="item">
                            <img src="assets/demo/flags/flag_placeholder.png"
                                [class]="'flag flag-' + option.code.toLowerCase()" alt="{{ option.name }}" />
                            <span>{{ option.mobileCode }}</span>
                        </ng-template>
                    </p-dropdown>
                </span>
                <!-- (onInput)="updateMainPhoneNumber()" -->
                <p-inputNumber [useGrouping]="false" inputId="mainPhoneNumber" class="p-inputtext-sm w-50"
                    formControlName="mainPhone" [(ngModel)]="_OverViewData.PhoneNumber.Number" />
            </div>
        </div>

        <div class="flex flex-column gap-2 mb-3">
            <label for="whatsappPhoneNumber" [style.color]="CheckValid(overviewForm.controls.whatsappNumber)"> Whatsapp
                phone number </label>
            <div class="p-inputgroup w-50">
                <span class="p-inputgroup-addon py-0 pe-0">
                    <i class="pi pi-whatsapp me-2" [ngStyle]="{ color: 'var(--green-500)' }"></i>
                    <!-- (onChange)="updateWhatsAppNumber()" -->
                    <p-dropdown [options]="allCountries.AllCOUNTRIES" id="allCOUNTRIES" optionLabel="mobileCode"
                        optionValue="mobileCode" class="p-inputtext-sm w-50 d-flex" [filter]="true"
                        filterBy="mobileCode" formControlName="whatsappNumberCode"
                        [(ngModel)]="_OverViewData.WhatsappNumber.CountryCode">

                        <ng-template let-option pTemplate="item">
                            <img src="assets/demo/flags/flag_placeholder.png"
                                [class]="'flag flag-' + option.code.toLowerCase()" alt="{{ option.name }}" />
                            <span>{{ option.mobileCode }}</span>
                        </ng-template></p-dropdown>
                </span>
                <!-- (onKeyDown)="updateWhatsAppNumber()" -->
                <p-inputNumber [useGrouping]="false" inputId="whatsappPhoneNumber" class="p-inputtext-sm w-50 d-flex"
                    formControlName="whatsappNumber" [(ngModel)]="_OverViewData.WhatsappNumber.Number" />
            </div>
        </div>
        <div class="flex flex-column gap-2 mb-3">
            <label for="companyActivityScope">Company activity scope</label>
            <p-dropdown [options]="activeScope" [(ngModel)]="selectedActiveScope" defaultLabel="Select Activity Scope"
                optionLabel="name" display="chip" id="companyActivityScope" (onChange)="changeActivityScope()"
                [disabled]="user.UserType != 0" [ngModelOptions]="{ standalone: true }"></p-dropdown>
        </div>
        <div class="flex flex-column gap-2 mb-3">
            <label for="countries">Country</label>
            <p-dropdown *ngIf="selectedActiveScope.id == 0" [options]="countries"
                [(ngModel)]="_OverViewData.Countries[0]" defaultLabel="Select countries" optionLabel="EnName"
                display="chip" id="dd_country" [disabled]="user.UserType != 0" [ngModelOptions]="{ standalone: true }"
                (onChange)="addCountry()"></p-dropdown>

            <p-multiSelect *ngIf="selectedActiveScope.id == 1" [options]="countries"
                [(ngModel)]="_OverViewData.Countries" defaultLabel="Select countries" optionLabel="EnName"
                display="chip" id="ms_countries" [disabled]="user.UserType != 0" (onChange)="addCountry()"
                [ngModelOptions]="{ standalone: true }"></p-multiSelect>
        </div>
        <div class="flex flex-column gap-2 mb-3">
            <label for="CompanyPin" [style.color]="CheckValid(overviewForm.controls.companyPin)">Company PIN</label>
            <input id="CompanyPin" pInputText [(ngModel)]="_OverViewData.CompanyPin" placeholder="Company PIN"
                class="p-inputtext-sm w-50" type="number" formControlName="companyPin" />
        </div>
        <div class="flex flex-column gap-2 mb-3">
            <label for="CompanyWebSite">Company WebSite</label>
            <input id="CompanyWebSite" formControlName="companyWebSite" type="text" pInputText
                placeholder="Company WebSite" [(ngModel)]="_OverViewData.CompanyWebSite" class="p-inputtext-sm w-50" />
            <span *ngIf="overviewForm.get('companyWebSite').hasError('invalidUrlError')"
                [style.color]="CheckValid(overviewForm.controls.companyWebSite)">
                Please enter a valid Company Website .
            </span>
        </div>
        <div class="flex flex-column gap-2 mb-3">
            <label for="about">English About</label>
            <textarea rows="5" pInputTextarea [(ngModel)]="_OverViewData.EnAbout"
                [ngModelOptions]="{ standalone: true }" id="enAbout"></textarea>

            <label for="about">Arabic About</label>
            <textarea rows="5" pInputTextarea [(ngModel)]="_OverViewData.ArAbout"
                [ngModelOptions]="{ standalone: true }" id="arAbout"></textarea>
        </div>
        <div class="flex justify-content-end gap-2 mb-3">
            <p-button label="Next" (click)="next()"></p-button>
        </div>
    </div>
</div>