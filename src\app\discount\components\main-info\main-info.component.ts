import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CompanyBranchModel } from 'src/app/Model/CompanyBranchModel';
import { CompanyModel } from 'src/app/Model/CompanyModel';
import { ConditionModel } from 'src/app/Model/ConditionModel';
import { DiscountModel } from 'src/app/Model/DiscountModel';
import { FilterModel } from 'src/app/Model/FiltersModel';
import { IndustryService } from 'src/app/services/industry.service';
import { ConditionService } from 'src/app/services/condition.service';
import { MessageService } from 'primeng/api';
import { environment } from 'src/environments/environment';
import { DomSanitizer } from '@angular/platform-browser';
import { ToastrService } from 'ngx-toastr';
import { TranslateService } from '@ngx-translate/core';
import { ActivatedRoute, Router } from '@angular/router';
import { RenewabilityType } from 'src/app/enum/renewbility-type';
import { CompanyTiers } from 'src/app/Model/CompanyTiers';
import { CompanyService } from 'src/app/services/company.service';
import { DiscountGroupInfoModel } from 'src/app/Model/DiscountGroupInfoModel';
import { DiscountValueEnum } from 'src/app/enum/discount-value-enum';
import { UserModel } from 'src/app/Model/UserModel';
import { EndpointUsedInEnum } from 'src/app/enum/endpoint-used-in-enum';
import { AuthService } from 'src/app/services/auth.service';


@Component({
    selector: 'app-main-info',
    templateUrl: './main-info.component.html',
    styleUrls: ['./main-info.component.scss'],

})
export class MainInfoComponent implements OnInit {
    @Input() _discountData: DiscountModel;
    discountType = [];
    discountValues: { id: string; name: string }[] = [];
    ProtectionType = [];
    companies: CompanyModel[] = [];
    CompanyTiers: CompanyTiers[] = [];
    conditions: ConditionModel[] = [];
    filters: FilterModel[] = [];
    companyBranchs: CompanyBranchModel[] = [];
    @Input() editing: boolean;
    @Input() activeIndex: number;
    @Output() activeIndexChange: EventEmitter<number> = new EventEmitter();
    pressedNext = false;
    companyProfileLogo: any;
    selectedCompany: CompanyModel = new CompanyModel();
    selectedCompanyCurrency: string = '$';
    imageUrl: any = '';
    user: UserModel;
    mainInfoForm = new FormGroup({
        //discountImage: new FormControl([], [Validators.required]),
        company: new FormControl('', [Validators.required]),
        discountEnTitle: new FormControl('', [Validators.required, Validators.minLength(3)]),
        discountArTitle: new FormControl('', [Validators.required, Validators.minLength(3)]),
        discountType: new FormControl('', [Validators.required]),
        discountValue: new FormControl('', [Validators.required]),
        estimatedSavings: new FormControl('', [Validators.required, Validators.min(1)]),
        originalPrice: new FormControl('', [Validators.required, Validators.min(1)]),
        protectionType: new FormControl('', [Validators.required]),
        SwipeCode: new FormControl('',),
        conditions: new FormControl(''),
        filters: new FormControl(''),
        EnTerms: new FormControl('', [Validators.required, Validators.minLength(3)]),
        ArTerms: new FormControl('', [Validators.required, Validators.minLength(3)]),
        relatedBranches: new FormControl('', [Validators.required]),
    });
    @Input() fileToUpload: any[];

    constructor(private companyService: CompanyService, private conditionService: ConditionService,
        private industryService: IndustryService, private messageService: MessageService, private _changeDetectorRef: ChangeDetectorRef, private sanitizer: DomSanitizer, private toastr: ToastrService, private translate: TranslateService, private route: ActivatedRoute, private router: Router, private authService: AuthService) {
    }
    ngOnInit(): void {
        this.user = this.authService.getUserData();
        if (this.user.UserType == 1) {
            this.mainInfoForm.get('company').clearValidators();
        }
        if (this._discountData.LogoUrl)
            // this.imageUrl = environment.imageSrc + this._discountData.LogoUrl;
            // disable sanitization to display image
            this.imageUrl = this._discountData.LogoUrl ? this.sanitizer.bypassSecurityTrustUrl(environment.imageSrc + this._discountData.LogoUrl) : '';
        this._discountData.CompanyId = null;

        this.companyService.GetAllId(EndpointUsedInEnum.Discount).subscribe((data) => {
            this.companies = data;
            if (this._discountData.Company) { // edit Discount
                this.selectedCompany = this.companies.find(x => x.Id == this._discountData.Company.Id);
                this._discountData.CompanyId = this._discountData.Company.Id;
                this.selectedCompanyCurrency = this.selectedCompany.selectedCompanyCurrency || '$';
                this.CompanyTiers = this.selectedCompany.CompanyTiers;
            }
            if (this.user.UserType == 1) {
                this.selectedCompany = this.companies[0];
                this.companyProfileLogo = environment.imageSrc + this.selectedCompany.LogoUrl;
                this.companyBranchs = this.companies[0].CompanyBranches;
                this._discountData.CompanyId = this.selectedCompany.Id;
                this.selectedCompanyCurrency = this.selectedCompany.selectedCompanyCurrency || '$';
                this.CompanyTiers = this.selectedCompany.CompanyTiers;
                this._discountData.Company = this.selectedCompany;
            }
            if (this._discountData.DiscountType == 0) {
                this._discountData.groupInfo = new DiscountGroupInfoModel();
                this.injectNormalRenewabilityOptions();
            }
            // Set Tiers (Normal Or Vip) to next step Timing & Details 
            if (this.route.snapshot.url[this.route.snapshot.url.length - 1].path === 'discount-new') {

                this._discountData.groupInfo = new DiscountGroupInfoModel();
            }
            if (this.route.snapshot.url[this.route.snapshot.url.length - 1].path === 'discount-edit') {

                if (!this._discountData.Id) {
                    this.router.navigate(['/discounts-inbox']);
                    return;
                }

                if (this._discountData.NormalTiersValues.length == 0) {
                    this.initNormalTiersValues();
                } else {
                    this.injectNormalRenewabilityOptions();
                }
                if (this._discountData.VipTiersValues.length == 0) {
                    this.initVipTiersValues();
                } else { this.injectVipRenewabilityOptions(); }
            }
        });
        this.companyBranchs = this._discountData.Branches;
        // if ( this._discountData.Branches.length > 0) {
        //     this.companyBranchs = this._discountData.Branches;
        //     // this._discountData.Branches = this.companyBranchs.filter(x => this._discountData.Branches.findIndex(y => y.Id == x.Id) != -1);
        // }

        if (this._discountData.Company && this._discountData.Company.LogoUrl) {
            this.companyProfileLogo = environment.imageSrc + this._discountData.Company.LogoUrl;
        }

        this.conditionService.GetAll().subscribe((data) => {
            this.conditions = data;
            // if (this._discountData.Conditions.length > 0) {
            //     // this._discountData.Conditions = this.conditions.filter(x => this._discountData.Conditions.findIndex(y => y.Id == x.Id) != -1)
            // }
        });
        this.industryService.GetAllFilters().subscribe((data) => {
            this.filters = data;
            // if (this._discountData.Filters.length > 0) {
            //     // this._discountData.Filters = this.filters.filter(x => this._discountData.Filters.findIndex(y => y.Id == x.Id) != -1)
            // }
        })

        this.discountType = [
            { id: 0, name: 'Normal Offer' },
            { id: 1, name: 'Group Offer' },
        ];
        this.discountValues = this.generateDiscountValuesArray();

        this.ProtectionType = [
            { id: 0, name: 'PIN Code' },
            { id: 1, name: 'Swipe' },
            // { id: 0, name: 'PIN' },
            // { id: 1, name: 'CODE' },
            // { id: 2, name: 'PASSWORD' },
        ];

        // Monitor the protectionType field and apply SWIP code validation when necessary
        this.mainInfoForm.get('protectionType')?.valueChanges.subscribe(value => {
            const swipCodeControl = this.mainInfoForm.get('SwipeCode');
            if (value == '1') {
                swipCodeControl?.setValidators([Validators.required, Validators.pattern('^[0-9]{6}$')]);
            } else {
                swipCodeControl?.clearValidators();
            }

            swipCodeControl?.updateValueAndValidity(); // Update control validity
        });
    }

    refreshCompanyRelatedDetails() {
        let selectedCompany = this.companies.find(x => x.Id == this._discountData.CompanyId);
        this._discountData.CompanyId = selectedCompany.Id;
        this._discountData.Company = selectedCompany;
        this.companyBranchs = selectedCompany.CompanyBranches;
        // this.companyProfileLogo  = environment.imageSrc + selectedCompany.LogoUrl.split("wwwroot\\")[1];
        this.companyProfileLogo = selectedCompany.LogoUrl ? this.sanitizer.bypassSecurityTrustUrl(environment.imageSrc + selectedCompany.LogoUrl) : '';
        // this.companyProfileLogo = selectedCompany.LogoUrl;
        this.selectedCompanyCurrency = selectedCompany.selectedCompanyCurrency || '$';
        this.selectedCompany = selectedCompany;
        setTimeout(() => {
            this.refreshTiersByCompany()
        });
    }
    next() {
        this.pressedNext = true;
        // Mark all controls as touched to trigger validation
        this.mainInfoForm.markAllAsTouched();

        if (!this.mainInfoForm.valid) {
            // this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Check your inputs' });
            // this.toastr.error('Check your inputs', this.translate.instant('error'));
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Check your input' });
            return;
        }
        this.refreshTiersByCompany()

        if (this.imageUrl == '') {
            // this.toastr.error('Please select image for this discount.', this.translate.instant('error'));
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please Upload image for this discount.' });
            return;
        }


        this.activeIndex++;
        this.activeIndexChange.emit(this.activeIndex);

    }

    CheckValid(input: FormControl) {

        if (input.invalid && (this.pressedNext || input.touched)) {
            return 'red';
        }
        return '#515C66';
    }

    uploadFile(files: any) {

        if (files.length === 0) {
            return;
        }
        var file = <File>files[0];
        const reader = new FileReader();
        this._discountData.Logo = file;
        reader.readAsDataURL(file);
        reader.onload = (e: any) => {
            this.imageUrl = e.target.result;
        }
        this.fileToUpload.push(file);
        this.imageUrl = URL.createObjectURL(this.fileToUpload[0]);
    }
    clearFileInput() {
        this.fileToUpload = [];
        this.imageUrl = '';
    }

    refreshTiersByCompany() {
        this.CompanyTiers = this.selectedCompany.CompanyTiers;
        this._discountData.IsVipTier = !this._discountData.IsNormalTier;
        this.initNormalTiersValues();
        this.initVipTiersValues();
        /* if (this.route.snapshot.url[this.route.snapshot.url.length - 1].path === 'discount-new') {
             this.initNormalTiersValues();
             this.initVipTiersValues();
 
         }
 
         else if (this._discountData.NormalTiersValues.length == 0) {
 
             this.initNormalTiersValues();
 
         }
 
         else if (this._discountData.VipTiersValues.length == 0) {
             this.initVipTiersValues();
         }
             */

    }


    generateDiscountValuesArray() {

        return Object.keys(DiscountValueEnum)
            .filter(key => isNaN(Number(key))) // Filter out the numeric keys added by TypeScript
            .map(key => {
                return {
                    id: DiscountValueEnum[key],
                    name: key
                };
            });

    }

    // This method updates the estimated saving when the original price changes
    updateEstimatedSaving(): void {
        var discountValue = this._discountData.DiscountValue;
        if (this._discountData.DiscountValue == 0) discountValue = 100; //B1G1
        if (this._discountData.OriginalPrice) {
            this._discountData.EstimatedSaving = Math.round((discountValue * this._discountData.OriginalPrice) / 100);
        } else {
            //  this._discountData.EstimatedSaving = 0;
        }
    }

    // This method updates the original price when the estimated saving changes
    updateOriginalPrice(): void {
        var discountValue = this._discountData.DiscountValue;
        if (this._discountData.DiscountValue == 0) discountValue = 100;  //B1G1
        if (this._discountData.EstimatedSaving) {
            this._discountData.OriginalPrice = Math.round((this._discountData.EstimatedSaving * 100) / discountValue);
        } else {
            //  this._discountData.OriginalPrice = 0;
        }
    }
    // Update both original price and estimated saving when the discount value changes
    updateOnDiscountChange(): void {
        if (this._discountData.OriginalPrice) {
            this.updateEstimatedSaving();
        } else if (this._discountData.EstimatedSaving) {
            this.updateOriginalPrice();
        }
    }

    initNormalTiersValues() {
        this._discountData.NormalTiersValues = this.CompanyTiers.filter(i => !i.IsVip).map(i => {
            return {
                TierId: i.Id,
                OffersNum: 0,
                RenewabilityType: RenewabilityType.Monthly,
                MonthlyRenewablility: i.MonthlyRenewablility,
                renewabilityOptions: this.getRenewabilityOptions(i),

                TotalOfferPerYear: 0,
                UnlimitedOffers: false,
                Tier: {
                    ...i,
                    Id: i.Id,
                    Checked: i.Checked,
                    ArName: i.ArName,
                    EnName: i.EnName,
                    IsVip: i.IsVip,
                    Rank: i.Rank,

                }
            }

        });

    }


    initVipTiersValues() {
        this._discountData.VipTiersValues = this.CompanyTiers.filter(i => i.IsVip).map(i => {
            return {
                TierId: i.Id,
                OffersNum: 0,
                RenewabilityType: RenewabilityType.Monthly,
                MonthlyRenewablility: i.MonthlyRenewablility,
                renewabilityOptions: this.getRenewabilityOptions(i),
                TotalOfferPerYear: 0,
                UsersNum: 0,
                UnlimitedOffers: false,
                Tier: {
                    Id: i.Id,
                    Checked: i.Checked,
                    ArName: i.ArName,
                    EnName: i.EnName,
                    IsVip: i.IsVip,
                    Rank: i.Rank
                }
            }

        });
    }

    getRenewabilityOptions(tier) {
        const options = [{ id: 0, name: 'Yearly' },];
        if (tier.MonthlyRenewablility) {
            options.push({ id: 1, name: 'Monthly' },);
        }
        return options;
    }

    injectNormalRenewabilityOptions(): void {
        this._discountData.NormalTiersValues = this._discountData.NormalTiersValues.map(tier => ({
            ...tier,
            renewabilityOptions: this.getRenewabilityOptions(tier)
        }));


    }
    injectVipRenewabilityOptions(): void {

        this._discountData.VipTiersValues = this._discountData.VipTiersValues.map(tier => ({
            ...tier,
            renewabilityOptions: this.getRenewabilityOptions(tier)
        }));
    }
}
