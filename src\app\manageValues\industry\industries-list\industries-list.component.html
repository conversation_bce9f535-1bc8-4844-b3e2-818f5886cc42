<div>
      <p-confirmDialog header="Confirmation" key="confirm1" icon="pi pi-exclamation-triangle"
            message="Are you sure you want to proceed?" [style]="{ width: '350px' }"
            acceptButtonStyleClass="p-button-text" rejectButtonStyleClass="p-button-text"></p-confirmDialog>


      <p-table #dt [value]="industries" [paginator]="paginator" [rows]="pageSize" [totalRecords]="totalRecords"
            [lazy]="true" [loading]="loading" (onLazyLoad)="loadData($event)" [showCurrentPageReport]="true"
            [currentPageReportTemplate]="'Showing {first} to {last} of {totalRecords} records'"
            responsiveLayout="scroll" [rowHover]="true" styleClass="p-datatable-gridlines">

            <!-- (click)=" add()" -->
            <ng-template *ngIf="user.UserType == 0" pTemplate="caption">
                  <app-grid-headers [myDt]="industries" (SearchEvent)='ReceivedFilteredData($event)'
                        addNewTxt="Add New Industry" goRoute="/industry-new" gridTitle="Industries" [title]="title">
                  </app-grid-headers>
            </ng-template>
            <ng-template pTemplate="header">
                  <tr>
                        <th>
                              <div class="flex justify-content-between align-items-center my-center-text">
                                    {{ "ID" | translate }}
                                    <p-sortIcon field="ID" class="my-center-text"></p-sortIcon>
                                    <!-- <p-columnFilter type="text" field="ID" display="menu"
                                          class="ml-auto my-center-text"></p-columnFilter> -->
                              </div>
                        </th>
                        <th>
                              <div class="flex justify-content-between align-items-center">
                                    {{ "Industry Name" | translate }}
                                    <p-sortIcon field="EnName"></p-sortIcon>
                                    <!-- <p-columnFilter type="text" field="EnName" display="menu"
                                          class="ml-auto"></p-columnFilter> -->
                              </div>
                        </th>
                        <th>
                              <div class="flex justify-content-between align-items-center">
                                    {{ "Filters" | translate }}
                              </div>
                        </th>

                        <th>
                              <div class="flex justify-content-between align-items-center">
                                    {{ "Actions" | translate }}
                              </div>
                        </th>
                  </tr>
            </ng-template>

            <ng-template pTemplate="body" let-industry style="text-align: right;">

                  <tr>

                        <td>
                              <div class="flex align-items-center">
                                    {{ industry.UserFriendly }}
                              </div>

                        </td>

                        <td>
                              <div class="flex align-items-center">
                                    <p-avatar *ngIf="industry.LogoUrl" [alt]="industry.EnName"
                                          image="{{imageSrc + industry.LogoUrl}}" styleClass="mr-2" size="medium"
                                          shape="circle"></p-avatar>
                                    {{ industry.EnName }}
                              </div>
                        </td>
                        <td>
                              <div class="flex align-items-center">
                                    <span *ngFor="let filter of industry.Filters; let i = index; let last = last"> {{
                                          filter.EnName }} <span *ngIf="
                                          !last">,</span> </span>
                              </div>
                        </td>
                        <td>
                              <div class="flex">
                                    <div class="d-flex align-items-center justify-content-between">
                                          <p-button icon="pi pi-pencil" styleClass="p-button-rounded" class="mx-1"
                                                (click)="
                                    edit(industry, 'edit' )"></p-button>
                                          <p-button *ngIf="user.UserType == 0" icon="pi pi-trash"
                                                styleClass="p-button-rounded p-button-danger" class="mx-1"
                                                (click)="delete(industry.Id)"></p-button>
                                    </div>
                              </div>
                        </td>
                  </tr>

            </ng-template>
            <ng-template pTemplate="emptymessage">
                  <tr>
                        <td class="text-center" colspan="7">
                              {{ "No Industries found." | translate }}
                        </td>
                  </tr>
            </ng-template>
      </p-table>
</div>