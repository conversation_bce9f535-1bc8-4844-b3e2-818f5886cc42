coupon-status {
      border-radius: var(--border-radius);
      padding: 0.25em 0.5rem;
      text-transform: uppercase;
      font-weight: 700;
      font-size: 12px;
      letter-spacing: 0.3px;
}

.coupon-status.status-0 {
      @extend coupon-status;
      background: #0c29e8;
      color: #ffffff;
}

.coupon-status.status-1 {
      @extend coupon-status;
      background: #219c36;
      color: #ffffff;
}

.coupon-status.status-2 {
      @extend coupon-status;
      background: #e8220c;
      color: #ffffff;
}

.coupon-status.status-3 {
      @extend coupon-status;
      background: #0ce85d;
      color: #ffffff;
}

.coupon-status.status-4 {
      @extend coupon-status;
      background: #f53a1d;
      color: #ffffff;
}
//     .coupon-status.status-renewal {
//       background: #ECCFFF;
//       color: #694382;
//     }
//     .coupon-status.status-proposal {
//       background: #FFD8B2;
//       color: #805B36;
//     }