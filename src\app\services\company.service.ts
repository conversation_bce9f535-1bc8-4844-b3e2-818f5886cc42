import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { CompanyModel } from '../Model/CompanyModel';
import { Dto } from '../Model/Dto';
import { environment } from 'src/environments/environment';
import { Observable, catchError, map, of } from 'rxjs';

import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class CompanyService {


  constructor(private httpClient: HttpClient, private authService: AuthService) {
  }
  GetAllId( EndpointUsedIn = null) {
    let params = new HttpParams();;
    if (EndpointUsedIn != null) {
      params = params.set('EndpointUsedIn', EndpointUsedIn);
    }
    return this.httpClient.get<Dto<CompanyModel>>(`${environment.apiUrl}` + 'Company/GetAllId', { params })
      .pipe(
        map((res: any) => {
          if (!res.HasError) {
            return res.ListResultContent;
          }
        }),
        catchError((error) => {

          return of(false);
        })
      );
  }

  public GetAll(page: number = null, pageSize: number = null, companyName: string = '', cityName: string = '', IndustryId: string = '') {
    var http;
    var url = `${environment.apiUrl}Company/GetAll`;
    // var params = {};
    // if (page != null) {
    let params = new HttpParams();;
    if (page) {
      params = params.set('PageNumber', page);
    }
    if (pageSize) {
      params = params.set('PageSize', pageSize);
    }
    if (companyName) {
      params = params.set('CompanyName', companyName);
    }
    if (IndustryId) {
      params = params.set('IndustryId', IndustryId);
    }
    if (cityName) {
      params = params.set('MainBranchCity', cityName);
    }
    // /}
    http = this.httpClient.get(url, { params });
    return http.pipe(
      map((res: Dto<CompanyModel>) => {
        if (res.HasError) {
          return of(false);
        } else {
          return res;
        }
      }),
      catchError((error) => {

        return of(false);
      })
    );
  }

  public GetCompanyById(Id: string = "") {
    var url = `${environment.apiUrl}Company/GetCompanyById?Id=${Id}`;
    var http = this.httpClient.get(url);
    return http.pipe(
      map((res: Dto<CompanyModel>) => {
        return res.ResultContent;
      }),
      catchError((error) => {
        return of(false);
      })
    );
  }
  public AddCompany(data): Observable<Dto<CompanyModel>> {
    var http;
    var url = `${environment.apiUrl}Company/AddCompany`;
    http = this.httpClient.post(
      url,
      data
    );
    return http.pipe(
      map((res: Dto<CompanyModel>) => {
        return res;
      }),
      catchError((error) => {

        return of(false);
      })
    );
  }

  // NOT USED
  // public saveCompanyPics(data: any): Observable<any> {
  //   var http;
  //   var url = `${environment.apiUrl}Company/SaveCompanyPics`;
  //   http = this.httpClient.post(url, data);
  //   return http.pipe(
  //     map((res: any) => {
  //        
  //       if (res.HasError) {
  //         return of(false);
  //       } else {
  //         return res;
  //       }
  //     }),
  //     catchError((error) => {
  //       
  //       return of(false);
  //     })
  //   );
  // }


  public GetCompanyByUser() {
    var http;
    var url = `${environment.apiUrl}Company/GetCompanyByUser`;
    http = this.httpClient.get(url);
    return http.pipe(
      map((res: Dto<CompanyModel>) => {
        return res.ResultContent;
      }),
      catchError((error) => {

        return of(false);
      })
    );
  }
  public EditCompany(data): Observable<Dto<CompanyModel>> {
    // const headers = new HttpHeaders({ 'Content-Type': 'application/json' });
    // const headers = new HttpHeaders().set('Content-Type', 'application/json');
    // const options = { headers: headers };
    // const headers = new HttpHeaders({
    //     'Content-Type': 'multipart/form-data'
    //   });
    // const headers = new HttpHeaders();
    // headers.append('Content-Type', 'multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW');

    var http;
    var url = `${environment.apiUrl}Company/EditCompany`;
    http = this.httpClient.put(url, data);
    return http.pipe(
      map((res: Dto<CompanyModel>) => {
        return res;
      }),
      catchError((error) => {
        return of(false);
      })
    );
  }

  public DeleteCompany(companyId: String, OverrideRunningDiscountsWarning: boolean = false): Observable<any> {
    var http;
    var url = `${environment.apiUrl}Company/DeleteCompany?CompanyId=${companyId}&OverrideRunningDiscountsWarning=${OverrideRunningDiscountsWarning}`;
    http = this.httpClient.delete(url);
    return http.pipe(
      map((res: any) => {
        return res;
      }),
      catchError((error) => {
        return of(false);
      })
    );
  }
  // NOT USED
  // public SearchByCompanyName(companyName: String): Observable<any> {
  //   var http;
  //   var url = `${environment.apiUrl}Company/SearchByCompanyName?companyName=${companyName}`;
  //   http = this.httpClient.get(url);
  //   return http.pipe(
  //     map((res: any) => {
  //        
  //       return res;
  //     }),
  //     catchError((error) => {
  //       
  //       return of(false);
  //     })
  //   );
  // }

  public EditActiveCompany(companyId: String, OverrideRunningDiscountsWarning: boolean = false): Observable<any> {
    var http;
    var url = `${environment.apiUrl}Company/EditCompanyActive?CompanyId=${companyId}&OverrideRunningDiscountsWarning=${OverrideRunningDiscountsWarning}`;
    http = this.httpClient.patch(url, {});
    return http.pipe(
      map((res: any) => {
        return res;
      }),
      catchError((error) => {
        return of(false);
      })
    );
  }


  public DeleteCompanyBranch(branchId: String, OverrideRunningDiscountsWarning: boolean = false): Observable<any> {
    var http;
    var url = `${environment.apiUrl}CompanyBranch/DeleteCompanyBranch?Id=${branchId}&OverrideRunningDiscountsWarning=${OverrideRunningDiscountsWarning}`;
    http = this.httpClient.delete(url);
    return http.pipe(
      map((res: any) => {
        return res;
      }),
      catchError((error) => {
        return of(false);
      })
    );
  }

  public EditCompanyBranchActive(branchId: String, OverrideRunningDiscountsWarning: boolean = false): Observable<any> {
    var http;
    var url = `${environment.apiUrl}CompanyBranch/EditCompanyBranchActive?Id=${branchId}&OverrideRunningDiscountsWarning=${OverrideRunningDiscountsWarning}`;
    http = this.httpClient.patch(url, {});
    return http.pipe(
      map((res: any) => {
        return res;
      }),
      catchError((error) => {
        return of(false);
      })
    );
  }
}
