<h3>
      <p-button icon="pi pi-arrow-left" styleClass="p-button-rounded p-button-secondary p-button-text"
            routerLink="/company-admins-management">
      </p-button>
      {{title}}
</h3>
<div class="grid p-fluid">
      <div class="col-12 md:col-6">
            <p-card class="container">
                  <div class="row" [formGroup]="CompanyAdminForm">
                        <div class="col-md-1" style="margin-right: 9%;">
                              <div class="text-center">
                                    <div class="image-avatar-container">
                                          <label for="image" class="w-100">
                                                <img [src]="
                                                      ProfilePhotoUrl| placeholder: 'assets/layout/images/upload-image.png' "
                                                      class="roundeisSelectedFiled-circle w-100" alt="..." />
                                                <p-button icon="pi pi-pencil" class="edit-button"
                                                      styleClass="p-button-rounded" (click)="file.click()"></p-button>
                                          </label>
                                          <input type="file" id="ProfilePhotoUrl" class="d-none" (change)="uploadFile(file.files)"
                                          accept="image/*" #file formControlName="ProfilePhotoUrl" />
                                    </div>
                                    <br>
                                    <label *ngIf="pressedSave && !this.ProfilePhotoUrl" for="logoImage"
                                          style="color: red">Please
                                          upload
                                          Profile Photo</label>
                              </div>
                        </div>

                        <div class="col-md-8">
                              <div class="flex flex-column gap-2 mb-4" *ngIf="currentUser.UserType != 1">
                                    <p-avatar [image]="
                                    companyProfileLogo
                                        | placeholder
                                            : 'assets/layout/images/upload-image.png'
                                " styleClass="mr-2" size="xlarge" shape="circle"></p-avatar>
                                    <span class="flex flex-column gap-2 mb-3">
                                          <label for="company" [style.color]="
                                        CheckValid(CompanyAdminForm.controls.Company)
                                    ">Company name</label>
                                          <p-dropdown [options]="companies" [(ngModel)]="CompanyAdmin.CompanyId"
                                                placeholder="select Company" optionLabel="EnName" optionValue="Id"
                                                class="p-inputtext-sm w-50" id="company"
                                                (onChange)="refreshCompanyRelatedDetails()"
                                                formControlName="Company"></p-dropdown>
                                    </span>
                              </div>

                              <div class="flex flex-column gap-3 mb-4">
                                    <label for="Name" [style.color]="CheckValid(CompanyAdminForm.controls.Name)">Admin
                                          Name</label>
                                    <input id="Name" type="text" pInputText placeholder="Name"
                                          [(ngModel)]="CompanyAdmin.Name" class="p-inputtext-sm w-50"
                                          formControlName="Name" />

                              </div>
                              <div class="flex flex-column gap-2 mb-4">
                                    <label for="Email" [style.color]="CheckValid(CompanyAdminForm.controls.Email)">Admin
                                          Email</label>
                                    <!-- [Email]="true" -->
                                    <input id="Email" (focusout)="onEmailInputFocusOut()" type="Email" pInputText
                                          placeholder="Admin Email" [(ngModel)]="CompanyAdmin.Email"
                                          class="p-inputtext-sm w-50" formControlName="Email" #EmailInput />
                                    <span *ngIf="CompanyAdminForm.get('Email').hasError('invalidEmailError')"
                                          [style.color]="CheckValid(CompanyAdminForm.controls.Email)">
                                          Please enter a valid Email .
                                    </span>
                                    <!-- autocomplete='false' -->
                                    <!-- </form> -->
                              </div>
                              <div class="flex flex-column gap-2 mb-4">
                                    <label *ngIf="!isEditing" for="adminPassword"
                                          [style.color]="CheckValid(CompanyAdminForm.controls.Password)">Admin
                                          Password
                                    </label>
                                    <label *ngIf="isEditing" for="adminPassword">Admin Password
                                    </label>
                                    <p-password [(ngModel)]="CompanyAdmin.Password" class="p-inputtext-sm w-50"
                                          [toggleMask]="true" formControlName="Password" id="adminPassword"
                                          placeholder="Admin Password" #PasswordInput></p-password>
                                    <span *ngIf="CompanyAdminForm.get('Password').hasError('pattern')"
                                          [style.color]="CheckValid(CompanyAdminForm.controls.Password)">
                                          Password must contain at least one lowercase letter, one uppercase
                                          letter, one
                                          digit, and one special character ($@$!%*?&), and be at least 8
                                          characters
                                          long.
                                    </span>
                              </div>
                              <div class="flex flex-column gap-2 mb-4">
                                    <label for="type"
                                          [style.color]="CheckValid(CompanyAdminForm.controls.AdminType)">Admin
                                          Type</label>
                                    <p-dropdown [options]="adminTypes" [(ngModel)]="CompanyAdmin.AdminType"
                                          class="p-inputtext-sm w-50" placeholder="select Admin Type"
                                          formControlName="AdminType"></p-dropdown>
                              </div>

                              <div class="flex flex-column gap-2 mb-4" *ngIf="CompanyAdmin.AdminType == 1">
                                    <label for="AdministeredBranches" [style.color]="
                                    CheckValid(CompanyAdminForm.controls.AdministeredBranches)"> Branches</label>
                                    <a *ngIf="CompanyAdmin.AdministeredBranches.length==0 && pressedSave && CompanyAdmin.AdminType == 1"
                                          style="color: red">Select One Branch at least </a>
                                    <p-multiSelect [options]="this.companyBranchs"
                                          [(ngModel)]="CompanyAdmin.AdministeredBranches" defaultLabel="Select branches"
                                          optionLabel="EnName" display="chip" id="AdministeredBranches"
                                          class="p-inputtext-sm w-50"
                                          formControlName="AdministeredBranches"></p-multiSelect>
                              </div>
                              <div class="flex flex-column gap-2 mb-4" *ngIf="CompanyAdmin.AdminType == 1">
                                    <label for="AdministeredPages"
                                          [style.color]="CheckValid( CompanyAdminForm.controls.AdministeredPages)">Administered
                                          Pages</label>
                                    <a *ngIf="CompanyAdmin.AdministeredPages.length==0 && pressedSave && CompanyAdmin.AdminType == 1"
                                          style="color: red">Pick One At Least</a>
                                    <p-card class="container">
                                          <div class="row">
                                                <div class="col-md-6" *ngFor="let column of columns">
                                                      <div *ngFor="let page of column" class="field-checkbox">
                                                            <p-checkbox [value]="page?.value" inputId="{{page?.name}}"
                                                                  [(ngModel)]="CompanyAdmin.AdministeredPages"
                                                                  formControlName="AdministeredPages"
                                                                  [ngModelOptions]="{standalone: true}">
                                                            </p-checkbox>
                                                            &nbsp;
                                                            <i [class]="CompanyAdminPageEnumIconMap[page?.value]"></i>
                                                            <label for="{{page?.name}}">{{page?.name}}</label>
                                                      </div>
                                                </div>
                                          </div>
                                    </p-card>
                              </div>
                        </div>

                        <br />


                        <br />
                        <div class="flex justify-content-end gap-2 mb-3">
                              <p-button label="save" (onClick)="save()"></p-button>
                              <!-- [disabled]="!this.timingDetailsForm.valid" -->
                        </div>
                  </div>
            </p-card>
      </div>
</div>