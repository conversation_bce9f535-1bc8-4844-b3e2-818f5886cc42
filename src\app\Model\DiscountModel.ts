import { DayOfWeek } from "../enum/day-of-week";
import { DiscountDayType } from "../enum/discount-day-type";
import { DiscountPeriod } from "../enum/discount-period";
import { DiscountType } from "../enum/discount-type";
import { DiscountValueEnum } from "../enum/discount-value-enum";
import { ProtectionType } from "../enum/protection-type";
import { CompanyBranchModel } from "./CompanyBranchModel";
import { CompanyModel } from "./CompanyModel";
import { DiscountNormalTier } from "./DiscountNormalTier";
import { DiscountVipTier } from "./DiscountVipTier";
import { ConditionModel } from "./ConditionModel";
import { DiscountBranchesModel } from "./DiscountBranchesModel";
import { DiscountConditionsModel } from "./DiscountConditionsModel";
import { DiscountDaysModel } from "./DiscountDaysModel";
import { DiscountFiltersModel } from "./DiscountFiltersModel";
import { DiscountGroupInfoModel } from "./DiscountGroupInfoModel";
import { DiscountStatus } from "./DiscountStatus";
import { FilterModel } from "./FiltersModel";
import { TiersModel } from "./TiersModel";
import { DiscountStatusEnum } from "../enum/discount-status-enum";

export class DiscountModel {
    constructor() {
    }
    Id: string = "";
    EnTitle: string = "";
    ArTitle: string = "";
    Value: number = 0.0;
    DiscountType: DiscountType;
    DiscountPeriod: DiscountPeriod;
    DiscountValue: DiscountValueEnum = 0; //B1G1 by default 
    EstimatedSaving: number = 0;
    OriginalPrice: number = 0;
    Renewability: boolean;
    bool: boolean;
    SwipeCode: string = "";
    ProtectionType: ProtectionType;
    EnTerms: string = "";
    ArTerms: string = "";
    UserFriendly: number = null;

    StartDate: Date;
    EndDate: Date;
    UpdatedDate?: Date;
    OfferStartTime: string = "";
    OfferEndTime: string = "";
    AvailableWeekDay: String = "";
    AvailableHours: String = "";
    Status: DiscountStatusEnum;
    ApprovedByAdmin: boolean = false;
    Active: boolean = false;
    ApprovedBySuperAdmin: boolean = false;

    //Tier
    IsNormalTier: boolean = true;
    IsVipTier: boolean = false;
    CompanyTiers: TiersModel[];
    TiersList: TiersModel[];

    NormalTiersValues: DiscountNormalTier[];
    VipTiersValues: DiscountVipTier[];


    //Group Offer
    groupInfo: DiscountGroupInfoModel;

    Company?: CompanyModel;
    Filters: FilterModel[] = [];
    Branches: CompanyBranchModel[] = [];
    Conditions: ConditionModel[] = [];
    DayOfWeeks: DayOfWeek[] = [];
    DiscountDayType: DiscountDayType = DiscountDayType.AllDays;

    DiscountBranches?: DiscountBranchesModel[];
    DiscountFilters?: DiscountFiltersModel[];
    DiscountDays?: DiscountDaysModel[];
    DiscountConditions?: DiscountConditionsModel[];
    DiscountStatuses?: DiscountStatus[];
    NoteDiscount?: string = "";
    //region NotMapped
    CompanyId?: string;
    VisibleDialog = false;
    //Image
    LogoUrl: string = "";
    Logo: File;
    
    hasSetNumTier: boolean = false;
}
