export class TiersModel {
    Id: string = "";
    ArName: string = "";
    EnName: string = "";
    Rank: number = 0;
    IsVip?: boolean;
    UserStatistics? = [];
    TotalEndUsers?: number = 0;
    TotalCountries?: number = 0;
    TotalUsers?: number = 0;
    PointsPerYear?: number = 0;
    PricePerYear?: number = 0;
    MonthlyRenewablility?: boolean = false;
    Color?: string = '#8E8E8E';
    //NotMapped
    Checked?: boolean = false;
    disabled?: boolean = false;  //set disabled checkbox to tiers level-up
    HasError?: boolean = false;
}
