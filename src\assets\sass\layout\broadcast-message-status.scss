broadcast-message-status {
      border-radius: var(--border-radius);
      padding: 0.25em 0.5rem;
      text-transform: uppercase;
      font-weight: 700;
      font-size: 12px;
      letter-spacing: 0.3px;
      
}

.broadcast-message-status.status-0 {
      @extend broadcast-message-status;
      background: #0c29e8;
      color: #ffffff;
      
}

.broadcast-message-status.status-1 {
      @extend broadcast-message-status;
      background: #219c36;
      color: #ffffff;
}

.broadcast-message-status.status-2 {
      @extend broadcast-message-status;
      background: #e8220c;
      color: #ffffff;
}

.broadcast-message-status.status-3 {
      @extend broadcast-message-status;
      background: #0ce85d;
      color: #ffffff;
}

// .broadcast-message-status.status-4 {
//       background: #f53a1d;
//       color: #ffffff;
// }
//     .broadcast-message-status.status-renewal {
//       background: #ECCFFF;
//       color: #694382;
//     }
//     .broadcast-message-status.status-proposal {
//       background: #FFD8B2;
//       color: #805B36;
//     }