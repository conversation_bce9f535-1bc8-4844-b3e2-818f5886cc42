<p-toast></p-toast>

<div class="row" [formGroup]="SubscriptionMainInfoForm">
      <div class="container row col-md-8">
            <div class="flex flex-column gap-3 mb-4">
                  <label for="EnName" [style.color]="CheckValid(SubscriptionMainInfoForm.controls.EnName)">English
                        Name</label>
                  <input id="EnName" type="text" pInputText placeholder="Subscription English Name"
                        [(ngModel)]="_SubscriptionData.EnName" class="p-inputtext-sm w-50" formControlName="EnName" />

            </div>
            <div class="flex flex-column gap-3 mb-4">
                  <label for="ArName" [style.color]="CheckValid(SubscriptionMainInfoForm.controls.ArName)"> Arabic
                        Name</label>
                  <input id="ArName" type="text" pInputText placeholder="Subscription Arabic Name"
                        [(ngModel)]="_SubscriptionData.ArName" class="p-inputtext-sm w-50" formControlName="ArName" />

            </div>
      </div>
      <br />
      <br />
      <div class="flex justify-content-end gap-2 mb-3">
            <p-button label="save" (onClick)="save()"></p-button>
            <!-- [disabled]="!this.timingDetailsForm.valid" -->
      </div>
</div>