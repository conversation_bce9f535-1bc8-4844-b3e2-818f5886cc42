import { Component, Input, OnInit, ViewChild } from "@angular/core";
import { MessageService, ConfirmationService } from "primeng/api";
import { Router } from "@angular/router";
import { ConfirmationDialogComponent } from "src/app/components/confirmation-dialog/confirmation-dialog.component";
import { TiersModel } from "src/app/Model/TiersModel";
import { TierService } from "src/app/services/tier.service";

@Component({
    selector: "app-tierlist",
    templateUrl: "./tiers-list.component.html",
    styleUrls: ["./tiers-list.component.scss"],
    providers: [MessageService, ConfirmationService],
})
export class TiersListComponent implements OnInit {
    @Input() _Tier: TiersModel;
    Tiers?: TiersModel[];
    Tier: TiersModel = new TiersModel();
    loading: boolean = true;
    formData: TiersModel;
    title: string;

    warningMessage: string = '';
    @ViewChild('confirmationDialog') confirmationDialog: ConfirmationDialogComponent;
    confirmationAction: string = '';
    OverrideWarning: boolean = false;


    totalRecords: number = 0;
    pageSize: number = 10;
    pageIndex: number = 0;
    paginator: boolean = false;

    //filters
    Name: string = '';

    //  track the first load
    private isInitialLoad: boolean = false;


    constructor(
        private TierService: TierService,
        private router: Router,
        private messageService: MessageService,) { }

    ngOnInit(): void {
        this.loadData({ first: 0, rows: this.pageSize });
        this.isInitialLoad = true;
        this.title = "Tiers Management";
    }

    loadData(event: any) {
        // Avoid double loading during the first render
        if (this.isInitialLoad) {
            this.isInitialLoad = false; // Set the flag to false after the first load
            return;
        }
        const pageNumber = event.first / event.rows + 1; // Calculate the page number
        const pageSize = event.rows; // Rows per page
        this.paginator = true;

        this.fetchTiersData(pageNumber, pageSize);

    }

    fetchTiersData(pageNumber, pageSize) {
        this.TierService
            .getAllTiersTable(pageNumber, pageSize, this.Name)
            .subscribe((data) => {
                this.Tiers = data.ListResultContent;
                this.totalRecords = data.TotalRecords; // Set total records for pagination
                this.loading = false;
            });
    }

    edit(e: { Tier?: TiersModel; state: string }) {

        this.formData = e.Tier;

        this.TierService.getTierById(e.Tier.Id.toString()).subscribe((data) => {
            this.router.navigate(["tier-edit"], {
                state: {
                    data: data,
                    command: "edit",
                },
            });
        });


    }
    ReceivedFilteredData(event) {
        this.Name = event.Name;
        this.fetchTiersData(1, this.pageSize);

        this.Tiers = event.ListResultContent;
        this.totalRecords = event.TotalRecords; // Set total records for pagination
        //this.paginator = false;

    }

    delete(Tier) {
        this.Tier = Tier;
        if (Tier.TotalCountries || Tier.TotalUsers) {
            this.messageService.add({ severity: 'error', summary: 'Error', detail: `Cannot delete this Tier. It has ${Tier.TotalUsers} associated users and It has ${Tier.TotalCountries} associated Countries.` });
            return;
        }
        this.confirmationDialog.message = 'Do you want to delete this Tier ' + this.Tier.EnName;
        this.confirmationDialog.item = this.Tier;
        this.confirmationDialog.openDialog();
        this.confirmationAction = 'delete';
    }

    // Method to handle the confirmation result
    handleConfirmationAction(result: boolean): void {
        if (this.confirmationAction == 'delete') {
            if (result) {
                this.TierService.DeleteTier(this.Tier.Id).subscribe((data) => {
                    if (data['HasError'] == false) {
                        this.messageService.add({
                            severity: "success",
                            summary: "Success Message",
                            detail: "Delete Tier Successfully",
                        });
                        // Update the data array (remove the deleted item)
                        this.Tiers = this.Tiers.filter(currentItem => currentItem.Id !== this.Tier.Id);
                        this.Tier = new TiersModel();
                        this.confirmationAction = '';

                    } else {
                        this.confirmationDialog.item = this.Tier;
                        this.confirmationDialog.openDialog();
                        this.confirmationDialog.message = data['EnErrorMessage'];
                    }
                });
            } else {
                this.confirmationAction = '';
                this.Tier = new TiersModel();
            }

        }

    }
}
