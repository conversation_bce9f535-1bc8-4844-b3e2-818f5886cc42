<p-table #dt [paginator]="true" [rows]="5" responsiveLayout="scroll" [value]="SubscriptionDataArray"
      responsiveLayout="scroll" [rowHover]="true" styleClass="p-datatable-gridlines">

      <ng-template pTemplate="header">
            <tr>
                  <th>
                        <div class="flex"> Total Countries</div>
                  </th>
                  <th>
                        <div class="flex">Total Companies</div>
                  </th>
            </tr>
      </ng-template>
      <ng-template pTemplate="body" let-rowData>
            <tr>
                  <td>
                        <div class="flex">{{rowData.TotalCountries}} </div>
                  </td>
                  <td>
                        <div class="flex">{{rowData.TotalCompanies }}</div>
                  </td>
            </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
            <tr>
                  <td class="text-center" colspan="7">
                        {{ "No Linked Countries." | translate }}
                  </td>
            </tr>
      </ng-template>
</p-table>

<p-table #dt [value]="_SubscriptionData.CompanyStatistics" [paginator]="true" [rows]="5" responsiveLayout="scroll"
      [rowHover]="true" styleClass="p-datatable-gridlines">
      <ng-template pTemplate="header">
            <tr>
                  <th>
                        <div class="flex">Country </div>
                  </th>
                  <th>
                        <div class="flex">Total Companies</div>
                  </th>
            </tr>
      </ng-template>
      <ng-template pTemplate="body" let-rowData let-Statistic>
            <tr>
                  <td>
                        <div class="flex">
                              <img src="assets/demo/flags/flag_placeholder.png"
                                    [class]="'flag flag-' +  this.allCountries.getCodeFromName( Statistic.Country?.EnName)"
                                    alt="{{ Statistic.Country?.EnName }}" />
                              &nbsp;
                              {{Statistic.Country?.EnName}}
                        </div>
                  </td>
                  <td>
                        <div class="flex">
                              {{Statistic.TotalCompanies }}
                        </div>
                  </td>
            </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
            <tr>
                  <td class="text-center" colspan="7">
                        {{ "No Linked Countries." | translate }}
                  </td>
            </tr>
      </ng-template>
</p-table>