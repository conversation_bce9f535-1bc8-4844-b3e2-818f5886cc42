<div>
    <p-toast></p-toast>
    <p-card>
        <div class="row">
            <div class="col d-flex justify-content-start">
                <label>{{ title | translate}}</label>
            </div>
            <div class="col d-flex justify-content-end">
                <!--  [label]="addNewTxt" -->
                <p-button *ngIf="addNewTxt!=''" label="{{ addNewTxt | translate}}" icon="pi pi-plus"
                    styleClass="p-button-outlined" [routerLink]="goRoute"></p-button>
            </div>
        </div>
        <div class="table-header row">
            <div class="col-3" *ngIf="gridTitle=='Companies'|| gridTitle=='Broadcast Messages'">
                <app-grid-item-input-text label=" {{ 'What are looking for ?' | translate }}" icon="pi pi-search"
                    placeholder="{{ 'Search by company Name or Title' | translate }}"
                    (myValueChange)="titleNameReceivedValue($event)"></app-grid-item-input-text>
            </div>

            <div class="col-3" *ngIf="gridTitle=='malls'">
                <app-grid-item-input-text label=" {{ 'What are looking for ?' | translate }}" icon="pi pi-search"
                    placeholder="{{ 'Search by Mall Name' | translate }}"
                    (myValueChange)="titleNameReceivedValue($event)"></app-grid-item-input-text>

            </div>
            <!-- CompanyAdminsManagment -->
            <div class="col-3" *ngIf="gridTitle=='CompanyAdminsManagement'||gridTitle=='WebsiteAdminsManagement'">
                <app-grid-item-input-text label=" {{ 'What are looking for ?' | translate }}" icon="pi pi-search"
                    placeholder="{{ 'Search by Name' | translate }}"
                    (myValueChange)="titleNameReceivedValue($event)"></app-grid-item-input-text>

            </div>
            <div class="col-3" *ngIf="gridTitle=='CompanyAdminsManagement'||gridTitle=='WebsiteAdminsManagement'">
                <app-grid-item-input-dropdown label="{{ 'Type' | translate }}" listType="AdminType"
                    (selectedAdminTypeEventEmitter)="adminTypeRecievedValue($event)"></app-grid-item-input-dropdown>
            </div>
            <div class="col-3" *ngIf="gridTitle=='CompanyAdminsManagement'">
                <app-grid-item-input-dropdown label="{{ 'Company' | translate }}" listType="Company"
                    (selectedCompanyEventEmitter)="companyRecievedValue($event)"></app-grid-item-input-dropdown>
            </div>
            <!-- WebsiteAdminsManagement -->
            <div class="col-3" *ngIf="gridTitle=='WebsiteAdminsManagement'">
                <app-grid-item-input-dropdown label="{{ 'Country' | translate }}" listType="Country"
                    (selectedCountryEventEmitter)="countryRecievedValue($event)"></app-grid-item-input-dropdown>
            </div>
            <!-- Industries -->
            <div class="col-3" *ngIf="gridTitle=='Industries'">
                <app-grid-item-input-text label="{{ 'What are looking for ?' | translate }}" icon="pi pi-search"
                    placeholder="{{ 'Search by Industry Name' | translate }}"
                    (myValueChange)="industryNameReceivedValue($event)"></app-grid-item-input-text>
            </div>
            <div class="col-3" *ngIf="gridTitle=='Companies'">
                <app-grid-item-input-dropdown label="{{ 'Company Industry' | translate }}" listType="Industry"
                    (selectedIndustryEventEmitter)="industryRecievedValue($event)"></app-grid-item-input-dropdown>
            </div>
            <!-- <div class="col-1"></div> -->

            <div class="col-3" *ngIf="gridTitle=='Companies'">
                <app-grid-item-input-text label="{{ 'City' | translate }}" icon="pi pi-search"
                    placeholder="{{ 'Search by company city' | translate }}"
                    (myValueChange)="cityNameReceivedValue($event)"></app-grid-item-input-text>
            </div>
            <div class="col-3" *ngIf="gridTitle=='malls'">
                <app-grid-item-input-text label="{{ 'Location' | translate }}" icon="pi pi-search"
                    placeholder="{{ 'Search by Location' | translate }}"
                    (myValueChange)="cityNameReceivedValue($event)"></app-grid-item-input-text>
            </div>


            <div class="col-3" *ngIf="gridTitle=='Discounts'||gridTitle== 'Coupons'">
                <app-grid-item-input-text label="{{ 'What are looking for ?' | translate }}" icon="pi pi-search"
                    placeholder="{{ 'Company Name or Title' | translate }}"
                    (myValueChange)="titleNameReceivedValue($event)"></app-grid-item-input-text>
            </div>
            <div class="col-5 m-auto"
                *ngIf="gridTitle=='Discounts'||gridTitle== 'Coupons'|| gridTitle=='Broadcast Messages'">
                <label class="fs-6 fw-bold" *ngIf="gridTitle!='Broadcast Messages'">modified Date Range : </label>
                <label class="fs-6 fw-bold" *ngIf="gridTitle=='Broadcast Messages'">Launch Date Range : </label><br>
                <p-calendar [(ngModel)]="startDate" placeholder="Start Date" [showIcon]="true" [showButtonBar]="true">
                    <ng-template pTemplate="footer">
                        <button type="button" class="p-button p-component p-button-secondary"
                            (click)="clearStartDate()">
                            <span class="p-button-label">Clear</span>
                        </button>
                    </ng-template>
                </p-calendar>
                &nbsp;
                <p-calendar [(ngModel)]="endDate" placeholder="End Date" [showIcon]="true" [showButtonBar]="true">
                    <ng-template pTemplate="footer">
                        <button type="button" class="p-button p-component p-button-secondary" (click)="clearEndDate()">
                            <span class="p-button-label">Clear</span>
                        </button>
                    </ng-template>
                </p-calendar>
                &nbsp;&nbsp;&nbsp;
                <p-checkbox *ngIf="gridTitle=='Broadcast Messages'" binary="true" [(ngModel)]="SendAsSoonAsPossible"
                    label="ASAP">
                </p-checkbox>

            </div>
            <!-- End Users Management -->
            <div class="col-3" *ngIf="gridTitle=='EndUsersManagement'">
                <app-grid-item-input-text label=" {{ 'What are looking for ?' | translate }}" icon="pi pi-search"
                    placeholder="{{ 'Search by user name' | translate }}"
                    (myValueChange)="titleNameReceivedValue($event)"></app-grid-item-input-text>
            </div>
            <div class="col-2" *ngIf="gridTitle=='EndUsersManagement'">
                <app-grid-item-input-dropdown label="{{ 'Gender' | translate }}" listType="Gender"
                    (selectedGenderEventEmitter)="genderRecievedValue($event)"></app-grid-item-input-dropdown>
            </div>
            <div class="col-3" *ngIf="gridTitle=='EndUsersManagement'">
                <app-grid-item-input-text label="{{ 'Residance' | translate }}" icon="pi pi-map-marker"
                    placeholder="{{ 'Search by Residance' | translate }}"
                    (myValueChange)="residanceReceivedValue($event)"></app-grid-item-input-text>
            </div>
            <div class="col-2" *ngIf="gridTitle=='EndUsersManagement'">
                <app-grid-item-input-dropdown label="{{ 'Tier' | translate }}" listType="Tier"
                    (selectedTierEventEmitter)="tierRecievedValue($event)"></app-grid-item-input-dropdown>
            </div>
            <!-- SupscriptionsManagment -->
            <div class="col-3"
                *ngIf="gridTitle== 'SubscriptionsManagement' || gridTitle=='TiersManagement'|| gridTitle=='CountriesManagement'">
                <app-grid-item-input-text label="{{ 'What are looking for ?' | translate }}" icon="pi pi-search"
                    placeholder="{{ 'Search By Name' | translate }}"
                    (myValueChange)="titleNameReceivedValue($event)"></app-grid-item-input-text>
            </div>
            <!-- Malls -->
            <div class="col-4" *ngIf="gridTitle=='malls'"></div>
            <div class="col-7"
                *ngIf="gridTitle=='Industries'||gridTitle=='SubscriptionsManagement'||gridTitle=='TiersManagement'||gridTitle=='CountriesManagement'">
            </div>
            <!-- <div class="col-1"></div> -->
            <div class="d-flex align-items-end justify-content-around col-2">
                <!-- <p-button label="Filter" icon="fa-solid fa-filter" class="mx-2"></p-button> -->
                <!-- <p-button label="{{ 'Search' | translate }}"></p-button> -->
                <p-button label="{{ 'Search' | translate }}" (click)="search()"></p-button>
                <!-- icon="pi pi-search" -->
            </div>
        </div>
    </p-card>
</div>