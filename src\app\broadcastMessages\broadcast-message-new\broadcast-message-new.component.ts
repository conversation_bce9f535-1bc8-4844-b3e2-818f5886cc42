import { Location } from '@angular/common';
import { Component, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Message, MessageService } from 'primeng/api';
import { BroadcastMessageModel } from 'src/app/Model/BroadcastMessageModel';
import { BroadcastMessageMainInfoComponent } from '../components/broadcast-messages-main-info/broadcast-message-main-info.component';
import { BroadcastMessageTimingTargetedUserComponentComponent } from '../components/broadcast-message-timing-targeted-user-component/broadcast-message-timing-targeted-user-component.component';

@Component({
  selector: 'app-broadcast-message-new',
  templateUrl: './broadcast-message-new.component.html',
  styleUrls: ['./broadcast-message-new.component.scss'],

})
export class BroadcastMessageNewComponent {
  title: string;
  routeState: any;
  _BroadcastMessageData: BroadcastMessageModel = new BroadcastMessageModel();
  activeIndex = 0;
  editing: boolean = false;
  previousTabIndex: number = 0; // Track the previous tab index
  tabsDisabled: boolean = true;  // Control whether tabs are disabled or not
  userType: number = 0; // by default super Admin

  msgs: Message[] = [];

  @ViewChild(BroadcastMessageMainInfoComponent) BroadcastMessageMainInfoComponent!: BroadcastMessageMainInfoComponent;
  @ViewChild(BroadcastMessageTimingTargetedUserComponentComponent) BroadcastMessageTimingTargetedUserComponentComponent!: BroadcastMessageTimingTargetedUserComponentComponent;

  constructor(private route: ActivatedRoute, private router: Router,private location: Location, private messageService: MessageService) {
    if (this.router.getCurrentNavigation()?.extras.state) {
      this.routeState = this.router.getCurrentNavigation()?.extras.state;
      if (this.routeState) {
        this._BroadcastMessageData = this.routeState.data
          ? this.routeState.data
          : new BroadcastMessageModel();
        if (this._BroadcastMessageData.Id == "") {
          this.editing = false;
        }
        else {
          this.editing = true;
        }
      //  console.log("this.editing", this.editing);

      }
      
      if (this.editing == true) this.tabsDisabled = false;
    }
    this.route.data.subscribe((item) => {
      this.title = item['title'];
    });
  }
  goBack() {
    this.location.back();
  }
  onTabChange(event: any) {

    const newTabIndex = event.index; // Get the newly selected tab index
    let proceed = true;
    if (this.previousTabIndex === 0 && newTabIndex !== 0) {
      // If Tab 1 is selected, trigger function in app-child-one-component
      this.BroadcastMessageMainInfoComponent.next();
      proceed = this.BroadcastMessageMainInfoComponent.pressedNext;
      if (this._BroadcastMessageData.hasError == true) {
        this.msgs = [];
        this.msgs.push({ severity: 'error', summary: 'Error Message', detail: 'Please Check the Main Info Tab !' });
      }
      // this.BroadcastMessageTimingTargetedUserComponentComponent.save();
      // proceed = true;
      // if (this._BroadcastMessageData.hasError == true) {
      //   this.msgs = [];
      //   this.msgs.push({ severity: 'error', summary: 'Error Message', detail: 'Please Check the Timing & Targeted Users Tab !' });
      // }
      // console.log('pressedNext', this.BroadcastMessageMainInfoComponent.pressedNext, proceed)
    } 
    else if (this.previousTabIndex === 1 && newTabIndex !== 1) {

      this.BroadcastMessageTimingTargetedUserComponentComponent.save();
      proceed = true;
      if (this._BroadcastMessageData.hasError == true) {
        this.msgs = [];
        this.msgs.push({ severity: 'error', summary: 'Error Message', detail: 'Please Check the Timing & Targeted Users Tab !' });
      }
    }

   // console.log('activeIndex', this.activeIndex)
  }

  // Method to handle child component event and disable tabs
  handleDisableTabs(disable: boolean) {
    this.tabsDisabled = disable;  // If true, disable all tabs
    console.log('tabDisable', this.tabsDisabled)
  }
}
