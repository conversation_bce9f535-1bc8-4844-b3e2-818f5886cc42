import { FormGroup, ValidationErrors, ValidatorFn } from "@angular/forms";

export const startEndDatesValidator: ValidatorFn = (group: FormGroup): ValidationErrors | null => {
    // console.log(group);

    // const start = group.get('startDate');
    const start = group.controls['startDate'];
    // console.log(start);
    const end = group.get('endDate');
    // console.log("validators called");
    const sDate = new Date(start.value);
    const eDate = new Date(end.value);
    // return sDate < eDate ? null : { dateValid: true };
    // return start.value !== null && end.value !== null && start.touched && end.touched && sDate < eDate
    //     ? null : { dateValid: true };

    if((start.value === null && start.touched) || (end.touched && end.value === null)) {
        return null;
    } else if (sDate > eDate) {
        return { dateValid: true };
    } else {
        return null;
    }
}
