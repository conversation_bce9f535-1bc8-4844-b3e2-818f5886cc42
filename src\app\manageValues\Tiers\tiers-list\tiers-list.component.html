<p-toast></p-toast>
<div class="grid">
    <div class="col-12">
        <div>
            <p-confirmDialog header="Confirmation" key="confirm1" icon="pi pi-exclamation-triangle"
                message="Are you sure you want to proceed?" [style]="{ width: '350px' }"
                acceptButtonStyleClass="p-button-text" rejectButtonStyleClass="p-button-text"></p-confirmDialog>


            <p-table #dt [value]="Tiers" [paginator]="paginator" [rows]="pageSize" [totalRecords]="totalRecords"
                [lazy]="true" [loading]="loading" (onLazyLoad)="loadData($event)" [showCurrentPageReport]="true"
                [currentPageReportTemplate]="'Showing {first} to {last} of {totalRecords} records'"
                responsiveLayout="scroll" [rowHover]="true" styleClass="p-datatable-gridlines">

                <ng-template pTemplate="caption">
                    <app-grid-headers [myDt]="Tiers" (SearchEvent)='ReceivedFilteredData($event)'
                        addNewTxt="Add New Tier" goRoute="/tier-new" gridTitle="TiersManagement" [title]="title">
                    </app-grid-headers>
                </ng-template>
                <ng-template pTemplate="header">
                    <tr>
                        <th pSortableColumn="EnName">
                            <div class="flex justify-content-between align-items-center">
                                {{ "Tier Name" | translate }}
                            </div>
                        </th>
                        <th>
                            <div class="flex justify-content-between align-items-center">
                                {{ "Rank" | translate }}
                            </div>
                        </th>
                        <th>
                            <div class="flex justify-content-between align-items-center">
                                {{ "Total Countries" | translate }}
                            </div>
                        </th>
                        <th>
                            <div class="flex justify-content-between align-items-center">
                                {{ "Total Users" | translate }}
                            </div>
                        </th>
                        <th>
                            <div class="flex justify-content-between align-items-center">
                                {{ "Edit" | translate }}
                            </div>
                        </th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-Tier>
                    <tr>
                        <td>
                            <div class="flex">
                                <span class="p-badge p-badge-lg" [style.backgroundColor]="Tier.Color">{{ Tier.EnName
                                    }}</span>
                            </div>
                        </td>
                        <td>
                            <div class="flex">
                                {{ Tier.Rank }}
                            </div>
                        </td>
                        <td>
                            <div class="flex">
                                {{ Tier.TotalCountries }}
                            </div>
                        </td>
                        <td>
                            <div class="flex">
                                {{ Tier.TotalUsers }}
                            </div>
                        </td>
                        <td>
                            <span class="p-column-title">Edit</span>
                            <div class="flex">
                                <div class="d-flex align-items-center justify-content-between">
                                    <p-button icon="pi pi-pencil" styleClass="p-button-rounded" class="mx-1"
                                        (click)=" edit({ Tier: Tier, state: 'edit' })"></p-button>
                                    <p-button icon="pi pi-trash" styleClass="p-button-rounded p-button-danger"
                                        class="mx-1" (click)="delete(Tier)"></p-button>
                                </div>
                            </div>
                        </td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td class="text-center" colspan="7">
                            {{ "No Tiers found." | translate }}
                        </td>
                    </tr>
                </ng-template>
            </p-table>
            <!-- Reusable Confirmation Dialog Component -->
            <app-confirmation-dialog #confirmationDialog
                (confirmResult)="handleConfirmationAction($event)"></app-confirmation-dialog>

        </div>
    </div>
</div>