import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { PrimeNGConfig } from 'primeng/api';
import { RoutingAnimation } from './animations/routing.animation';
import { CountryService } from './services/country.service';

@Component({
    selector: 'app-root',
    templateUrl: './app.component.html',
    animations: [RoutingAnimation],
    providers: [CountryService]
})
export class AppComponent implements OnInit {

    topbarTheme = 'light';

    menuTheme = 'dim';

    layoutMode = 'light';

    menuMode = 'static';

    isRTL = false;

    inputStyle = 'outlined';

    ripple: boolean;

    constructor(private primengConfig: PrimeNGConfig, translate: TranslateService, _countryService: CountryService) {
        translate.addLangs(['en', 'ar']);
        translate.setDefaultLang('en');
        const lang = localStorage.getItem('lang');
        if (lang == 'ar') {
            translate.use('ar');
            this.isRTL = true;
        }
        else {
            translate.use('en');
            localStorage.setItem('lang', 'en');
        }
        _countryService.getCountries().subscribe();
    }

    ngOnInit() {
        this.primengConfig.ripple = true;

    }
}
