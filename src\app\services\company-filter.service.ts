import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, map, Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { Dto } from '../Model/Dto';
import { CompanyFilterModel } from '../Model/CompanyFilterModel';

@Injectable({
  providedIn: 'root'
})
export class CompanyFiltersService {
  public CompanyFiltersDataSource = new BehaviorSubject<Array<CompanyFilterModel>>([]);
  public CompanyFilters = this.CompanyFiltersDataSource.asObservable();
  constructor(private httpClient: HttpClient) { }
  getCompanyFilters() {
    return this.httpClient.get<Dto<CompanyFilterModel>>(`${environment.apiUrl}` + 'Industry/GetAllFilters')
      .pipe(
        map((res: Dto<CompanyFilterModel>) => {
          //this.CompanyFiltersDataSource.next(res.ListResultContent);
          return res.ListResultContent;
        })
      );
  }
}
