<p-card [ngStyle]="{ 'text-align': 'center' }">
      <div class="p-d-flex p-ai-center mb-2" *ngIf="loading==false">
            <img src="{{getImageUrl( currentUser?.ProfilePhotoUrl)}}" alt="currentUser? Profile" class="p-mr-3"
                  style="border-radius: 50%; width: 100px; height: 100px;" />
            <div>
                  <!-- <h2 class="p-m-3">{{ currentUser?.Name }}</h2> -->
                  <p class="p-m-3">{{ currentUser?.AdminType |AdminType }}
                        <i *ngIf="currentUser?.AdminType==1" class="fas fa-user"></i>
                        <i *ngIf="currentUser?.AdminType==0" class="fas fa-user-shield"></i>
                  </p>
            </div>
      </div>

      <!-- <div class="p-d-flex p-ai-center mb-2">
            <p><strong>Last Login:</strong> {{ currentUser?.lastLogin? | date: 'medium' }}</p>
            <p-chip><strong>User Type: </strong> &nbsp; {{ currentUser?.UserType | UserType }}</p-chip>

      </div>
      <div class="mb-4">
            <p-chip><strong>Email: </strong> &nbsp; {{ currentUser?.Email }}</p-chip>

      </div> -->
      <div class="justify-content-center" *ngIf="loading==false">
            <div class=" flex-column gap-3 mb-4">
                  <label class="col-fixed w-6rem">Name:</label>
                  <input style="width: 20%;" type="text" pInputText [(ngModel)]="currentUser.Name" readonly>
            </div>
            <div class=" flex-column gap-3 mb-4">
                  <label class="col-fixed w-6rem">Email:</label>
                  <input style="width: 20%;" type="text" pInputText [(ngModel)]="currentUser.Email" readonly>
            </div>
            <div class=" flex-column gap-3 mb-4">
                  <label>Last Login :</label> &nbsp;
                  <input style="width: 20%;" type="text" pInputText
                        [value]="currentUser?.LastLoginTime | date:'yyyy-MM-dd HH:mm:ss'" readonly>

            </div>
            <div class="d-flex gap-2 align-items-center mb-4 justify-content-center">
                  <label class="col-fixed w-6rem">Password:</label>
                  <input style="width: 17%;" type="text" pInputText [(ngModel)]="currentUser.Password" readonly>
                  <button pButton pRipple title="Update Password" icon="pi pi-refresh"
                        (click)="displayresetPasswordModel=true"></button>
            </div>
            <div class=" flex-column gap-3 mb-4">
                  <label class="col-fixed w-6rem">Countries:</label> &nbsp;
                  <p-multiSelect [style]="{'width': '20%'}" [options]="currentUser.AdministeredCountries"
                        [(ngModel)]="currentUser.AdministeredCountries" defaultLabel="Administered Countries"
                        optionLabel="EnName" displayUpdatePasswordModel="chip" id="dd_country" [disabled]="true"
                        [ngModelOptions]="{ standalone: true }"></p-multiSelect>
            </div>
            <div class="d-flex gap-2 align-items-center mb-4 justify-content-center">
                  <label>Max Service Discount:</label> &nbsp;
                  <p-inputNumber [style]="{'width': '18%'}" [useGrouping]="false"
                        [(ngModel)]="currentUser.MaxServiceDiscount" mode="decimal" suffix="%" [min]="0" [max]="100"
                        [disabled]="true" />
            </div>
      </div>

      <button pButton type="button" label="Logout" icon="pi pi-sign-out" class="p-button-danger p-mt-3"
            (click)="onLogout()"></button>
</p-card>


<p-dialog header="Reset Password" [(visible)]="displayresetPasswordModel" modal="modal" [style]="{width: '30vw'}"
      [breakpoints]="{'960px': '75vw'}" [draggable]="false" closable="true">

      <div class="d-flex gap-2 align-items-center mb-4 justify-content-center" [formGroup]="restPasswordForm">
            <label for="adminPassword">Admin password : </label>
            <p-password [toggleMask]="true" formControlName="newPassword" id="adminPassword"
                  placeholder="Admin Password" #newPasswordInput>
            </p-password>
      </div>
      <span *ngIf="restPasswordForm.get('newPassword')?.invalid && restPasswordForm.get('newPassword')?.touched"
            [style.color]="CheckValid(restPasswordForm.controls.newPassword)">
            Password must contain at least one lowercase letter, one uppercase letter, one digit, and one special
            character ($@$!%*?&), and be at least 8 characters long.
      </span>
      <ng-template pTemplate="footer">
            <button pButton icon="pi pi-check" [disabled]="restPasswordForm.invalid" (click)="resetPassword()"
                  label="Ok" class="p-button-outlined"></button>
      </ng-template>
</p-dialog>

<div class="loader" *ngIf="loading">
      <div class="loader-inner">
            <div class="loader-line-wrap">
                  <div class="loader-line"></div>
            </div>
            <div class="loader-line-wrap">
                  <div class="loader-line"></div>
            </div>
            <div class="loader-line-wrap">
                  <div class="loader-line"></div>
            </div>
            <div class="loader-line-wrap">
                  <div class="loader-line"></div>
            </div>
            <div class="loader-line-wrap">
                  <div class="loader-line"></div>
            </div>
      </div>