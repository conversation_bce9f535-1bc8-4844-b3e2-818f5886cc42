import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { catchError, map } from 'rxjs/operators';
import { Dto } from '../Model/Dto';
import { environment } from 'src/environments/environment';
import { UserModel } from '../Model/UserModel';
import { Observable, of } from 'rxjs';

@Injectable({
	providedIn: 'root'
})
export class CompanyAdminService {

	constructor(protected httpClient: HttpClient,) { }

	GetAllCompanyAdmins(page: number = null, pageSize: number = null, Name: string = null, adminType: string = null, CompanyId: string = null) {
		let params = new HttpParams();
		if (page) {
			params = params.set('PageNumber', page);
		}
		if (pageSize) {
			params = params.set('PageSize', pageSize);
		}
		if (Name) {
			params = params.set('Name', Name);
		}
		if (adminType != null) {
			params = params.set('AdminType', adminType);
		}
		if (CompanyId) {
			params = params.set('CompanyId', CompanyId);
		}
		return this.httpClient.get<Dto<UserModel>>(`${environment.apiUrl}` + 'CompanyAdmin/GetAll', { params })
			.pipe(
				map((res: any) => {
					return res;

				})
			);
	}

	getCompanyAdminById(Id: string = "") {
		return this.httpClient.get<Dto<UserModel>>(`${environment.apiUrl}CompanyAdmin/GetCompanyAdminById?Id=${Id}`)
			.pipe(
				map((res: any) => {

					return res['ResultContent'];
				})
			);
	}

	public AddCompanyAdmin(data): Observable<Dto<UserModel>> {
		var http;
		var url = `${environment.apiUrl}CompanyAdmin/Register`;
		http = this.httpClient.post(url, data);
		return http.pipe(
			map((res: Dto<UserModel>) => {
				return res;
			}),
			catchError(error => {
				return of(false);

			}));

	}

	public EditCompanyAdmin(data): Observable<Dto<UserModel>> {
		var http;
		var url = `${environment.apiUrl}CompanyAdmin/EditCompanyAdmin`;
		http = this.httpClient.put(url, data);
		return http.pipe(
			map((res: Dto<UserModel>) => {

				return res;

			}),
			catchError(error => {

				return of(false);
			}));
	}

	public EditActiveCompanyAdmin(CompanyAdminId: String): Observable<any> {
		var http;
		var url = `${environment.apiUrl}CompanyAdmin/EditCompanyAdminActive?Id=${CompanyAdminId}`;
		http = this.httpClient.patch(url, {});
		return http.pipe(
			map((res: any) => {
				return res;
			}),
			catchError((error) => {

				return of(false);
			})
		);
	}

	public DeleteCompanyAdmin(CompanyAdminId: string): Observable<any> {
		var http;
		var url = `${environment.apiUrl}CompanyAdmin/DeleteCompanyAdmin?Id=${CompanyAdminId}`;

		http = this.httpClient.delete(url);
		return http.pipe(
			map((res: any) => {
				return res;
			}),
			catchError((error) => {

				return of(false);
			})
		);
	}

}
