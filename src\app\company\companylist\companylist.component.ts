import { ChangeDetectorRef, Component, Input, OnInit, ViewChild } from "@angular/core";
import { CompanyModel } from "src/app/Model/CompanyModel";
import { Dto } from "src/app/Model/Dto";
import { UserModel } from "src/app/Model/UserModel";
import { MessageService, ConfirmationService } from "primeng/api";
import { Router } from "@angular/router";
import { CompanyService } from "src/app/services/company.service";
import { environment } from "src/environments/environment";
import { ConfirmationDialogComponent } from "src/app/components/confirmation-dialog/confirmation-dialog.component";
import { AuthService } from "src/app/services/auth.service";

@Component({
    selector: "app-companylist",
    templateUrl: "./companylist.component.html",
    styleUrls: ["./companylist.component.scss"],
    providers: [MessageService, ConfirmationService],
})
export class CompanylistComponent implements OnInit {
    @Input() _Company: CompanyModel;
    companies?: CompanyModel[];
    company: CompanyModel = new CompanyModel();
    loading: boolean = true;
    user: UserModel;
    formData: CompanyModel;
    title: string;

    warningMessage: string = '';
    @ViewChild('confirmationDialog') confirmationDialog: ConfirmationDialogComponent;
    confirmationAction: string = '';
    OverrideWarning: boolean = false;


    totalRecords: number = 0;
    pageSize: number = 10;
    pageIndex: number = 0;
    paginator: boolean = false;

    //filters
    companyName: string = '';
    cityName: string = '';
    industry: string = '';

    //  track the first load
    private isInitialLoad: boolean = false;

    constructor(
        private companyService: CompanyService,
        private router: Router,
        private messageService: MessageService, private authService: AuthService) { }

    ngOnInit(): void {
        this.loadData({ first: 0, rows: this.pageSize });
        this.isInitialLoad = true;
        this.title = "Companies";
    }

    loadData(event: any) {
        // Avoid double loading during the first render
        if (this.isInitialLoad) {
            this.isInitialLoad = false; // Set the flag to false after the first load
            return;
        }

        this.user =  this.authService.getUserData();
        const pageNumber = event.first / event.rows + 1; // Calculate the page number
        const pageSize = event.rows; // Rows per page
        this.paginator = true;
        if (this.user.UserType == 0) {
            this.fetchCompaniesData(pageNumber, pageSize);
        } else {
            this.companyService
                .GetCompanyByUser()
                .subscribe((data: Dto<CompanyModel>) => {
                    this.loading = false;
                    this.router.navigate(["company-new"], {
                        state: {
                            data: data,
                            command: "edit",
                        },
                    });
                });

        }
    }
    
    fetchCompaniesData(pageNumber, pageSize) {
        this.companyService
            .GetAll(pageNumber, pageSize, this.companyName, this.cityName, this.industry)
            .subscribe((data) => {
                this.companies = data.ListResultContent;
                this.totalRecords = data.TotalRecords; // Set total records for pagination
                this.loading = false;
            });
    }

    edit(e: { company?: CompanyModel; state: string }) {

        if (e.company.Active == false) {
            this.messageService.add({
                severity: "warn",
                summary: "Warning",
                detail: "Please activate the company before Editing ! ",
            });

        } else {
            this.formData = e.company;

            this.companyService.GetCompanyById(e.company.Id.toString()).subscribe((data) => {
                this.router.navigate(["company-new"], {
                    state: {
                        data: data,
                        command: "edit",
                    },
                });
            });
        }

    }

    editActiveCompany(companyId: String) {
        this.user =  this.authService.getUserData();
        if (this.user.UserType == 0) {
            this.companyService
                .EditActiveCompany(companyId)
                .subscribe((data: any) => {
                    this.company = this.companies.find((x) => x.Id == companyId);
                    if (data['HasError'] == true) {
                        this.OverrideWarning = true;
                        this.confirmationAction = 'active';
                        this.confirmationDialog.item = this.company;
                        this.confirmationDialog.openDialog();
                        this.confirmationDialog.message = data['EnErrorMessage'];
                    }
                });
        }
    }

    // searchingData(companyName: String) {
    //     this.user =  this.authService.getUserData();
    //     if (this.user.UserType == 0) {
    //         this.companyService
    //             .SearchByCompanyName(companyName)
    //             .subscribe((data: any) => {
    //                 console.log("Test From Company List");
    //                 this.companies = data.Result;
    //                 this.loading = false;
    //             });
    //     }
    // }
    ReceivedFilteredData(event) {
        this.companyName = event.companyName;
        this.industry = event.industry?.Code;
        this.cityName = event.cityName;
        this.fetchCompaniesData(1, this.pageSize);

        this.companies = event.ListResultContent;
        this.totalRecords = event.TotalRecords; // Set total records for pagination
        //this.paginator = false;

    }

    delete(companyId: String) {
        this.company = this.companies.find((x) => x.Id == companyId);
        this.confirmationDialog.message = 'Do you want to delete this Company ' + this.company.EnName;
        this.confirmationDialog.item = this.company;
        this.confirmationDialog.openDialog();
        this.confirmationAction = 'delete';

    }

    // Method to handle the confirmation result
    handleConfirmationAction(result: boolean): void {
        if (this.confirmationAction == 'delete') {
            if (result) {
                this.companyService.DeleteCompany(this.company.Id, this.OverrideWarning).subscribe((data) => {
                    if (data['HasError'] == false) {
                        // Update the data array (remove the deleted item)
                        this.companies = this.companies.filter(currentItem => currentItem.Id !== this.company.Id);
                        this.company = new CompanyModel();
                        this.OverrideWarning = false;
                        this.confirmationAction = '';

                    } else {
                        this.OverrideWarning = true;
                        this.confirmationDialog.item = this.company;
                        this.confirmationDialog.openDialog();
                        this.confirmationDialog.message = data['EnErrorMessage'];
                    }
                });
            } else {
                this.confirmationAction = '';
                this.company = new CompanyModel();
            }

        } else if (this.confirmationAction == 'active') {
            if (result) {
                this.companyService.EditActiveCompany(this.company.Id, this.OverrideWarning).subscribe((data) => {
                    if (data['HasError'] == true) {
                        this.OverrideWarning = true;
                        this.confirmationDialog.item = this.company;
                        this.confirmationDialog.openDialog();
                        this.confirmationDialog.message = data['EnErrorMessage'];
                    } else {
                        this.confirmationAction = '';
                        this.OverrideWarning = false;
                        this.company = new CompanyModel();
                    }
                });
            } else {
                this.confirmationAction = '';
                this.OverrideWarning = false;
                this.company = new CompanyModel();
            }

        }
    }
    getLogoUrl(logo) {
        return logo ? environment.imageSrc + logo : '';
        // return logo ? this.sanitizer.bypassSecurityTrustUrl(environment.imageSrc + logo) : '';
    }
}
