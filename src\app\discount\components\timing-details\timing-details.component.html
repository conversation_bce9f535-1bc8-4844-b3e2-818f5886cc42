<div class="container" [formGroup]="timingDetailsForm">

    <div class="flex flex-column gap-2 mb-3">
        <label [style.color]="CheckValid(timingDetailsForm.controls.period)" for="period">Period</label>
        <p-dropdown [options]="discountPeriod" [(ngModel)]="_discountData.DiscountPeriod" optionLabel="name" id="period"
            optionValue="id" formControlName="period" (onChange)="changePeriod()"></p-dropdown>
    </div>
    <p-toast></p-toast>
    <p-card>
        <div class="flex flex-column gap-2 mb-3">
            <label for="timing">Timing</label>
            <div class="row">
                <div class="col-12 row" formGroupName="startEndDates">
                    <div class="col-6">
                        <div class="flex flex-column gap-2 mb-3">
                            <label
                                [style.color]="CheckValid(timingDetailsForm.controls.startEndDates.controls.startDate)"
                                for="StartDate">Start date</label>
                            <input type="date" pInputText id="StartDate" [(ngModel)]="_discountData.StartDate"
                                [min]="currentDate" [ngModel]="
                                    _discountData.StartDate | date : 'yyyy-MM-dd'
                                " class="w-50" formControlName="startDate" />
                        </div>
                    </div>

                    <div class="col-6">
                        <div class="flex flex-column gap-2 mb-3">
                            <label [style.color]="CheckValid(timingDetailsForm.controls.startEndDates.controls.endDate)"
                                for="EndDate">End date</label>
                            <input type="date" id="EndDate" pInputText [(ngModel)]="_discountData.EndDate"
                                [min]="currentDate" [ngModel]="
                                    _discountData.EndDate | date : 'yyyy-MM-dd' " class="w-50"
                                formControlName="endDate" [readonly]="_discountData.DiscountPeriod==0" />
                        </div>
                    </div>

                    <!-- <div class="col-12" *ngIf="!timingDetailsForm.controls.startEndDates.valid"> -->
                    <div class="col-12"
                        *ngIf="timingDetailsForm.controls.startEndDates.invalid && (this.pressedNext || timingDetailsForm.controls.startEndDates.touched)">
                        <h6 [style.color]="CheckValid(timingDetailsForm.controls.startEndDates)">End date must be
                            greater than start date!</h6>
                        <!-- <h6 [style.color]="CheckValid(timingDetailsForm.controls.startEndDates)">Please enter valid start date and end date!</h6> -->
                    </div>
                </div>

                <div class="col-6">

                    <div class="flex flex-column gap-2 mb-3">
                        <label for="discountDays">Discount Days</label>
                        <p-dropdown [options]="discountDays" [(ngModel)]="_discountData.DiscountDayType"
                            optionLabel="name" id="discountdays" optionValue="id"
                            formControlName="discountDays"></p-dropdown>
                    </div>
                    <div *ngIf="_discountData.DiscountDayType == 1" class="flex flex-column gap-2 mb-3">
                        <!-- [style.color]="CheckValid(timingDetailsForm.controls.weekDays)" -->
                        <label for="weekDays">Days of week</label>
                        <p-selectButton [options]="weekDays" [(ngModel)]="_discountData.DayOfWeeks" [multiple]="true"
                            optionLabel="name" optionValue="value"
                            [ngModelOptions]="{standalone: true}"></p-selectButton>
                        <!-- formControlName="weekDays" -->
                    </div>
                </div>
                <div class="col-6 grid gap-3">
                    <!-- <div class="">
                            
                        </div> -->
                    <div class="flex flex-column gap-2 mb-3">
                        <label [style.color]="CheckValid(timingDetailsForm.controls.OfferStartTime)"
                            for="OfferStartTime">Offer Start time</label>
                        <input type="text" pInputText id="OfferStartTime" [(ngModel)]="_discountData.OfferStartTime"
                            class="flex" type="time" formControlName="OfferStartTime" />
                    </div>
                    <div class="flex flex-column gap-2 mb-3">
                        <label [style.color]="CheckValid(timingDetailsForm.controls.OfferEndTime)"
                            for="OfferEndTime">Offer End time</label>
                        <input type="text" pInputText id="OfferEndTime" [(ngModel)]="_discountData.OfferEndTime"
                            class="flex" type="time" formControlName="OfferEndTime" />
                    </div>



                </div>

            </div>

        </div>
    </p-card>
    <br />
    <!-- <ng-container *ngIf="_discountData.ApprovedByAdmin"> -->

    <div *ngIf="_discountData.DiscountType != 1">
        <div class="flex gap-2 mb-3">
            <label [style.color]="CheckValid(timingDetailsForm.controls.normalTiers)" for="normalTiers">Normal
                Tiers</label>
            <p-inputSwitch id="normalTiers" [(ngModel)]="_discountData.IsNormalTier" (onChange)="changeVip()"
                formControlName="normalTiers"></p-inputSwitch>
        </div>

        <div class="flex gap-2 mb-3" *ngIf="this._discountData.IsNormalTier">
            <p-table [columns]="tiersCols" [value]="_discountData.NormalTiersValues" index="0"
                [tableStyle]="{ 'min-width': '50rem' }" styleClass="p-datatable-sm p-datatable-gridlines">
                <ng-template pTemplate="header" let-columns>
                    <tr>
                        <th *ngFor="let col of columns" class="text-center">
                            {{ col.header }}
                        </th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-rowData let-columns="columns" class="gap-2">
                    <tr>
                        <!-- <td *ngFor="let col of columns" class="text-center">
                            <p-inputNumber *ngIf="rowData[col.field].name == 'FreeTier'"
                                [(ngModel)]="_discountData.FreeTierOffers"></p-inputNumber>
                            {{ rowData[col.field] }}
                        </td> -->
                        <td class="text-center">
                            {{rowData['Tier'].EnName}}
                        </td>
                        <td class="text-center">
                            <p-inputNumber [min]="0" [(ngModel)]="rowData['OffersNum']" [ngModelOptions]="{standalone: true}"
                                [disabled]=" rowData['UnlimitedOffers']"
                                [ngClass]="{'error': !isValidValueTier(rowData['OffersNum'],rowData['UnlimitedOffers'],'normalTier')}"
                                (input)="onInputVipTierValueChange(rowData)" mode="decimal" class="text-center m-2"
                                [min]="0" />

                            <input class="text-center" type="checkbox" [ngModelOptions]="{standalone: true}"
                                [(ngModel)]="rowData['UnlimitedOffers']" />
                            <label>&#8734;</label>

                            <div class="field p-fluid">
                                <small id="hasErrorNormalOffersNumTier" *ngIf="hasErrorNormalOffersNumTier"
                                    class="p-error">Please Add offers Numbers</small>
                            </div>
                        </td>

                        <td class="text-center">
                            <p-dropdown [options]="rowData['renewabilityOptions']"
                                [ngModelOptions]="{ standalone: true }" optionLabel="name" optionValue="id"
                                [(ngModel)]="rowData['RenewabilityType']" appendTo="body"></p-dropdown>
                        </td>
                        <td class="text-center">
                            <input class="text-center" type="text" pInputText [ngModelOptions]="{standalone: true}"
                                [value]="calculateTotalOfferPerYear(rowData['RenewabilityType'],rowData['OffersNum'])"
                                readOnly="true" />
                        </td>
                        <!-- <td class="text-center">
                            <p-inputNumber *ngIf="rowData[col.field].name == 'FreeTier'"
                                [(ngModel)]="_discountData.FreeTierOffers"></p-inputNumber>
                            {{ rowData[col.field] }}
                        </td> -->
                    </tr>
                </ng-template>
            </p-table>
        </div>

        <div class="flex gap-2 mb-3">
            <label [style.color]="CheckValid(timingDetailsForm.controls.vipTier)" for="vipTier">VIP Tier</label>
            <p-inputSwitch id="vipTier" [(ngModel)]="_discountData.IsVipTier" (onChange)="changeNormal()"
                formControlName="vipTier"></p-inputSwitch>
        </div>

        <div class="flex gap-2 mb-3" *ngIf="this._discountData.IsVipTier">

            <p-table [columns]="tiersColsVip" [value]="_discountData.VipTiersValues" index="0"
                [tableStyle]="{ 'min-width': '50rem' }" styleClass="p-datatable-sm p-datatable-gridlines">
                <ng-template pTemplate="header" let-columns>
                    <tr>
                        <th *ngFor="let col of columns" class="text-center">
                            {{ col.header }}
                        </th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-rowData let-columns="columns" class="gap-2">
                    <tr>
                        <!-- <td *ngFor="let col of columns" class="text-center">
                        <p-inputNumber *ngIf="rowData[col.field].name == 'FreeTier'"
                            [(ngModel)]="_discountData.FreeTierOffers"></p-inputNumber>
                        {{ rowData[col.field] }}
                    </td> -->
                        <td class="text-center">
                            {{rowData['Tier'].EnName}}
                        </td>
                        <td class="text-center field p-fluid">
                            <p-inputNumber [ngModelOptions]="{standalone: true}" [(ngModel)]="rowData['UsersNum']"
                                (input)="onInputVipTierValueChange(rowData)" [disabled]="rowData['UnlimitedOffers']"
                                [ngClass]="{'error': !isValidValueTier(rowData['UsersNum'],rowData['UnlimitedOffers'],'vipTier')}"
                                mode="decimal" class="text-center m-2" [min]="0" />
                            <small id="hasErrorVipNumTier" *ngIf="hasErrorVipNumTier" class="p-error">Please Add Users
                                Number</small>
                            <!-- <p-message *ngIf="hasErrorVipNumTier" severity="error" text=" Please Check VIP Tier Number Users"></p-message> -->
                        </td>
                        <td class="text-center">
                            <p-inputNumber [ngModelOptions]="{standalone: true}" [(ngModel)]="rowData['OffersNum']"
                                (input)="onInputVipTierValueChange(rowData)" [disabled]="rowData['UnlimitedOffers']"
                                mode="decimal" class="text-center m-2" [min]="0" />
                            <input class="text-center" type="checkbox" [ngModelOptions]="{standalone: true}"
                                [(ngModel)]="rowData['UnlimitedOffers']" />

                            <label>&#8734;</label>
                        </td>
                        <td class="text-center">
                            <p-dropdown [options]="rowData['renewabilityOptions']"
                                [ngModelOptions]="{ standalone: true }" optionLabel="name" optionValue="id"
                                [(ngModel)]="rowData['RenewabilityType']" appendTo="body"></p-dropdown>
                        </td>
                        <td class="text-center">
                            <input class="text-center" type="text" pInputText [ngModelOptions]="{standalone: true}"
                                [value]="calculateTotalOfferPerYear(rowData['RenewabilityType'],rowData['OffersNum'])"
                                readOnly="true" />
                        </td>
                        <!-- <td class="text-center">
                        <p-inputNumber *ngIf="rowData[col.field].name == 'FreeTier'"
                            [(ngModel)]="_discountData.FreeTierOffers"></p-inputNumber>
                        {{ rowData[col.field] }}
                    </td> -->
                    </tr>
                </ng-template>
            </p-table>
        </div>
    </div>
    <div *ngIf="_discountData.DiscountType == 1">
        <p-card>
            <div formGroupName="groupInfo" class="flex flex-column gap-2 mb-3">
                <label for="timing">Group Information</label>

                <div class="row">
                    <div class="col-6">
                        <div class="flex flex-column gap-2 mb-3">
                            <label
                                [style.color]="CheckValid(timingDetailsForm.controls.groupInfo.controls.offerPerUser)"
                                for="OfferPerUser">Offer Per User</label>
                            <p-inputNumber [ngModelOptions]="{standalone: true}"
                                [(ngModel)]="_discountData.groupInfo.OfferPerUser"
                                [disabled]="_discountData.groupInfo.UnlimitedOffers" class="w-50" mode="decimal"
                                [min]="0" />
                        </div>
                        <div>
                            <label>Set Unlimited Offers :</label>
                            <input type="checkbox" [ngModelOptions]="{standalone: true}"
                                [(ngModel)]="_discountData.groupInfo.UnlimitedOffers"
                                (ngModelChange)="onUnlimitedChange($event)" />
                            <label> &#8734; </label>

                        </div>
                        <div *ngIf="!_discountData.groupInfo.UnlimitedOffers">
                            <small *ngIf="_discountData.groupInfo.OfferPerUser==null" class="p-error">Please Add Offer
                                Per User Number</small>
                        </div>

                    </div>

                    <div class="col-6">
                        <div class="flex flex-column gap-2 mb-3">
                            <label [style.color]="CheckValid(timingDetailsForm.controls.groupInfo.controls.renewbility)"
                                for="renewbility">Renewability </label>
                            <p-dropdown [options]="renewbility" [(ngModel)]="_discountData.groupInfo.RenewabilityType"
                                optionLabel="name" id="period" optionValue="id"
                                formControlName="renewbility"></p-dropdown>
                        </div>
                    </div>

                    <div class="col-6">
                        <div class="flex flex-column gap-2 mb-3">
                            <label [style.color]="CheckValid(timingDetailsForm.controls.groupInfo.controls.minPeople)"
                                for="MinPeople">Min
                                People</label>
                            <p-inputNumber id="MinPeople" [(ngModel)]="_discountData.groupInfo.MinPeople" class="w-50"
                                formControlName="minPeople" mode="decimal" class="text-center m-2" [min]="0" />

                        </div>
                    </div>
                    <div class="col-6">
                        <div class="flex flex-column gap-2 mb-3">
                            <label [style.color]="CheckValid(timingDetailsForm.controls.groupInfo.controls.maxPeople)"
                                for="MaxPeople">Max
                                People</label>
                            <p-inputNumber id="MaxPeople" [(ngModel)]="_discountData.groupInfo.MaxPeople" class="w-50"
                                formControlName="maxPeople" mode="decimal" class="text-center m-2" [min]="0" />
                        </div>
                    </div>

                    <div [style.color]="CheckValid(timingDetailsForm.controls.groupInfo.controls.minPeople)"
                        *ngIf="timingDetailsForm.controls.groupInfo.hasError('invalidRange')">
                        Max value must be greater than min value.
                    </div>
                </div>
            </div>
        </p-card>

    </div>
    <!-- </ng-container> -->
    <!-- <ng-container *ngIf="!_discountData.ApprovedByAdmin">

        <div class="row p-3">
            <p-card>
                <div class="col-4">
                    <p>Tiers</p>
                    <div *ngFor="let tier of dbTiers$">
                        <input class="me-2" type="checkbox" [value]="tier.Id" [id]="tier.Id" [checked]="tier.Checked"
                            (change)="onTierChange(tier, $event.target.checked)" />
                        <label [for]="tier.Id">{{tier.EnName}}</label>
                    </div>
                </div>
            </p-card>
        </div>
    </ng-container> -->

    <br />
    <div class="flex justify-content-end gap-2 mb-3">
        <p-button label="back" styleClass="p-button-outlined p-button-secondary" (click)="back()"></p-button>
        <p-button label="save" (onClick)="save()"></p-button>
        <!-- [disabled]="!this.timingDetailsForm.valid" -->
    </div>
</div>