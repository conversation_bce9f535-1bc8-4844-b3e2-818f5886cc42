import { Component, EventEmitter, Input, Output } from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { MessageService } from 'primeng/api';
import { CompanyModel } from 'src/app/Model/CompanyModel';

@Component({
    selector: 'app-services-list',
    templateUrl: './services-list.component.html',
    styleUrls: ['./services-list.component.scss'],

})
export class ServicesListComponent {
    @Input() _Company: CompanyModel;
    @Input() activeIndex: number;
    @Output() activeIndexChange: EventEmitter<number> = new EventEmitter();
    // serviceListUrl: FormControl = new FormControl('', [Validators.required, urlValidator('pdf')]);
    // serviceListUrl: FormControl = new FormControl('');
    pressedNext: boolean = false;

    constructor(private messageService: MessageService, private sanitizer: DomSanitizer

    ) { }

    next() {
        this.pressedNext = true;
        if (this.CheckValid() == 'red') {
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Check your input' });
            return;
        }
        // this._Company.ServiceListUrl = this.serviceListUrl.value;
        this.activeIndex++;
        this.activeIndexChange.emit(this.activeIndex);
    }
    back() {
        this.activeIndex--;
        this.activeIndexChange.emit(this.activeIndex);
    }
    CheckValid() {
        // const urlPattern = /^https?:\/\/\S+/;
        let pdfUrlPattern = new RegExp('^(https?:\\/\\/)?' + // protocol
            '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|' + // domain name
            '((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
            '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*'  // port and path
            // + '.pdf$', 'i'  // ends with .pdf extension;

        );

        if (this._Company.ServiceListUrl != null && this._Company.ServiceListUrl != undefined && this._Company.ServiceListUrl != "") {
            //console.log(this._Company.ServiceListUrl.urlValidator('pdf'));
            const isValid = pdfUrlPattern.test(this._Company.ServiceListUrl);
            // console.log('valid', isValid);
            if (isValid == false) {
                return 'red';
            } else {
                return '#515C66';
            }
        }
        else {
            return '#515C66';
        }
    }

    sanitizeURL(url: string): SafeResourceUrl {
        return this.sanitizer.bypassSecurityTrustResourceUrl(url);
    }
}
