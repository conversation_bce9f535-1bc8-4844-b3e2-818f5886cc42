import { Component, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ConfirmationDialogComponent } from 'src/app/components/confirmation-dialog/confirmation-dialog.component';
import { UserModel } from 'src/app/Model/UserModel';
import { AuthService } from 'src/app/services/auth.service';

import { WebsiteAdminService } from 'src/app/services/website-admin.service';
import { Countries } from 'src/app/utilities/countries';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'website-admins-list',
  templateUrl: './website-admins-list.component.html',
  styleUrls: ['./website-admins-list.component.scss'],
  providers: [MessageService, ConfirmationService]
})
export class WebsiteAdminsListComponent {
  WebsiteAdmins?: UserModel[];
  WebsiteAdmin: UserModel = new UserModel();
  loading: boolean = true;
  formData: UserModel;
  title: string;
  warningMessage: string = '';
  @ViewChild('confirmationDialog') confirmationDialog: ConfirmationDialogComponent;
  confirmationAction: string = '';
  OverrideWarning: boolean = false;
  imageSrc: string = environment.imageSrc;
  totalRecords: number = 0;
  pageSize: number = 10;
  pageIndex: number = 0;
  paginator: boolean = false;

  //filters
  Name: string = '';
  adminType: any;
  CountryId: any;

  //  track the first load
  private isInitialLoad: boolean = false;
  allCountries: Countries = new Countries();

  currentUser: UserModel;
  constructor(
    private WebsiteAdminService: WebsiteAdminService,
    private router: Router,
    private messageService: MessageService, private authService: AuthService) { }


  ngOnInit(): void {
    this.loadData({ first: 0, rows: this.pageSize });
    this.isInitialLoad = true;
    this.title = "Website Admins Management";
    this.currentUser = this.authService.getUserData();
  }

  loadData(event: any) {
    // Avoid double loading during the first render
    if (this.isInitialLoad) {
      this.isInitialLoad = false; // Set the flag to false after the first load
      return;
    }
    const pageNumber = event.first / event.rows + 1; // Calculate the page number
    const pageSize = event.rows; // Rows per page
    this.paginator = true;

    this.fetchWebsiteAdminsData(pageNumber, pageSize);

  }

  fetchWebsiteAdminsData(pageNumber, pageSize) {
    this.WebsiteAdminService
      .GetAllWebsiteAdmins(pageNumber, pageSize, this.Name, this.adminType, this.CountryId)
      .subscribe((data) => {
        this.WebsiteAdmins = data.ListResultContent;
        this.totalRecords = data.TotalRecords; // Set total records for pagination
        this.loading = false;
      });
  }

  edit(e: { WebsiteAdmin?: UserModel; state: string }) {
    if (e.WebsiteAdmin.Active == false) {
      this.messageService.add({
        severity: "warn",
        summary: "Warning",
        detail: "Please activate the Admin before Editing ! ",
      });

    } else {
      this.formData = e.WebsiteAdmin;

      this.WebsiteAdminService.getWebsiteAdminById(e.WebsiteAdmin.Id.toString()).subscribe((data) => {
        this.router.navigate(["website-admin-edit"], {
          state: {
            data: data,
            command: "edit",
          },
        });
      });
    }


  }
  ReceivedFilteredData(event) {
    this.Name = event.Name;
    this.adminType = event.AdminType;
    this.CountryId = event.CountryId;
    this.fetchWebsiteAdminsData(1, this.pageSize);

    this.WebsiteAdmins = event.ListResultContent;
    this.totalRecords = event.TotalRecords; // Set total records for pagination
    //this.paginator = false;

  }

  delete(WebsiteAdmin) {

    this.WebsiteAdmin = WebsiteAdmin;
    this.confirmationDialog.message = 'Do you want to delete this WebsiteAdmin ' + this.WebsiteAdmin.Name;
    this.confirmationDialog.item = this.WebsiteAdmin;
    this.confirmationDialog.openDialog();
    this.confirmationAction = 'delete';
  }

  // Method to handle the confirmation result
  handleConfirmationAction(result: boolean): void {
    if (this.confirmationAction == 'delete') {
      if (result) {
        this.WebsiteAdminService.DeleteWebsiteAdmin(this.WebsiteAdmin.Id).subscribe((data) => {
          if (data['HasError'] == false) {
            if (this.WebsiteAdmin.Name == this.currentUser.UserName) {
              this.authService.logout();
            }
            this.messageService.add({
              severity: "success",
              summary: "Success Message",
              detail: "Delete WebsiteAdmin Successfully",
            });
            // Update the data array (remove the deleted item)
            this.WebsiteAdmins = this.WebsiteAdmins.filter(currentItem => currentItem.Id !== this.WebsiteAdmin.Id);
            this.WebsiteAdmin = new UserModel();
            this.confirmationAction = '';

          } else {
            this.confirmationDialog.item = this.WebsiteAdmin;
            this.confirmationDialog.openDialog();
            this.confirmationDialog.message = data['EnErrorMessage'];
          }
        });
      } else {
        // console.log('Cancelled!');
        this.confirmationAction = '';
        this.WebsiteAdmin = new UserModel();
      }
    }
  }
  editActiveWebsiteAdmin(Id: String) {
    this.WebsiteAdminService
      .EditActiveWebsiteAdmin(Id)
      .subscribe((data: any) => {
        this.WebsiteAdmin = this.WebsiteAdmins.find((x) => x.Id == Id);
        if (data['HasError'] == true) {
          this.confirmationDialog.message = data['EnErrorMessage'];
        }
      });
  }

}
