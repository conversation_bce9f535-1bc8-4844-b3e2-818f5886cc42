import { Component, EventEmitter, Input, OnInit, Output, ChangeDetectorRef, isDevMode } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { DiscountNormalTier } from 'src/app/Model/DiscountNormalTier';
import { DiscountModel } from 'src/app/Model/DiscountModel';
import { TiersModel } from 'src/app/Model/TiersModel';
import { RenewabilityType } from 'src/app/enum/renewbility-type';
import { DiscountService } from 'src/app/services/discount.service';
import { TierService } from 'src/app/services/tier.service';
import { startEndDatesValidator } from 'src/app/validators/start-end-dates.directive';
import { AuthService } from 'src/app/services/auth.service';

interface SelectboxObject {
    name: string;
    value: number;
}

interface Tier {
    name: string;
    offers: number | string;
    renewability: string;
    totalOffersPerYear: number;
}
interface VipTier {
    vipTiers: number;
    offers: number | string;
    renewability: string;
    totalOffersPerYear: number;
}
@Component({
    selector: 'app-timing-details',
    templateUrl: './timing-details.component.html',
    styleUrls: ['./timing-details.component.scss'],
  
})
export class TimingDetailsComponent implements OnInit {
    @Input() _discountData: DiscountModel;
    currentDate: string;
    // dbTiers$: Observable<TiersModel[]>;
    dbTiers$: TiersModel[];
    discountPeriod = [];
    discountDays = [];
    renewbility = [];
    weekDays: SelectboxObject[];
    tiersCols: any[];
    tiersColsVip: any[];
    // tiers: Tier[];
    tiersModels: DiscountNormalTier[];
    vipTier: VipTier[];
    @Input() activeIndex: number;
    @Input() editing: boolean;
    @Output() activeIndexChange: EventEmitter<number> = new EventEmitter();
    pressedNext = false;

    filteredRenewabilityOptions = [];

    timingDetailsForm = new FormGroup({
        period: new FormControl('', [Validators.required]),
        startEndDates: new FormGroup({
            startDate: new FormControl('', [Validators.required]),
            endDate: new FormControl('', [Validators.required]),
        }, { validators: startEndDatesValidator }),
        OfferStartTime: new FormControl(''),
        OfferEndTime: new FormControl(''),
        normalTiers: new FormControl(''),
        vipTier: new FormControl(''),
        discountDays: new FormControl(''),
        groupInfo: new FormGroup({
            offerPerUser: new FormControl('',),
            UnlimitedOffers: new FormControl('',),
            renewbility: new FormControl(''),
            minPeople: new FormControl(''),
            maxPeople: new FormControl(''),
        },
            { validators: this.minValueValidator }),
        // discountTitle: new FormControl('', [Validators.required, Validators.minLength(3)]),
    });

    hasErrorVipNumTier: boolean = false;
    hasErrorNormalOffersNumTier: boolean = false;

    /**
     *
     */
    constructor(private discountService: DiscountService, private router: Router, private messageService: MessageService
        , private _changeDetectorRef: ChangeDetectorRef, private _tierService: TierService
        , private route: ActivatedRoute, private authService: AuthService) {
        this.currentDate = new Date().toISOString().slice(0, 10);

    }

    ngOnInit(): void {
        this.discountPeriod = [
            { id: 0, name: 'Yearly' },
            { id: 1, name: 'One Time Offer' },
        ];
        this.discountDays = [
            { id: 0, name: 'All Days' },
            { id: 1, name: 'Certian Days' },
            { id: 2, name: 'Off Peak Hours' },
        ];

        this.weekDays = [
            { name: 'S', value: 0 },
            { name: 'M', value: 1 },
            { name: 'T', value: 2 },
            { name: 'W', value: 3 },
            { name: 'T', value: 4 },
            { name: 'F', value: 5 },
            { name: 'S', value: 6 },
        ];
        this.tiersCols = [
            { field: 'name', header: 'tier name' },
            { field: 'offers', header: '#offers' },
            { field: 'renewability', header: 'renewability' },
            { field: 'totalOffersPerYear', header: 'total offers per year' }
        ];
        this.tiersColsVip = [
            // { field: 'vipTiers', header: '#VIP tiers' },
            { field: 'name', header: 'tier name' },
            { field: 'vipUsers', header: '#VIP Users' },
            { field: 'offers', header: '#offers' },
            { field: 'renewability', header: 'renewability' },
            { field: 'totalOffersPerYear', header: 'total offers per year' }
        ];
        this.renewbility = Object.keys(RenewabilityType)
            .filter(key => isNaN(Number(key))) // Filter out the numeric keys added by TypeScript
            .map(key => {
                return {
                    id: RenewabilityType[key],
                    name: key
                };
            });
        if (!this.editing) {
            this._discountData.IsVipTier = false;
            this._discountData.IsNormalTier = !this._discountData.IsVipTier;
        }
        else {
            this._discountData.IsVipTier = !this._discountData.IsNormalTier;
            //this._discountData.DayOfWeeks = [];
            // this._discountData.DiscountDays.forEach(element => {
            //     this._discountData.DayOfWeeks.push(this.weekDays.filter(x => x.value == element.DayOfWeek)[0].value)
            // });
        }
        // this._discountData.ApprovedByAdmin = !Boolean(this.user.UserType);

        this._discountData.DiscountPeriod = this._discountData.DiscountPeriod ? this._discountData.DiscountPeriod : 0;
        //this.dbTiers$ = this._tierService.getAllTiers();
        this.changePeriod();
    }

    changeNormal() {
        this._discountData.IsNormalTier = !this._discountData.IsVipTier;
        if (this._discountData.IsVipTier) {
            // OffersNum: 0,
            //                     RenewabilityType: RenewabilityType.Monthly,
            //                     TotalOfferPerYear: 0,
            //                     UnlimitedOffers: false,
            this._discountData.NormalTiersValues.forEach((key) => {
                key.UnlimitedOffers = false;
                key.OffersNum = 0;
                key.RenewabilityType = RenewabilityType.Monthly;

            })

        }
        if (this._discountData.IsNormalTier) {
            // OffersNum: 0,
            //                     RenewabilityType: RenewabilityType.Monthly,
            //                     TotalOfferPerYear: 0,
            //                     UnlimitedOffers: false,
            this._discountData.VipTiersValues.forEach((key) => {
                key.UnlimitedOffers = false;
                key.OffersNum = 0;
                key.RenewabilityType = RenewabilityType.Monthly;

            })

        }
    }

    onTierChange(tier: any, isChecked: boolean) {
        tier.Checked = isChecked;
        // this._discountData.TiersList = this.dbTiers$.filter(t => t.Checked);
    }

    changeVip() {
        this._discountData.IsVipTier = !this._discountData.IsNormalTier;
        if (this._discountData.IsNormalTier) {
            // OffersNum: 0,
            //                     RenewabilityType: RenewabilityType.Monthly,
            //                     TotalOfferPerYear: 0,
            //                     UnlimitedOffers: false,
            this._discountData.VipTiersValues.forEach((key) => {
                key.UnlimitedOffers = false;
                key.OffersNum = 0;
                key.RenewabilityType = RenewabilityType.Monthly;

            })

        }
        if (this._discountData.IsVipTier) {
            // OffersNum: 0,
            //                     RenewabilityType: RenewabilityType.Monthly,
            //                     TotalOfferPerYear: 0,
            //                     UnlimitedOffers: false,
            this._discountData.NormalTiersValues.forEach((key) => {
                key.UnlimitedOffers = false;
                key.OffersNum = 0;
                key.RenewabilityType = RenewabilityType.Monthly;

            })

        }
    }

    back() {
        this.activeIndex--;
        this.activeIndexChange.emit(this.activeIndex);
    }

    save() {

        if (this._discountData.DiscountType == 0 && (this.hasErrorVipNumTier || this.hasErrorNormalOffersNumTier)) {
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please Check Discount Tiers' });
            return;
        }
        if (this._discountData.DiscountType == 1 && this._discountData.groupInfo.UnlimitedOffers == false && (this._discountData.groupInfo.OfferPerUser == null || this._discountData.groupInfo.OfferPerUser == 0)) {
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please Check Discount Group Information' });
            return;
        }
        delete this._discountData.IsVipTier;

        if (this._discountData.DiscountDayType == 0) { // all days
            this._discountData.DayOfWeeks = [0, 1, 2, 3, 4, 5, 6];
            this._discountData.DiscountDayType = 0;
        }
        if (this._discountData.DiscountDayType == 1) { // CertiaDays
            this._discountData.DiscountDayType = 1;
        }
        if (this._discountData.DiscountDayType == 2) { // OffPeakHours
            this._discountData.DayOfWeeks = [];
            this._discountData.DiscountDayType = 2;
        }
        this.pressedNext = true;
        if (!this.timingDetailsForm.valid) {
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Check your inputs' });
            return;
        }

        // if(this._discountData.IsNormalTier==true && ){

        // }
        this._discountData.SwipeCode = this._discountData.SwipeCode ? this._discountData.SwipeCode.toString() : '';

        delete this._discountData.Active;
        delete this._discountData.LogoUrl;
        delete this._discountData.CompanyId;
        delete this._discountData.UserFriendly;

        if (this._discountData.Company) {
            const attributesToKeepInCompany = ['Id', 'EnName', 'ArName'];
            // Delete attributes not in attributesToKeepInCompany
            Object.keys(this._discountData.Company).forEach((key) => {
                if (!attributesToKeepInCompany.includes(key)) {
                    delete this._discountData.Company[key];
                }
            });
        }
        // delete this._discountData.DiscountFilters;
        //  delete this._discountData.DiscountBranches;
        // delete this._discountData.DiscountConditions;
        delete this._discountData.DiscountStatuses;
        delete this._discountData.NoteDiscount;
        delete this._discountData.VisibleDialog;
        // this._discountData.TiersList = this.dbTiers$!.filter(i => i.Checked);


        let form: FormData = new FormData();
        if (this._discountData.Logo)
            form.append('Logo', this._discountData.Logo);
        form.append('request', JSON.stringify(this._discountData));

        if (this.editing) {
            this.discountService.EditDiscount(form).subscribe(data => {
                if (data.HasError) {
                    console.log('post result', data);
                }
                else {
                    this.router.navigate(['discounts-inbox'])
                }
            });
        }
        else {
            this.discountService.AddDiscount(form).subscribe((data) => {
                if (data.HasError) {
                    console.log('post result', data);
                }
                else {
                    this.router.navigate(['discounts-inbox'])
                    // if (!isDevMode()) {
                    // if (this.user.UserType === 0) {
                    //     this.router.navigate(['reviewed'])
                    // } else {
                    //     this.router.navigate(['inbox'])
                    // }
                    // }
                }
            })
        }
    }

    CheckValid(input: FormControl) {
        if (input.invalid && (this.pressedNext || input.touched)) {
            return 'red';
        }
        return '#515C66';
    }

    changePeriod() {
        if (this._discountData.DiscountPeriod === 0) {
            let lastDateOfThisYear = new Date(new Date().getFullYear(), 11, 31)
            let now = new Date();
            // let lastDateOfCurrentYear = `${now.getFullYear()}-${now.getMonth() + 1}-${now.getDate()}`

            this._discountData.StartDate = now;
            this._discountData.EndDate = lastDateOfThisYear;
            this._changeDetectorRef.detectChanges();
        } else if (this._discountData.StartDate == null) {
            this._discountData.StartDate = null;
            this._discountData.EndDate = null;
        }
    }

    calculateTotalOfferPerYear(renewabilityType, offersNum) {
        if (renewabilityType === 0) {
            return offersNum
        }
        var edt = new Date(this._discountData.EndDate);
        var sdt = new Date(this._discountData.StartDate);
        return offersNum * (edt.getMonth() - sdt.getMonth() + 1)
    }

    minValueValidator(control) {
        const minValue = control.get('minPeople').value;
        const maxValue = control.get('maxPeople').value;

        if (minValue !== '' && maxValue !== '' && minValue >= maxValue) {

            control.get('minPeople').setErrors({ 'minPeopleError': true });
            control.get('maxPeople').setErrors({ 'maxPeopleError': true });
            return { 'invalidRange': true };
        } else {

            control.get('minPeople').setErrors(null);
            control.get('maxPeople').setErrors(null);
            return null;

        }

        return null;
    }


    // Function to validate the values based on tier type
    isValidValueTier(value: number, UnlimitedOffers: boolean, typeTier: string): boolean {

        if (typeTier == 'vipTier') {
            if (UnlimitedOffers == false && (value == null || value == 0)) {
                this.hasErrorVipNumTier = true;
                return false;

            }
            else {
                this.hasErrorVipNumTier = false
                return true;
            }
        } else if (typeTier == 'normalTier') {
            if (UnlimitedOffers == false && (value == null || value == 0)) {
                this.hasErrorNormalOffersNumTier = true;
                return false;
            }
            else {
                this.hasErrorNormalOffersNumTier = false
                return true;
            }
        }
        return true;
    }

    onUnlimitedChange(newValue: number) {
        this._discountData.groupInfo.OfferPerUser = null;
    }
    onInputVipTierValueChange(item) {
        if (item.OffersNum == null || item.UsersNum == null) {
            item.OffersNum = 0;
            item.UsersNum = 0;
        }
    }
    onInputNormalTierValueChange(item) {
        if (item.OffersNum == null) {
            item.OffersNum = 0;
        }
    }

}
