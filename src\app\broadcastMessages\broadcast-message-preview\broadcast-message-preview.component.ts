import { Location } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { BroadcastMessageActionLogEnum } from 'src/app/enum/broadcast-message-action-log-enum';
import { BroadcastMessageModel } from 'src/app/Model/BroadcastMessageModel';
import { BroadcastMessageStatusViewModel } from 'src/app/Model/BroadcastMessageStatusViewModel';
import { UserModel } from 'src/app/Model/UserModel';
import { AuthService } from 'src/app/services/auth.service';
import { BroadcastMessagesService } from 'src/app/services/broadcast-messages.service';
import { Countries } from 'src/app/utilities/countries';
import { environment } from 'src/environments/environment';
@Component({
    selector: 'app-broadcast-message-preview',
    templateUrl: './broadcast-message-preview.component.html',
    styleUrls: ['./broadcast-message-preview.component.scss'],
  
})
export class BroadcastMessagePreviewComponent implements OnInit {

    BroadcastMessage: BroadcastMessageModel;
    selected: any[];
    routeState: any;
    checkIfApproved: boolean;
    checkIfRejected: boolean;
    rejectDialogVisibility: boolean = false;
    rejectReasonControl: FormControl;
    user: UserModel;
    allCountries: Countries = new Countries();

    constructor(private router: Router, private BroadcastMessageService: BroadcastMessagesService, private messageService: MessageService, private location: Location, private authService: AuthService) {
        this.user =  this.authService.getUserData();
        if (this.router.getCurrentNavigation()?.extras.state) {
            this.routeState = this.router.getCurrentNavigation()?.extras.state;

            if (this.routeState) {
                this.BroadcastMessage = this.routeState.data ? this.routeState.data : new BroadcastMessageModel();
            }
        } else {
            this.BroadcastMessage = new BroadcastMessageModel();
        }
        // console.log('BroadcastMessage', this.BroadcastMessage)
        this.rejectReasonControl = new FormControl('', [Validators.required]);
    }

    ngOnInit(): void {

        this.checkBroadcastMessageApprovedRejected();

        // console.log(this.checkIfRejected, this.checkIfApproved);

    }

    checkBroadcastMessageApprovedRejected() {
        console
        if (this.user.UserType == 1) {
            this.checkIfRejected = true;
            this.checkIfApproved = true;
        } else
            if (this.BroadcastMessage.Status == 2) { // Rejected : show approved button only 
                this.checkIfApproved = false;
                this.checkIfRejected = true;
            }
        if (this.BroadcastMessage.Status == 1) {  //approved  : show rejected button only 

            this.checkIfRejected = false;
            this.checkIfApproved = true;
        }

        if (this.BroadcastMessage.Active == false) { // diactivated
            this.checkIfRejected = true;
            this.checkIfApproved = true;
        }
        if (this.BroadcastMessage.Status == 3) { // sent : hide buttons 
            this.checkIfRejected = true;
            this.checkIfApproved = true;
        }
    }

    Accept() {
        let _BroadcastMessage = new BroadcastMessageStatusViewModel();
        _BroadcastMessage.Id = this.BroadcastMessage.Id;
        _BroadcastMessage.Note = this.BroadcastMessage.Note;
        _BroadcastMessage.Status = BroadcastMessageActionLogEnum.Approved;

        this.BroadcastMessageService.AcceptBroadcastMessage(_BroadcastMessage).subscribe((data) => {
            if (!data.HasError) {
                this.router.navigate(['broadcast-messages-reviewed']);
            }
            else {
                this.messageService.add({ severity: 'error', summary: 'Error', detail: data.EnErrorMessage });
            }
        })
    }
    Reject() {
        if (this.rejectReasonControl.valid) {
            let _BroadcastMessage = new BroadcastMessageStatusViewModel();
            _BroadcastMessage.Id = this.BroadcastMessage.Id;
            _BroadcastMessage.Note = this.BroadcastMessage.Note;
            _BroadcastMessage.Status = BroadcastMessageActionLogEnum.Rejected;

            this.BroadcastMessageService.RejectBroadcastMessage(_BroadcastMessage).subscribe((data) => {
                if (!data.HasError) {
                    this.router.navigate(['broadcast-messages-reviewed']);
                }
                else {
                    this.messageService.add({ severity: 'error', summary: 'Error', detail: data.EnErrorMessage });
                }
            });
        } else {
            this.rejectReasonControl.markAsTouched();
        }
    }
    showRejectReasonDialog() {
        this.rejectDialogVisibility = true;
    }

    getImageUrl(image) {
        return image ? environment.imageSrc + image : '';
        // return logo ? this.sanitizer.bypassSecurityTrustUrl(environment.imageSrc + logo) : '';
    }
    goBack() {
        this.location.back();
    }
}
