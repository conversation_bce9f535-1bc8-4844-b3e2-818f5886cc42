import { Location } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { DiscountModel } from 'src/app/Model/DiscountModel';
import { DiscountStatusViewModel } from 'src/app/Model/DiscountStatusViewModel';
import { UserModel } from 'src/app/Model/UserModel';
import { DiscountActionLogEnum } from 'src/app/enum/discount-action-log-enum';
import { AuthService } from 'src/app/services/auth.service';
import { DiscountService } from 'src/app/services/discount.service';
import { environment } from 'src/environments/environment';

interface SelectboxObject {
    name: string;
    value: number;

}
@Component({
    selector: 'app-dicount-approvment',
    templateUrl: './dicount-approvment.component.html',
    styleUrls: ['./dicount-approvment.component.scss'],
  
})
export class DicountApprovmentComponent implements OnInit {

    discount: DiscountModel;
    weekDays: SelectboxObject[];
    selected: any[];
    routeState: any;
    checkIfApproved: boolean;
    checkIfRejected: boolean;
    rejectDialogVisibility: boolean = false;
    rejectReasonControl: FormControl;
    user: UserModel;

    constructor(private router: Router, private discountService: DiscountService, private messageService: MessageService, private location: Location, private authService: AuthService) {
        this.user =  this.authService.getUserData();
        if (this.router.getCurrentNavigation()?.extras.state) {
            this.routeState = this.router.getCurrentNavigation()?.extras.state;
            this.weekDays = [
                { name: 'S', value: 0 },
                { name: 'M', value: 1 },
                { name: 'T', value: 2 },
                { name: 'W', value: 3 },
                { name: 'T', value: 4 },
                { name: 'F', value: 5 },
                { name: 'S', value: 6 },
            ];
            if (this.routeState) {
                this.discount = this.routeState.data ? this.routeState.data : new DiscountModel();
            }
        } else {
            this.discount = new DiscountModel();
        }
        this.rejectReasonControl = new FormControl('', [Validators.required]);
    }

    ngOnInit(): void {
        this.selected = [];
        // this.discount.DayOfWeeks.forEach(element => {
        //     this.selected.push(this.weekDays.filter(x => x.value == element.DayOfWeek)[0].value)
        // });
        // this.checkIfApproved = this.discount.DiscountStatuses[0].Status == 3;
        //his.checkIfRejected = this.discount.DiscountStatuses[0].Status == 1;
        this.checkDiscountApprovedRejected();

    }

    checkDiscountApprovedRejected() {
        if (this.user.UserType == 1) {
            this.checkIfRejected = true;
            this.checkIfApproved = true;
        } else
            if (this.discount.Status == 2)  // Rejected : show approved button only 
            {
                this.checkIfApproved = false;
                this.checkIfRejected = true;
            }
        if (this.discount.Status == 1) //approved  : show rejected button only 
        {
                this.checkIfRejected = false;
                this.checkIfApproved = true;
        }
        // if (this.discount.Status == 4) { // Expired
        //     this.checkIfRejected = true;
        //     this.checkIfApproved = true;
        // }
        if (this.discount.Active == false) { // diactivated
            this.checkIfRejected = true;
            this.checkIfApproved = true;
        }
        if (this.discount.Status == 3 || this.discount.Status == 4) { // running or Expired : hide buttons  
            this.checkIfRejected = true;
            this.checkIfApproved = true;
        }
    }

    Accept() {
        let _discount = new DiscountStatusViewModel();
        _discount.Id = this.discount.Id;
        _discount.Note = this.discount.NoteDiscount;
        _discount.Status = DiscountActionLogEnum.Approved;

        this.discountService.AcceptDiscount(_discount).subscribe((data) => {
            if (!data.HasError) {
                this.router.navigate(['discounts-reviewed']);
            }
            else {
                this.messageService.add({ severity: 'error', summary: 'Error', detail: data.EnErrorMessage });
            }
        })
    }
    Reject() {
        if (this.rejectReasonControl.valid) {
            let _discount = new DiscountStatusViewModel();
            _discount.Id = this.discount.Id;
            _discount.Note = this.discount.NoteDiscount;
            _discount.Status = DiscountActionLogEnum.Rejected;

            this.discountService.RejectDiscount(_discount).subscribe((data) => {
                if (!data.HasError) {
                    this.router.navigate(['discounts-reviewed']);
                }
                else {
                    this.messageService.add({ severity: 'error', summary: 'Error', detail: data.EnErrorMessage });
                }
            });
        } else {
            this.rejectReasonControl.markAsTouched();
        }
    }
    showRejectReasonDialog() {
        this.rejectDialogVisibility = true;
    }

    getImageUrl(image) {
        return image ? environment.imageSrc + image : '';
        // return logo ? this.sanitizer.bypassSecurityTrustUrl(environment.imageSrc + logo) : '';
    }
    goBack() {
        this.location.back();
    }
}
