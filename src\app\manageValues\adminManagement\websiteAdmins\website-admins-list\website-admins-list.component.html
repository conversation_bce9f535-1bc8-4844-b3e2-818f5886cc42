<p-toast></p-toast>
<div class="grid">
      <div class="col-12">
            <div>
                  <p-confirmDialog header="Confirmation" key="confirm1" icon="pi pi-exclamation-triangle"
                        message="Are you sure you want to proceed?" [style]="{ width: '350px' }"
                        acceptButtonStyleClass="p-button-text" rejectButtonStyleClass="p-button-text"></p-confirmDialog>
                  <p-table #dt [value]="WebsiteAdmins" [paginator]="paginator" [rows]="pageSize"
                        [totalRecords]="totalRecords" [lazy]="true" [loading]="loading" (onLazyLoad)="loadData($event)"
                        [showCurrentPageReport]="true"
                        [currentPageReportTemplate]="'Showing {first} to {last} of {totalRecords} records'"
                        responsiveLayout="scroll" [rowHover]="true" styleClass="p-datatable-gridlines">

                        <ng-template pTemplate="caption">
                              <app-grid-headers [myDt]="WebsiteAdmins" (SearchEvent)='ReceivedFilteredData($event)'
                                    addNewTxt="Add New Website Admin" goRoute="/website-admin-new"
                                    gridTitle="WebsiteAdminsManagement" [title]="title">
                              </app-grid-headers>
                        </ng-template>
                        <ng-template pTemplate="header">
                              <tr>
                                    <th>

                                    </th>
                                    <th>
                                          <div class="flex justify-content-between align-items-center">
                                                {{ "Admin Name" | translate }}
                                          </div>
                                    </th>
                                    <th>
                                          <div class="flex justify-content-between align-items-center">
                                                {{ "Type" | translate }}
                                          </div>
                                    </th>
                                    <th>
                                          <div class="flex justify-content-between align-items-center">
                                                {{ "Countries" | translate }}
                                          </div>
                                    </th>
                                    <th>
                                          <div class="flex justify-content-between align-items-center">
                                                {{ "Edit" | translate }}
                                          </div>
                                    </th>
                              </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-WebsiteAdmin>
                              <tr [ngClass]="{'inactive-table-row': WebsiteAdmin.Active === false}">
                                    <td>
                                          <div class="d-flex align-items-center">
                                                <p-inputSwitch [(ngModel)]="WebsiteAdmin.Active"
                                                      (onChange)="editActiveWebsiteAdmin(WebsiteAdmin.Id)"></p-inputSwitch>
                                          </div>
                                    </td>
                                    <td>
                                          <div class="flex">
                                                <p-avatar *ngIf="WebsiteAdmin.ProfilePhotoUrl"
                                                      image="{{imageSrc + WebsiteAdmin.ProfilePhotoUrl}}"
                                                      styleClass="mr-2" size="medium" shape="circle"></p-avatar>
                                                {{ WebsiteAdmin.Name }}
                                          </div>
                                    </td>
                                    <td>
                                          <div class="flex">
                                                <p-tag severity="success" *ngIf="WebsiteAdmin.AdminType==1"
                                                      styleClass="mr-2" icon="pi pi-user"
                                                      value="{{WebsiteAdmin.AdminType | AdminType}}"></p-tag>
                                                <p-tag severity="warning" *ngIf="WebsiteAdmin.AdminType==0" styleClass="mr-2"
                                                      icon="fas fa-user-shield"
                                                      value="{{WebsiteAdmin.AdminType | AdminType}}"></p-tag>
                                          </div>
                                    </td>
                                    <td>
                                          <div class="flex">
                                                <div *ngIf="WebsiteAdmin.AdministeredCountries.length>0">
                                                      <span *ngFor="let Country of WebsiteAdmin.AdministeredCountries">
                                                            <img src="assets/demo/flags/flag_placeholder.png"
                                                                  [class]="'flag flag-' +  this.allCountries.getCodeFromName( Country.EnName)"
                                                                  alt="{{  Country.EnName }}" />
                                                            &nbsp;
                                                      </span>
                                                </div>

                                                <span *ngIf="WebsiteAdmin.AdministeredCountries.length==0"> All
                                                      Countries</span>
                                          </div>
                                    </td>
                                    <td>
                                          <div class="flex">
                                                <div class="d-flex align-items-center justify-content-between">
                                                      <p-button icon="pi pi-pencil" styleClass="p-button-rounded"
                                                            class="mx-1"
                                                            (click)=" edit({ WebsiteAdmin: WebsiteAdmin, state: 'edit' })"></p-button>
                                                      <p-button icon="pi pi-trash"
                                                            styleClass="p-button-rounded p-button-danger" class="mx-1"
                                                            (click)="delete(WebsiteAdmin)"></p-button>
                                                </div>
                                          </div>
                                    </td>
                              </tr>
                        </ng-template>
                        <ng-template pTemplate="emptymessage">
                              <tr>
                                    <td class="text-center" colspan="7">
                                          {{ "No Website Admins found." | translate }}
                                    </td>
                              </tr>
                        </ng-template>
                  </p-table>
                  <!-- Reusable Confirmation Dialog Component -->
                  <app-confirmation-dialog #confirmationDialog
                        (confirmResult)="handleConfirmationAction($event)"></app-confirmation-dialog>

            </div>
      </div>
</div>