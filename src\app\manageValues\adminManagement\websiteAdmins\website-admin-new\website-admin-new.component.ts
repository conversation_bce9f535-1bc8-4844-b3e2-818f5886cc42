import { ChangeDetectorRef, Component, ElementRef, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { Password } from 'primeng/password';
import { MenuService } from 'src/app/app.menu.service';
import { AdminType } from 'src/app/enum/admin-type';
import { WebsiteAdminPageEnum, WebsiteAdminPageEnumIconMap } from 'src/app/enum/website-admin-pages-enum';
import { UserModel } from 'src/app/Model/UserModel';
import { AuthService } from 'src/app/services/auth.service';
import { CountryService } from 'src/app/services/country.service';
import { UserService } from 'src/app/services/user-service.service';
import { WebsiteAdminService } from 'src/app/services/website-admin.service';
import { EmailValidator } from 'src/app/validators/email.validators';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-website-admin-new',
  templateUrl: './website-admin-new.component.html',
  styleUrls: ['./website-admin-new.component.scss'],

})
export class WebsiteAdminNewComponent {
  @ViewChild('PasswordInput') PasswordInput: Password;
  @ViewChild('emailInput') emailInput: ElementRef;
  websiteAdmin: UserModel = new UserModel();
  pressedSave: boolean = false;
  isEditing: boolean = false;
  title: string = 'Add New Website Admin';
  routeState: any;
  fileToUpload: any[] = [];
  websiteAdminForm = new FormGroup({
    Name: new FormControl('', [Validators.required, Validators.minLength(3)]),
    Email: new FormControl('', [Validators.required, EmailValidator.isValidEmailFormat]),
    Password: new FormControl('', [Validators.required, Validators.pattern('(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[$@$!%*?&])[A-Za-z\d$@$!%*?&].{8,}')]),
    AdminType: new FormControl('', [Validators.required]),
    AdministeredCountries: new FormControl(''),
    AdministeredPages: new FormControl(''),
    MaxServiceDiscount: new FormControl('', [Validators.required]),
    ProfilePhotoUrl: new FormControl('',),
  });
  adminEmail: string = '';
  ProfilePhotoUrl: any = '';
  adminTypes = [];
  countries: any = [];
  pagesList: { name: string, value: number }[] = [];
  WebsiteAdminPageEnum = WebsiteAdminPageEnum;
  WebsiteAdminPageEnumIconMap = WebsiteAdminPageEnumIconMap;
  currentUser: UserModel;

  constructor(private messageService: MessageService, private websiteAdminService: WebsiteAdminService, private fb: FormBuilder, private router: Router, private ref: ChangeDetectorRef, private route: ActivatedRoute, private sanitizer: DomSanitizer, private userService: UserService, private countryService: CountryService, private authService: AuthService, private menuService: MenuService) {

    if (this.router.getCurrentNavigation()?.extras.state) {

      this.routeState = this.router.getCurrentNavigation()?.extras.state;
      if (this.routeState) {
        this.websiteAdmin = this.routeState.data
          ? this.routeState.data
          : new UserModel();
        if (this.websiteAdmin.Id == "") {
          this.isEditing = false;
        }
        else {
          this.isEditing = true;
        }
      }
    }
    if (this.route.snapshot.url[this.route.snapshot.url.length - 1].path === 'website-admin-edit') {
      this.isEditing = true;
      this.title = 'Edit Website Admin';
    }
  }

  ngOnInit() {
    this.currentUser = this.authService.getUserData();
    if (this.isEditing) {
      this.websiteAdminForm.controls.Password.clearValidators();
      this.adminEmail = this.websiteAdmin.Email;
    } else {
      setTimeout(() => {
        this.websiteAdminForm.controls.Password.reset()
        this.websiteAdminForm.reset()
      }, 900);
    }
    if (this.isEditing == true && this.websiteAdmin.Id == "") {
      this.router.navigate(['/website-admins-management']);
      return;
    }

    if (this.websiteAdmin.ProfilePhotoUrl) {
      this.ProfilePhotoUrl = this.websiteAdmin.ProfilePhotoUrl ? this.sanitizer.bypassSecurityTrustUrl(environment.imageSrc + this.websiteAdmin.ProfilePhotoUrl) : '';
      //this.websiteAdminForm.controls.ProfilePhotoUrl= this.websiteAdmin.ProfilePhotoUrl.toString() 
    }

    this.adminTypes = Object.keys(AdminType)
      .filter(key => isNaN(Number(key)))  // Filter out numeric keys (enum reverse keys)
      .map(key => ({
        label: key,
        value: AdminType[key]
      }));
    this.initializePagesList()
    this.countryService.Countries.subscribe((data) => {
      this.countries = data.map(key => ({
        Id: key.Id,
        ArName: key.ArName,
        EnName: key.EnName,
        FlagUrl: ''
      }));
    });

  }

  save() {
    console.log('form', this.websiteAdminForm, 'data', this.websiteAdmin)
    //  this.onEmailInputFocusOut();
    this.pressedSave = true;
    // Mark all controls as touched to trigger validation
    this.websiteAdminForm.markAllAsTouched();

    if (this.isEditing) {
      if (this.passwordControl.value && this.passwordControl.value.trim() !== '') {
        this.passwordControl.setValidators([Validators.required, Validators.pattern('(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[$@$!%*?&])[A-Za-z\d$@$!%*?&].{8,}')])
        // this.passwordControl.setErrors({ 'ErrorRequired': true });
      }
    }
    if (!this.ProfilePhotoUrl) {
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please Upload Profile Photo !' });
      return;
    }
    if (this.websiteAdminForm.getError('EmailError') || this.websiteAdminForm.getError('ErrorRequired')) {
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'This email is already used. Please enter another email' });
      return;
    }
    if (this.websiteAdmin.AdminType == 1) { //sub Admin
      if (this.websiteAdmin.AdministeredPages.length == 0) {
        this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Check your input' });
        return;
      }
      if (this.websiteAdmin.AdministeredCountries.length == 0) {
        this.websiteAdminForm.get('AdministeredCountries').setValidators([Validators.required,]);
        this.websiteAdminForm.get('AdministeredCountries')?.updateValueAndValidity()
        this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Check your input' });
        return;
      }
    }
    if (!this.websiteAdminForm.valid) {
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Check your input' });
      return;
    } else {
      if (this.websiteAdmin.AdminType == 0)// SuperAdmin
      {
        this.websiteAdminForm.get('AdministeredCountries')?.clearValidators();
        this.websiteAdminForm.get('AdministeredCountries')?.reset();
        this.websiteAdminForm.get('AdministeredCountries')?.updateValueAndValidity()
        this.websiteAdmin.AdministeredCountries = [];
        this.websiteAdmin.AdministeredPages = [];
      }

      let form: FormData = new FormData();
      if (this.websiteAdmin.ProfilePhoto)
        form.append('ProfilePhoto', this.websiteAdmin.ProfilePhoto);
      delete this.websiteAdmin.ProfilePhotoUrl;
      delete this.websiteAdmin.ProfilePhoto;

      form.append('request', JSON.stringify(this.websiteAdmin));

      if (this.isEditing) {
        this.websiteAdminService.EditWebsiteAdmin(form).subscribe(data => {
          if (data.HasError) {
            console.log('post result', data);
          }
          else {
            if (this.adminEmail == this.currentUser.Email) {
              this.authService.updateCurrentUser(data.ResultContent.Email, data.ResultContent.Name, 0, data.ResultContent.AdminType, data.ResultContent.AdministeredPages, data.ResultContent.ProfilePhotoUrl);
              // Notify the side menu to refresh
              this.menuService.notifyMenuRefresh();
              // this.router.navigate(['/']);
              // return;
            }
            this.router.navigate(['website-admins-management']);
          }
        });

      }
      else {
        this.websiteAdminService.AddWebsiteAdmin(form).subscribe((data) => {
          if (data.HasError) {
            console.log('post result', data);
          }
          else {
            this.router.navigate(['website-admins-management'])
          }
        })
      }
    }
  }

  initializePagesList() {
    this.pagesList = Object.keys(WebsiteAdminPageEnum)
      .filter(key => isNaN(Number(key))) // Exclude numeric keys from reverse mapping
      .map(key => ({ name: key, value: WebsiteAdminPageEnum[key] }));
  }

  get columns() {
    const mid = Math.ceil(this.pagesList.length / 2);
    return [this.pagesList.slice(0, mid), this.pagesList.slice(mid)];
  }

  uploadFile(files: any) {

    if (files.length === 0) {
      return;
    }
    var file = <File>files[0];
    const reader = new FileReader();
    this.websiteAdmin.ProfilePhoto = file;
    reader.readAsDataURL(file);
    reader.onload = (e: any) => {
      this.ProfilePhotoUrl = e.target.result;
    }
    this.fileToUpload.push(file);
    this.ProfilePhotoUrl = URL.createObjectURL(this.fileToUpload[0]);
  }


  clearFileInput() {
    this.fileToUpload = [];
    this.ProfilePhotoUrl = '';
  }

  // Optional method to check password control errors programmatically
  get passwordControl() {
    return this.websiteAdminForm.get('Password');
  }

  onEmailInputFocusOut(): void {
    // Your logic here, triggered when the input field loses focus
    var data = {
      'Email': this.websiteAdmin.Email,
    };
    if (!this.isEditing) {
      if (this.websiteAdmin.Email != null)
        this.userService.CheckUserEmailExist(data)
          .subscribe((data) => {
            if (data['HasError'] == true) {
              // this.messageService.add({ severity: 'error', summary: 'Error', detail: data['EnErrorMessage'] });
              this.websiteAdminForm.get('email').setErrors({ 'EmailError': true });

            }
          });
    } else {
      if (this.adminEmail != this.websiteAdmin.Email && this.isEditing)
        this.userService.CheckUserEmailExist(data)
          .subscribe((data) => {
            if (data['HasError'] == true) {
              // this.messageService.add({ severity: 'error', summary: 'Error', detail: data['EnErrorMessage'] });
              this.websiteAdminForm.get('email').setErrors({ 'EmailError': true });
            }
          });
    }
  }

  CheckValid(input: FormControl) {
    if (input.invalid && (this.pressedSave || input.touched)) {
      return 'red';
    }
    return '#515C66';
  }

}
