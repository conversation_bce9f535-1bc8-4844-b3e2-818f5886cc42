import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
      name: 'AmPmTime'
})
export class AmPmTimePipe implements PipeTransform {
      transform(value: string): string {
            // Assume the input is in HH:mm (24-hour) format
            if (!value) return '';

            const [hour, minute] = value.split(':').map(Number);
            if (isNaN(hour) || isNaN(minute)) return value;

            const period = hour >= 12 ? 'PM' : 'AM';
            const adjustedHour = hour % 12 || 12; // Convert 0 to 12 for AM

            return `${adjustedHour}:${minute.toString().padStart(2, '0')} ${period}`;
      }
}