import { Component, EventEmitter, Input, Output } from '@angular/core';
import { MessageService } from 'primeng/api';
import { SubscriptionModel } from 'src/app/Model/SubscriptionModel';
import { Countries } from 'src/app/utilities/countries';

@Component({
  selector: 'subscription-statistics',
  templateUrl: './subscription-statistics.component.html',
  styleUrls: ['./subscription-statistics.component.scss']
})
export class SubscriptionStatisticsComponent {
 @Input() _SubscriptionData: SubscriptionModel;
  @Input() isEditing: boolean;
  @Input() activeIndex: number;
  @Output() activeIndexChange: EventEmitter<number> = new EventEmitter();
  pressedNext = false;
  SubscriptionDataArray: any[] = [];
  allCountries: Countries = new Countries();
  constructor(private messageService: MessageService) {
  }

  ngOnInit(): void {
    // Convert the object to an array with key-value pairs
    this.SubscriptionDataArray = [
      { TotalCountries: this._SubscriptionData.TotalCountries, TotalCompanies: this._SubscriptionData.TotalCompanies }
    ];
    console.log('Statistics', this._SubscriptionData.CompanyStatistics)
  }

}
