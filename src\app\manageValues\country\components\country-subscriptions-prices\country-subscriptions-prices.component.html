<p-toast></p-toast>

<div>

      <!--  Subscriptions -->
      <div class="mb-1">Company Subscriptions : </div>
      <a *ngIf="SubscriptionStatusMessage!=''" style="color: red">{{SubscriptionStatusMessage}}</a>
      <p-card>
            <p-table [value]="Subscriptions" [tableStyle]="{ 'min-width': '50rem' }"
                  styleClass="p-datatable-gridlines p-datatable-sm">
                  <ng-template pTemplate="caption"> Subscriptions </ng-template>
                  <ng-template pTemplate="header">
                        <tr>
                              <th>Subscription Name</th>
                              <th>Fee Per Year</th>
                        </tr>
                  </ng-template>
                  <ng-template pTemplate="body" let-Subscription>
                        <tr>
                              <td>
                                    {{ Subscription.EnName }}
                              </td>
                              <td>
                                    <p-inputNumber [suffix]="' ' + _CountryData.Currency" [useGrouping]="false"
                                          id="FeePerYear" [(ngModel)]="Subscription.FeePerYear" [min]="0" />
                              </td>
                        </tr>
                  </ng-template>
            </p-table>
      </p-card>
      <p-card>
            <div class="flex flex-column gap-2 mb-3">
                  <label class="mb-1">Subscriptions Discount</label>
                  <p-inputSwitch [ngModelOptions]="{ standalone: true }"
                        [(ngModel)]="_CountryData.IsSubsciptionsDiscountEnabled"
                        (onChange)="onChangeIsSubsciptionsDiscountEnabled()"></p-inputSwitch>
                  <!-- <span>{{ isSubscriptionDiscount ? 'On' : 'Off' }}</span> -->
                  <a *ngIf="_CountryData.IsSubsciptionsDiscountEnabled && !_CountryData.SubsciptionsDiscountValue"
                        style="color: red">Please Insert Subscriptions Discount Value </a>
                  <div *ngIf="_CountryData.IsSubsciptionsDiscountEnabled">


                        <label class="mb-1">Value:</label>
                        <p-inputNumber id="SubsciptionsDiscountValue" [min]="0"
                              [(ngModel)]="_CountryData.SubsciptionsDiscountValue" class="p-inputtext-sm w-50"
                              class="p-inputtext-sm w-50" />
                  </div>

            </div>
      </p-card>
      <div class="row">
            <div class="flex justify-content-end gap-2 my-3">
                  <p-button label="back" styleClass="p-button-outlined p-button-secondary" (click)="back()"></p-button>
                  <p-button label="Save" (click)="save()"></p-button>
            </div>
      </div>


</div>