import { Injectable } from '@angular/core';
import { UserType } from '../enum/user-type';
import { AdminType } from '../enum/admin-type';
import { WebsiteAdminPageEnum } from '../enum/website-admin-pages-enum';
import { CompanyAdminPageEnum } from '../enum/company-admin-pages-enum';
import { UserModel } from '../Model/UserModel';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class PermissionService {
  user: UserModel;
  constructor(private authService: AuthService) {
    this.user = this.authService.getUserData();
  }
  updateUser(userUpdated) {
    this.user = userUpdated;
  }
  getAccessiblePages(userType: UserType, adminType: AdminType): number[] {
    if (adminType === AdminType.SubAdmin) {
      switch (userType) {
        case UserType.WebsiteAdmin:
          return Object.values(WebsiteAdminPageEnum).filter((value) => typeof value === 'number') as number[];
        case UserType.CompanyAdmin:
          return Object.values(CompanyAdminPageEnum).filter((value) => typeof value === 'number') as number[];
      }
    }
    return [];
  }

  isPageAllowed(page: any): boolean {
    // SuperAdmin can access all pages
    if (this.user.AdminType === AdminType.SuperAdmin) {
      return true;
    }

    // SubAdmin can access only allowedPages
    if (this.user.AdminType === AdminType.SubAdmin) {
      return this.user.AdministeredPages.includes(page);
    }

    return false;
  }
}
