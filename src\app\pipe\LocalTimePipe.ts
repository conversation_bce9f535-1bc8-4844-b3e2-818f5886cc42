import { DatePipe } from '@angular/common';
import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
      name: 'localTime'
})
export class LocalTimePipe implements PipeTransform {
      constructor(private datePipe: DatePipe) {}

      transform(value: Date | string): string {

            let date = new Date(value);
            return this.datePipe.transform(date.toISOString().toString(), 'yyyy-MM-dd HH:mm:ss a');
      }
}