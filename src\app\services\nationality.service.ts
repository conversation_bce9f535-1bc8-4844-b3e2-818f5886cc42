import { Injectable } from '@angular/core';
import { Observable, map } from 'rxjs';
import { NationalityModel } from '../Model/NationalityModel';
import { environment } from 'src/environments/environment';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class NationalityService {

  constructor(private httpClient: HttpClient) { }

  getAllNationalities(): Observable<NationalityModel[]> {
    return this.httpClient.get<NationalityModel[]>(`${environment.apiUrl}Nationality/GetAll`)
      .pipe(
        map((res: any) => {
            return res.ListResultContent;
        })
      );
  }
}
