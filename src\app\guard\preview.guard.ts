// import { Injectable } from '@angular/core';
// import { Router, CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';

import { Location } from "@angular/common";
import { Injectable } from "@angular/core";
import { CanActivate, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from "@angular/router";


@Injectable({
    providedIn: 'root'
})
export class PreviewGuard implements CanActivate {

    constructor(private _router: Router, private location: Location) {
    }

    canActivate(_: ActivatedRouteSnapshot,
        _1: RouterStateSnapshot): boolean {

        //check some conditionroute
        if (this._router.getCurrentNavigation()?.previousNavigation === null || !this._router.getCurrentNavigation()?.extras.state) {
            // this._router.navigate(['/']);
            this.location.back()
            return false;
        }
        return true;
    }
}
