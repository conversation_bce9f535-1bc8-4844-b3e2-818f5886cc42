import { RenewabilityType } from "../enum/renewbility-type";
import { TiersModel } from "./TiersModel";

export class DiscountNormalTier {
    // DiscountId :string;
    // Discount? Disocunt 
    TierId?: string;
    Tier?: TiersModel;
    OffersNum: number = 0;
    UnlimitedOffers?: boolean = false;
    RenewabilityType: RenewabilityType;
    TotalOfferPerYear: number = 0;
    MonthlyRenewablility: boolean = false;
    renewabilityOptions ;
}