<p-toast></p-toast>
<div class="grid">
    <div class="col-12">
        <div>
            <p-confirmDialog header="Confirmation" key="confirm1" icon="pi pi-exclamation-triangle"
                message="Are you sure you want to proceed?" [style]="{ width: '350px' }"
                acceptButtonStyleClass="p-button-text" rejectButtonStyleClass="p-button-text"></p-confirmDialog>


            <p-table #dt [value]="Countries" [paginator]="paginator" [rows]="pageSize" [totalRecords]="totalRecords"
                [lazy]="true" [loading]="loading" (onLazyLoad)="loadData($event)" [showCurrentPageReport]="true"
                [currentPageReportTemplate]="'Showing {first} to {last} of {totalRecords} records'"
                responsiveLayout="scroll" [rowHover]="true" styleClass="p-datatable-gridlines">

                <ng-template pTemplate="caption">
                    <app-grid-headers [myDt]="Countries" (SearchEvent)='ReceivedFilteredData($event)'
                        addNewTxt="Add New Country" goRoute="/country-new" gridTitle="CountriesManagement"
                        [title]="title">
                    </app-grid-headers>
                </ng-template>
                <ng-template pTemplate="header">
                    <tr>
                        <th>
                            <div class="flex justify-content-between align-items-center">
                                {{ "Country Name" | translate }}
                            </div>
                        </th>
                        <th>
                            <div class="flex justify-content-between align-items-center">
                                {{ "total Companies" | translate }}
                            </div>
                        </th>
                        <th>
                            <div class="flex justify-content-between align-items-center">
                                {{ "total Users" | translate }}
                            </div>
                        </th>
                        <th>
                            <div class="flex justify-content-between align-items-center">
                                {{ "Edit" | translate }}
                            </div>
                        </th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-Country>
                    <tr>
                        <td>
                            <div class="flex">
                                <img src="assets/demo/flags/flag_placeholder.png"
                                    [class]="'flag flag-' +  this.allCountries.getCodeFromName( Country.EnName)"
                                    alt="{{  Country.EnName }}" />
                                &nbsp; &nbsp;
                                {{ Country.EnName }}
                            </div>
                        </td>
                        <td>
                            <div class="flex justify-content-between align-items-center">
                                {{ Country.TotalCompanies }}
                            </div>
                        </td>
                        <td>
                            <div class="flex justify-content-between align-items-center">
                                {{ Country.TotalEndUsers }}
                            </div>
                        </td>
                        <td>
                            <div class="flex">
                                <div class="d-flex align-items-center justify-content-between">
                                    <p-button icon="pi pi-pencil" styleClass="p-button-rounded" class="mx-1"
                                        (click)=" edit({ Country: Country, state: 'edit' })"></p-button>
                                    <p-button icon="pi pi-trash" styleClass="p-button-rounded p-button-danger"
                                        class="mx-1" (click)="delete(Country)"></p-button>
                                </div>
                            </div>
                        </td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td class="text-center" colspan="7">
                            {{ "No Countries found." | translate }}
                        </td>
                    </tr>
                </ng-template>
            </p-table>
            <!-- Reusable Confirmation Dialog Component -->
            <app-confirmation-dialog #confirmationDialog
                (confirmResult)="handleConfirmationAction($event)"></app-confirmation-dialog>

        </div>
    </div>
</div>