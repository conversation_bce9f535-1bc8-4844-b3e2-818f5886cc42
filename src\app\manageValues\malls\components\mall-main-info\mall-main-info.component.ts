
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { MallModel } from 'src/app/Model/MallModel';
import { AuthService } from 'src/app/services/auth.service';
import { CountryService } from 'src/app/services/country.service';
import { MallService } from 'src/app/services/mall.service';
import { Countries } from 'src/app/utilities/countries';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'mall-main-info',
  templateUrl: './mall-main-info.component.html',
  styleUrls: ['./mall-main-info.component.scss'],

})
export class MallMainInfoComponent {
  pressedSave: boolean = false;
  cities = [];
  countries = [];

  enableEditFilter: boolean = false;
  routeState: any;

  fileToUpload: any[];
  imageUrl: any;
  allCountries: Countries = new Countries();
  @Input() _MallData: MallModel = new MallModel();
  @Input() isEditing: boolean;
  @Input() activeIndex: number;
  @Output() activeIndexChange: EventEmitter<number> = new EventEmitter();

  mallForm = new FormGroup({
    EnName: new FormControl('', [Validators.required, Validators.minLength(3)]),
    ArName: new FormControl('', [Validators.required, Validators.minLength(3)]),
    City: new FormControl('', [Validators.required]),
    imageUrl: new FormControl('', [Validators.required]),
    PhoneNumbers: this.fb.array([this.initPhoneNumber()])

  });

  constructor(private messageService: MessageService, private MallService: MallService, private fb: FormBuilder, private router: Router, private countryService: CountryService, private sanitizer: DomSanitizer, private authService: AuthService) {

  }

  ngOnInit() {

    this.countryService.Countries.subscribe((data) => {
      this.countries = data;
      this.cities = this.countries.flatMap(country =>
        country.Cities.map(city => ({
          Id: city.Id,
          name: `${country.EnName} - ${city.EnName}`,
          EnName: city.EnName,
          ArName: city.ArName,
          countryId: country.Id
        }))
      );

    });
    if (this.isEditing) {
      this._MallData.City = this.cities.find((city) => city.Id == this._MallData.City.Id) // update city to update name field
    }
    if (this._MallData.PhoneNumbers.length > 0) {
      this.removePhoneNumber(0)
      this._MallData.PhoneNumbers.forEach(phone => {
        this.addPhoneNumber(phone)
      });
    }
    if (this._MallData.LogoUrl) {
      this.imageUrl = this._MallData.LogoUrl ? this.sanitizer.bypassSecurityTrustUrl(environment.imageSrc + this._MallData.LogoUrl) : '';
    }
  }

  CheckValid(input: FormControl) {
    if (input.invalid && (this.pressedSave || input.touched)) {
      return 'red';
    }
    return '#515C66';
  }

  save() {
    this.pressedSave = true;
    if (!this.mallForm.valid) {
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Check your input' });
      return;
    }
    this._MallData.PhoneNumbers = this.PhoneNumbers.value
      .filter(phone => phone.Number && phone.CountryCode)
      .map(phone => ({
        ...phone,
        Number: phone.Number.toString()
      }));
    delete this._MallData.AssociatedCompaniesNum;
    delete this._MallData.AssociatedCompanies;
    delete this._MallData.CityId;
    delete this._MallData.Location;
    delete this._MallData.PhoneNumber;



    let form: FormData = new FormData();
    form.append('request', JSON.stringify(this._MallData));
    if (this._MallData.Logo)
      form.append('logo', this._MallData.Logo);
    delete this._MallData.Logo;
    delete this._MallData.LogoUrl;

    if (this.isEditing) {
      this.MallService.EditMall(form).subscribe(data => {
        if (data.HasError) {
          return;
        }
        else {
          this.router.navigate(['/malls'])
        }
      });
    }
    else {
      this.MallService.AddMall(form).subscribe((data) => {
        if (data.HasError) {
          console.log('post result', data);
        }
        else {
          this.router.navigate(['/malls']);
        }
      })
    }


  }

  uploadFile(files: any) {

    if (files.length === 0) {
      return;
    }
    var file = <File>files[0];
    const reader = new FileReader();
    this._MallData.Logo = file;
    reader.readAsDataURL(file);
    reader.onload = (e: any) => {
      this.imageUrl = e.target.result;
    }
    this.fileToUpload.push(file);
    this.imageUrl = URL.createObjectURL(this.fileToUpload[0]);
  }

  clearFileInput() {
    this.fileToUpload = [];
    this.imageUrl = '';
  }
  get PhoneNumbers(): FormArray {
    return this.mallForm.get('PhoneNumbers') as FormArray;
  }
  initPhoneNumber(): FormGroup {
    return this.fb.group({
      CountryCode: ['+963',], // Default country code
      Number: ['',]
    });
  }
  createPhoneNumberGroup(phone): FormGroup {
    return this.fb.group({
      CountryCode: [phone.CountryCode],
      Number: [phone.Number],
    });
  }
  addPhoneNumber(phone = null): void {
    if (phone != null) {
      this.PhoneNumbers.push(this.createPhoneNumberGroup(phone))
    } else
      this.PhoneNumbers.push(this.initPhoneNumber());
    this._MallData.PhoneNumbers = this.PhoneNumbers.value;
  }
  removePhoneNumber(index: number): void {
    this.PhoneNumbers.removeAt(index);
  }

  setValidatorsPhoneNumber() {

    this.mallForm.get('mainPhoneCode')?.valueChanges.subscribe(value => {
      const mainPhone = this.mallForm.get('mainPhone');
      if (value) {
        mainPhone?.setValidators([Validators.required]);
      } else {
        mainPhone?.clearValidators();
      }

      mainPhone?.updateValueAndValidity({ emitEvent: false }); // Update control validity
    });
    this.mallForm.get('mainPhone')?.valueChanges.subscribe(value => {
      const mainPhoneCode = this.mallForm.get('mainPhoneCode');
      if (value) {
        mainPhoneCode?.setValidators([Validators.required]);
      } else {
        mainPhoneCode?.clearValidators();
      }

      mainPhoneCode?.updateValueAndValidity({ emitEvent: false }); // Update control validity
    });
    return null;
  }

}
