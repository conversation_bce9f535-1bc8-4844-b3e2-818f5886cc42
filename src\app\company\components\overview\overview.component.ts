import { AfterContentInit, AfterV<PERSON>w<PERSON>hecked, AfterViewInit, Component, ElementRef, EventEmitter, Input, OnInit, Output, Renderer2, ViewChild } from '@angular/core';
import { FormControl, FormGroup, NgControl, Validators } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { MessageService } from 'primeng/api';
import { Password } from 'primeng/password';
import { CompanyModel } from 'src/app/Model/CompanyModel';
import { UserModel } from 'src/app/Model/UserModel';
import { Country } from 'src/app/Model/Country';
import { IndustryModel } from 'src/app/Model/IndustryModel';
import { FilterModel } from 'src/app/Model/FiltersModel';
import { ActivityScope } from 'src/app/enum/activity-scope';
import { IndustryService } from 'src/app/services/industry.service';
import { CountryService } from 'src/app/services/country.service';
import { SharedDataComponentService } from 'src/app/services/shared-data-component.service';
import { Countries } from 'src/app/utilities/countries';
import { environment } from 'src/environments/environment';
import { UserService } from 'src/app/services/user-service.service';
import { SubscriptionService } from 'src/app/services/subscription.service';
import { TierService } from 'src/app/services/tier.service';
import { Router } from '@angular/router';
import { UrlValidator } from 'src/app/validators/url.validators';
import { EmailValidator } from 'src/app/validators/email.validators';
import { PhoneNumberModel } from 'src/app/Model/PhoneNumberModel';
import { AuthService } from 'src/app/services/auth.service';

@Component({
    selector: 'app-overview',
    templateUrl: './overview.component.html',
    styleUrls: ['./overview.component.scss'],

})
export class OverviewComponent implements OnInit {
    @ViewChild('passwordInput') passwordInput: Password;
    @ViewChild('emailInput') emailInput: ElementRef;
    @Input() isEditing: boolean;
    @Input() _OverViewData: CompanyModel;
    @Input() fileToUpload: any[];
    imageUrl: any;
    Logo: File;
    @Input() activeIndex: number;
    @Output() activeIndexChange: EventEmitter<number> = new EventEmitter();
    adminEmail: string = '';

    overviewForm = new FormGroup({
        companyArName: new FormControl('', [Validators.required, Validators.minLength(3)]),
        companyEnName: new FormControl('', [Validators.required, Validators.minLength(3)]),
        companyPin: new FormControl('', [Validators.required, Validators.pattern(/^\d{4}$/)]),
        companyWebSite: new FormControl('', [UrlValidator.isValidUrlFormat]),
        imageUrl: new FormControl('', [Validators.required]),
        name: new FormControl('', [Validators.required, Validators.minLength(3)]),
        email: new FormControl('', [Validators.required, EmailValidator.isValidEmailFormat]),
        password: new FormControl('', [Validators.required, Validators.pattern('(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[$@$!%*?&])[A-Za-z\d$@$!%*?&].{8,}')]),
        Industry: new FormControl([], [Validators.required]),
        filters: new FormControl([],),
        mainPhone: new FormControl('', [Validators.required, Validators.min(7)]),
        mainPhoneCode: new FormControl(''),
        whatsappNumber: new FormControl(''),
        whatsappNumberCode: new FormControl(''),
    });

    countries: Country[];
    industries: IndustryModel[] = [];
    filters: FilterModel[];
    activeScope: { id: number, name: string }[];
    selectedActiveScope: { id: number, name: string };
    user: UserModel;
    pressedNext = false;
    allCountries: Countries = new Countries();

    constructor(private countryService: CountryService,
        private messageService: MessageService, private industryService: IndustryService, private sharedDataComponentService: SharedDataComponentService, private userService: UserService, private subscriptionService: SubscriptionService, private tierService: TierService,
        private sanitizer: DomSanitizer, private router: Router, private authService: AuthService
    ) {

        this.user = this.authService.getUserData();
        this.activeScope = [
            { id: 0, name: 'Local' },
            { id: 1, name: 'International' },
        ];

        this.allCountries.AllCOUNTRIES = [
            { name: '', mobileCode: null, code: '', utc: '', timezone: '' }, // Add empty option dynamically
            ... this.allCountries.AllCOUNTRIES,
        ];

        // this.overviewForm.controls.email.setValue('');
    }
    ngAfterViewInit(): void {
        if (!this.isEditing) {
            setTimeout(() => {
                // Reset the entire form -> to empty email & pass
                this.restForm()
            }, 900);
        }


    }

    ngOnInit(): void {
        if (this.isEditing) {
            this.overviewForm.controls.imageUrl.clearValidators();
            this.overviewForm.controls.password.clearValidators();
            this.adminEmail = this._OverViewData.CompanyAdmin.Email;
        }

        if (this._OverViewData.LogoUrl)
            // disable sanitization to display image
            this.imageUrl = this._OverViewData.LogoUrl ? this.sanitizer.bypassSecurityTrustUrl(environment.imageSrc + this._OverViewData.LogoUrl) : '';

        if (this._OverViewData.PhoneNumber == null) {
            this._OverViewData.PhoneNumber = new PhoneNumberModel();
        }
        if (this._OverViewData.WhatsappNumber == null) {
            this._OverViewData.WhatsappNumber = new PhoneNumberModel();
        }
        this.countryService.Countries.subscribe((data) => {
            this.countries = data;
            // if (this._OverViewData.CompanyCountries && this._OverViewData.CompanyCountries.length > 0) {
            if (this.countries && this.countries.length > 0) {
                // edit activeScope & countries 
                if (this._OverViewData.ActivityScope == 1) { // international

                    this.selectedActiveScope = { id: 1, name: 'International' };
                    this._OverViewData.ActivityScope = ActivityScope.International;
                    this._OverViewData.Countries = this.countries.filter(x => this._OverViewData.CompanyCountries.findIndex(y => y.Id == x.Id) != -1)
                }// check if scope is local set cointries & selectedActiveScope
                else if (this._OverViewData.ActivityScope == 0) {

                    this.selectedActiveScope = { id: 0, name: 'Local' };
                    this._OverViewData.Countries = this.countries.filter(x => this._OverViewData.CompanyCountries.findIndex(y => y.Id == x.Id) != -1);
                }
                else { //by default

                    this.selectedActiveScope = { id: 0, name: 'Local' };
                    this._OverViewData.ActivityScope = ActivityScope.Local;
                    this._OverViewData.Countries = [];
                }
            }
        });
        if (this.countries.length == 0) {
            this.router.navigate(['/companies']);
        }
        this.industryService.GetAllIndustry().subscribe((data) => {
            this.industries = data.ListResultContent;
            if (this._OverViewData.CompanyIndustries && this._OverViewData.CompanyIndustries.length > 0) {
                this._OverViewData.Industries = this.industries.filter(x => this._OverViewData.CompanyIndustries.findIndex(y => y.Id == x.Id) != -1)
            }
        });
        this.industryService.GetAllFilters().subscribe((data) => {
            this.filters = data;
            if (this._OverViewData.CompanyFilters && this._OverViewData.CompanyFilters.length > 0) {
                this._OverViewData.Filters = this.filters.filter(x => this._OverViewData.CompanyFilters.findIndex(y => y.Id == x.Id) != -1)
            }
        });
        if (this.user.UserType != 0) {
            // this.overviewForm.get('companyPin')?.disable();
            // this.overviewForm.get('companyWebSite')?.disable();
            this.overviewForm.get('industry')?.disable();
            this.overviewForm.get('password')?.disable();
            this.overviewForm.get('email')?.disable();
            this.overviewForm.get('companyArName')?.disable();
            this.overviewForm.get('companyEnName')?.disable();
            this.overviewForm.get('name')?.disable();
        }

        //by default Tiers  & Subscriptions
        this.tierService.getAllTiers().subscribe((data) => {
            // Using map to Create a New Array
            this._OverViewData.Tiers = data.map(tier => ({ ...tier, Checked: false}));
        });


        this.subscriptionService.getAllSubscriptions().subscribe((data) => {
            this._OverViewData.Subscriptions = data.map(Subscription => ({ ...Subscription, Checked: false }));
        });
        this.setValidatorsPhoneNumber();

    }


    next() {
        //  console.log('form',this.overviewForm)
        // this.onEmailInputFocusOut();
        if (this._OverViewData.Countries.length == 0) {
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please Select Country' });
            return;
        }

        // Mark all controls as touched to trigger validation
        this.overviewForm.markAllAsTouched();
        // this.setValidatorsPhoneNumber();
        if (this._OverViewData.ActivityScope == 0) {
            //In Add Company Sometimes there is a cache of previous values so it is init  Checked: false 
            this._OverViewData.Subscriptions = this._OverViewData.Countries[0].CountrySubscriptions.map(Subscription => ({ ...Subscription, Checked: false }));;
            this._OverViewData.Tiers = this._OverViewData.Countries[0].CountryTiers.map(tier => ({ ...tier, Checked: false }));

            this._OverViewData.Currency = this._OverViewData.Countries[0].Currency;
        }
        else {
            this._OverViewData.PriceOfSubscription = 0;
            this._OverViewData.Currency = 'Dollar';
        }

        this._OverViewData.Tiers.sort((a, b) => a.Rank - b.Rank);

        this._OverViewData.Tiers.forEach(element => {
           
            if (this._OverViewData.CompanyTiers.find(i => i.Id == element.Id)) {
                element.Checked = true;
            }
            // set disabled checkbox to tiers level-up
            if (element.Checked) {
                this._OverViewData.Tiers.forEach((data) => {
                    if (data.Rank > element.Rank) {
                        data.Checked = true;
                        data.disabled = true;
                    }
                })
            }
        });
        this._OverViewData.Tiers = this._OverViewData.Tiers.filter(tier => !tier.IsVip);

        console.log('tiers', this._OverViewData.Tiers)
        if (this._OverViewData.PhoneNumber?.Number)
            this._OverViewData.PhoneNumber.Number = this._OverViewData.PhoneNumber?.Number.toString();
        else {
            this._OverViewData.PhoneNumber = new PhoneNumberModel();
        }

        if (this._OverViewData.WhatsappNumber?.Number)
            this._OverViewData.WhatsappNumber.Number = this._OverViewData.WhatsappNumber?.Number.toString();
        else {
            this._OverViewData.WhatsappNumber = new PhoneNumberModel();
        }
        this._OverViewData.Subscriptions.forEach(element => {
            if (this._OverViewData.CompanySubscriptions.find(i => i.Id == element.Id)) {
                element.Checked = true;
                element.FeePerYear = this._OverViewData.CompanySubscriptions.find(i => i.Id == element.Id).FeePerYear
            }
        });

        if (this.overviewForm.getError('EmailError') || this.overviewForm.getError('ErrorRequired')) {
            this._OverViewData.hasError = true;
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'This email is already used. Please enter another email' });
            return;
        }

        // Set up custom validation to password based on a isEditing form
        if (this.isEditing) {
            //const passwordControl = this.overviewForm.get('password');
            if (this.passwordControl.value.trim() !== '') {
                this.passwordControl.setValidators([Validators.required, Validators.pattern('(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[$@$!%*?&])[A-Za-z\d$@$!%*?&].{8,}')])
                // this.passwordControl.setErrors({ 'ErrorRequired': true });
            }
        }

        if (!this.overviewForm.valid) {
            this._OverViewData.hasError = true;
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Check your input' });
            return;
        }

        this.pressedNext = true;
        this._OverViewData.hasError = false;
        this._OverViewData.AllCities = this.countries.filter(x => x.Id == this._OverViewData.Countries[0].Id)[0].Cities;

        // delete not used fields 
        delete this._OverViewData.CompanyCountries;
        delete this._OverViewData.CompanyIndustries;
        delete this._OverViewData.CompanyFilters;
        this.activeIndex++;
        this.activeIndexChange.emit(this.activeIndex);
    }

    changeActivityScope() {
        this._OverViewData.ActivityScope = this.selectedActiveScope.id == 0 ? ActivityScope.Local : ActivityScope.International;
        this._OverViewData.Countries = this.countries.filter(x => this._OverViewData.Countries.findIndex(y => y.Id == x.Id) != -1);
    }
    uploadFile(files: any) {
        if (files.length === 0) {
            return;
        }
        this._OverViewData.Logo = <File>files[0];

        const reader = new FileReader();

        reader.readAsDataURL(this._OverViewData.Logo);
        reader.onload = (e: any) => {
            this.imageUrl = e.target.result;
        }
        this.fileToUpload.push(this._OverViewData.Logo);
        this._OverViewData.LogoUrl = "";
        // this.imageUrl = URL.createObjectURL(this._OverViewData.Logo);

        // const fileData = new FormData();
        // fileData.append('Logo',this.Logo,this.Logo.name)
    }
    clearFileInput() {
        this.fileToUpload = [];
        this.imageUrl = '';
    }
    CheckValid(input: FormControl) {
        if (input.invalid && (this.pressedNext || input.touched)) {
            return 'red';
        }
        return '#515C66';
    }
    addCountry() {

        if (this._OverViewData.Countries.length == 0) {
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please Select Country' });
            return;
        }
        //  this._OverViewData.Countries = [];
        //this._OverViewData.Countries.push(this._OverViewData.Country);
        // this.sharedDataComponentService.Tieres.subscribe((data) => {
        // });

    }

    onEmailInputFocusOut(): void {
        // Your logic here, triggered when the input field loses focus
        var data = {
            'Email': this._OverViewData.CompanyAdmin.Email,
            'CompanyId': this._OverViewData.Id == "" ? null : this._OverViewData.Id
        };
        //data.email=this._OverViewData.CompanyAdmin.Email ;
        if (!this.isEditing) {
            if (this._OverViewData.CompanyAdmin.Email != null)
                this.userService.CheckCompanyAdminExist(data)
                    .subscribe((data) => {
                        if (data['HasError'] == true) {
                            // this.messageService.add({ severity: 'error', summary: 'Error', detail: data['EnErrorMessage'] });
                            this.overviewForm.get('email').setErrors({ 'EmailError': true });
                            this._OverViewData.hasError = true;
                        }
                    });
        } else {
            if (this.adminEmail != this._OverViewData.CompanyAdmin.Email && this.isEditing)
                this.userService.CheckCompanyAdminExist(data)
                    .subscribe((data) => {
                        if (data['HasError'] == true) {
                            // this.messageService.add({ severity: 'error', summary: 'Error', detail: data['EnErrorMessage'] });
                            this.overviewForm.get('email').setErrors({ 'EmailError': true });
                            this._OverViewData.hasError = true;
                        }
                    });
        }




    }
    // Optional method to check password control errors programmatically
    get passwordControl() {
        return this.overviewForm.get('password');
    }

    restForm() {
        this.overviewForm.reset();
        this.overviewForm.controls.mainPhoneCode.setValue(this.allCountries.AllCOUNTRIES.filter(x => x.mobileCode == '+963')[0].mobileCode);
    }

    setValidatorsPhoneNumber() {
        this.overviewForm.get('whatsappNumberCode')?.valueChanges.subscribe(value => {
            const whatsappNumber = this.overviewForm.get('whatsappNumber');
            if (value) {
                whatsappNumber?.setValidators([Validators.required]);
            } else {
                whatsappNumber?.clearValidators();
            }

            whatsappNumber?.updateValueAndValidity({ emitEvent: false }); // Update control validity
        });

        this.overviewForm.get('whatsappNumber')?.valueChanges.subscribe(value => {
            const whatsappNumberCode = this.overviewForm.get('whatsappNumberCode');
            if (value) {
                whatsappNumberCode?.setValidators([Validators.required]);
            } else {
                whatsappNumberCode?.clearValidators();
            }

            whatsappNumberCode?.updateValueAndValidity({ emitEvent: false }); // Update control validity
        });
        return null;
    }
}
