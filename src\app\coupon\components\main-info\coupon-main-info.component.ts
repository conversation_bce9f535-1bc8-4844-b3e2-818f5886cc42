import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CompanyBranchModel } from 'src/app/Model/CompanyBranchModel';
import { CompanyModel } from 'src/app/Model/CompanyModel';
import { CouponModel } from 'src/app/Model/CouponModel';
import { FilterModel } from 'src/app/Model/FiltersModel';
import { IndustryService } from 'src/app/services/industry.service';
import { ConditionService } from 'src/app/services/condition.service';
import { CouponService } from 'src/app/services/coupon.service';
import { MessageService } from 'primeng/api';
import { environment } from 'src/environments/environment';
import { DomSanitizer } from '@angular/platform-browser';
import { ToastrService } from 'ngx-toastr';
import { TranslateService } from '@ngx-translate/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CompanyService } from 'src/app/services/company.service';
import { DiscountValueEnum } from 'src/app/enum/discount-value-enum';
import { UserModel } from 'src/app/Model/UserModel';
import { startEndDatesValidator } from 'src/app/validators/start-end-dates.directive';
import { ConditionModel } from 'src/app/Model/ConditionModel';
import { EndpointUsedInEnum } from 'src/app/enum/endpoint-used-in-enum';
import { AuthService } from 'src/app/services/auth.service';


@Component({
    selector: 'coupon-main-info',
    templateUrl: './coupon-main-info.component.html',
    styleUrls: ['./coupon-main-info.component.scss'],

})
export class CouponMainInfoComponent implements OnInit {
    currentDate: string;
    @Input() _CouponData: CouponModel;
    CouponValues: { id: string; name: string }[] = [];
    protectionType = [];
    companies: CompanyModel[] = [];
    companyBranchs: CompanyBranchModel[] = [];
    @Input() editing: boolean;
    @Input() activeIndex: number;
    @Output() activeIndexChange: EventEmitter<number> = new EventEmitter();
    pressedNext = false;
    companyProfileimage: any;
    selectedCompany: CompanyModel = new CompanyModel();
    selectedCompanyCurrency: string = '$';
    imageUrl: any = '';
    user: UserModel;
    mainInfoForm = new FormGroup({
        //CouponImage: new FormControl([], [Validators.required]),
        company: new FormControl('', [Validators.required]),
        CouponEnTitle: new FormControl('', [Validators.required, Validators.minLength(3)]),
        CouponArTitle: new FormControl('', [Validators.required, Validators.minLength(3)]),
        CouponValue: new FormControl('', [Validators.required]),
        estimatedSavings: new FormControl('', [Validators.required, Validators.min(1)]),
        originalPrice: new FormControl('', [Validators.required, Validators.min(1)]),
        protectionType: new FormControl('', [Validators.required]),
        filters: new FormControl('', [Validators.required]),
        EnTerms: new FormControl('', [Validators.required, Validators.minLength(3)]),
        ArTerms: new FormControl('', [Validators.required, Validators.minLength(3)]),
        relatedBranches: new FormControl('', [Validators.required]),
        startEndDates: new FormGroup({
            startDate: new FormControl('', [Validators.required]),
            endDate: new FormControl('', [Validators.required]),
        }, { validators: startEndDatesValidator }),
        targetedUsers: new FormControl('', [Validators.required]),
        conditions: new FormControl('', [Validators.required]),

    });

    @Input() fileToUpload: any[];

    conditions: ConditionModel[] = [];
    filters: FilterModel[] = [];

    constructor(private companyService: CompanyService, private conditionService: ConditionService,
        private industryService: IndustryService, private couponService: CouponService,
        private messageService: MessageService, private _changeDetectorRef: ChangeDetectorRef, private sanitizer: DomSanitizer, private toastr: ToastrService, private translate: TranslateService, private route: ActivatedRoute, private router: Router, private authService: AuthService) {
        this.currentDate = new Date().toISOString().slice(0, 10);
    }
    ngOnInit(): void {
        this.user = this.authService.getUserData();
        if (this.user.UserType == 1) {
            this.mainInfoForm.get('company').clearValidators();
        }
        if (this._CouponData.ImageUrl)
            // this.imageUrl = environment.imageSrc + this._CouponData.imageUrl;
            // disable sanitization to display image
            this.imageUrl = this._CouponData.ImageUrl ? this.sanitizer.bypassSecurityTrustUrl(environment.imageSrc + this._CouponData.ImageUrl) : '';
        this._CouponData.CompanyId = null;

        this.companyService.GetAllId(EndpointUsedInEnum.Coupon).subscribe((data) => {
            this.companies = data;
            if (this._CouponData.Company) { // edit Coupon
                this.selectedCompany = this.companies.find(x => x.Id == this._CouponData.Company.Id);
                this._CouponData.CompanyId = this._CouponData.Company.Id;
                this.selectedCompanyCurrency = this.selectedCompany.selectedCompanyCurrency || '$';
                //this.companyBranchs = this.selectedCompany.CompanyBranches

            }
            if (this.user.UserType == 1) {
                this.selectedCompany = this.companies[0];
                this.companyProfileimage = environment.imageSrc + this.selectedCompany.LogoUrl;
                // this.companyBranchs = this.companies[0].CompanyBranches;
                this._CouponData.CompanyId = this.selectedCompany.Id;
                this.selectedCompanyCurrency = this.selectedCompany.selectedCompanyCurrency || '$';
                this._CouponData.Company = this.selectedCompany;
            }

            if (this.route.snapshot.url[this.route.snapshot.url.length - 1].path === 'coupon-edit') {
                if (!this._CouponData.Company) {
                    this.router.navigate(['/coupons-inbox']);
                    return;
                }
            }
        });
        this.companyBranchs = this._CouponData.CouponBranches;
        // if ( this._CouponData.CouponBranches.length > 0) {
        //     this.companyBranchs = this._CouponData.CouponBranches;
        //     this._CouponData.CouponBranches = this.companyBranchs.filter(x => this._CouponData.CouponBranches.findIndex(y => y.Id == x.Id) != -1);
        // }
        if (this._CouponData.Company && this._CouponData.Company.LogoUrl) {
            this.companyProfileimage = environment.imageSrc + this._CouponData.Company.LogoUrl;
        }

        this.CouponValues = this.generateCouponValuesArray();
        this.conditionService.GetAll().subscribe((data) => {
            this.conditions = data;
            // if (this._discountData.Conditions.length > 0) {
            //     // this._discountData.Conditions = this.conditions.filter(x => this._discountData.Conditions.findIndex(y => y.Id == x.Id) != -1)
            // }
        });
        this.industryService.GetAllFilters().subscribe((data) => {
            this.filters = data;
            // if (this._discountData.Filters.length > 0) {
            //     // this._discountData.Filters = this.filters.filter(x => this._discountData.Filters.findIndex(y => y.Id == x.Id) != -1)
            // }
        })

        // this._changeDetectorRef.detectChanges();

        this.protectionType = [
            { id: 0, name: 'PIN Code' },
            { id: 1, name: 'Swipe' },
            // { id: 0, name: 'PIN' },
            // { id: 1, name: 'CODE' },
            // { id: 2, name: 'PASSWORD' },
        ];
    }

    refreshCompanyRelatedDetails() {
        let selectedCompany = this.companies.find(x => x.Id == this._CouponData.CompanyId);
        this._CouponData.CompanyId = selectedCompany.Id;
        this._CouponData.Company = selectedCompany;
        this.companyBranchs = selectedCompany.CompanyBranches;
        // this.companyProfileimage  = environment.imageSrc + selectedCompany.imageUrl.split("wwwroot\\")[1];
        this.companyProfileimage = selectedCompany.LogoUrl ? this.sanitizer.bypassSecurityTrustUrl(environment.imageSrc + selectedCompany.LogoUrl) : '';
        // this.companyProfileimage = selectedCompany.imageUrl;
        this.selectedCompanyCurrency = selectedCompany.selectedCompanyCurrency || '$';
        this.selectedCompany = selectedCompany;
    }
    save() {
        this.pressedNext = true;
        // Mark all controls as touched to trigger validation
        this.mainInfoForm.markAllAsTouched();
        // if (!this.mainInfoForm.valid || this.fileToUpload.length === 0) {
        if (!this.mainInfoForm.valid) {
            // this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Check your inputs' });
            // this.toastr.error('Check your inputs', this.translate.instant('error'));
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Check your input' });
            return;
        }
        if (this.imageUrl == '') {
            // this.toastr.error('Please select image for this Coupon.', this.translate.instant('error'));
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please Upload image for this Coupon.' });
            return;
        }

        delete this._CouponData.Active;
        delete this._CouponData.ImageUrl;
        delete this._CouponData.CompanyId;
        delete this._CouponData.UserFriendly;
        if (!this.editing) delete this._CouponData.Id;

        if (this._CouponData.Company) {
            const attributesToKeepInCompany = ['Id', 'EnName', 'ArName'];
            // Delete attributes not in attributesToKeepInCompany
            Object.keys(this._CouponData.Company).forEach((key) => {
                if (!attributesToKeepInCompany.includes(key)) {
                    delete this._CouponData.Company[key];
                }
            });
        }
        delete this._CouponData.VisibleDialog;

        let form: FormData = new FormData();
        if (this._CouponData.image)
            form.append('image', this._CouponData.image);
        form.append('request', JSON.stringify(this._CouponData));

        if (this.editing) {
            this.couponService.EditCoupon(form).subscribe(data => {
                if (data.HasError) {
                    console.log('post result', data);
                }
                else {
                    this.router.navigate(['coupons-inbox'])
                }
            });
        }
        else {
            this.couponService.AddCoupon(form).subscribe((data) => {
                if (data.HasError) {
                    console.log('post result', data);
                }
                else {
                    this.router.navigate(['coupons-inbox'])
                }
            })
        }

    }
    CheckValid(input: FormControl) {
        if (input.invalid && (this.pressedNext || input.touched)) {
            return 'red';
        }
        return '#515C66';
    }
    uploadFile(files: any) {

        if (files.length === 0) {
            return;
        }
        var file = <File>files[0];
        const reader = new FileReader();
        this._CouponData.image = file;
        reader.readAsDataURL(file);
        reader.onload = (e: any) => {
            this.imageUrl = e.target.result;
        }
        this.fileToUpload.push(file);
        this.imageUrl = URL.createObjectURL(this.fileToUpload[0]);
    }
    clearFileInput() {
        this.fileToUpload = [];
        this.imageUrl = '';
    }


    generateCouponValuesArray() {

        return Object.keys(DiscountValueEnum)
            .filter(key => isNaN(Number(key))) // Filter out the numeric keys added by TypeScript
            .map(key => {
                return {
                    id: DiscountValueEnum[key],
                    name: key
                };
            });

    }

    // This method updates the estimated saving when the original price changes
    updateEstimatedSaving(): void {
        var CouponValue = this._CouponData.CouponValue;
        if (this._CouponData.CouponValue == 0) CouponValue = 100; //B1G1
        if (this._CouponData.OriginalPrice) {
            this._CouponData.EstimatedSaving = Math.round((CouponValue * this._CouponData.OriginalPrice) / 100);
        } else {
            //  this._CouponData.EstimatedSaving = 0;
        }
    }

    // This method updates the original price when the estimated saving changes
    updateOriginalPrice(): void {
        var CouponValue = this._CouponData.CouponValue;
        if (this._CouponData.CouponValue == 0) CouponValue = 100;  //B1G1
        if (this._CouponData.EstimatedSaving) {
            this._CouponData.OriginalPrice = Math.round((this._CouponData.EstimatedSaving * 100) / CouponValue);
        } else {
            //  this._CouponData.OriginalPrice = 0;
        }
    }
    // Update both original price and estimated saving when the Coupon value changes
    updateOnCouponChange(): void {
        if (this._CouponData.OriginalPrice) {
            this.updateEstimatedSaving();
        } else if (this._CouponData.EstimatedSaving) {
            this.updateOriginalPrice();
        }
    }

}
