import { Component, ViewChild } from '@angular/core';
import { Message, MessageService } from 'primeng/api';
import { TiersModel } from 'src/app/Model/TiersModel';
import { TierStatisticsComponent } from '../components/tier-statistics/tier-statistics.component';
import { ActivatedRoute, Router } from '@angular/router';
import { SharedDataComponentService } from 'src/app/services/shared-data-component.service';
import { TierMainInfoComponent } from '../components/tier-main-info/tier-main-info.component';

@Component({
  selector: 'app-tier-new',
  templateUrl: './tier-new.component.html',
  styleUrls: ['./tier-new.component.scss'],

})
export class TierNewComponent {
  newTier: TiersModel = new TiersModel();
  routeState: any;
  activeIndex = 0;
  previousTabIndex: number = 0; // Track the previous tab index
  isEditing: boolean = false;
  title: string = 'Add New Tier';

  tabsDisabled: boolean = true;  // Control whether tabs are disabled or not
  msgs: Message[] = [];

  @ViewChild(TierMainInfoComponent) TierMainInfoComponent!: TierMainInfoComponent;
  @ViewChild(TierStatisticsComponent) TierStatisticsComponent!: TierStatisticsComponent;

  constructor(private router: Router, private sharedDataComponentService: SharedDataComponentService, private messageService: MessageService, private route: ActivatedRoute) {
    if (this.router.getCurrentNavigation()?.extras.state) {

      this.routeState = this.router.getCurrentNavigation()?.extras.state;
      if (this.routeState) {
        this.newTier = this.routeState.data
          ? this.routeState.data
          : new TiersModel();
        if (this.newTier.Id == "") {
          this.isEditing = false;
        }
        else {
          this.isEditing = true;
        }
      }
    }
    if (this.route.snapshot.url[this.route.snapshot.url.length - 1].path === 'tier-edit') {
      this.isEditing = true;
      this.title = 'Edit Tier';
      this.tabsDisabled = false;

    }

  }
  ngOnInit() {
    if (this.isEditing == true && this.newTier.Id == "") {
      this.router.navigate(['/tiers']);
      return;
    }
  }
  onTabChange(event: any) {
  }

  // Method to handle child component event and disable tabs
  handleDisableTabs(disable: boolean) {
    this.tabsDisabled = disable;  // If true, disable all tabs

  }
}
