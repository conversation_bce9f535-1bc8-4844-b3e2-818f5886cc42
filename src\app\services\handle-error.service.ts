import { HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { AlertService } from './alert.service';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class HandleErrorService {

  constructor(private alertService: AlertService, private router: Router) { }

  public handleError(err: HttpErrorResponse) {
    console.log('errorService', err)
    let errorMessage: string;
    if (err.error instanceof ErrorEvent) {
      // A client-side or network error occurred. Handle it accordingly.
      errorMessage = `An error occurred: ${err.error.message}`;
    } else {
      // The backend returned an unsuccessful response code.
      switch (err.status) {
        case 400:
          errorMessage = (err.status) + `: Bad Request.`;
          break;
        case 404:
          errorMessage = (err.status) + `: The requested resource does not exist.`;
          break;
        case 412:
          errorMessage = (err.status) + `: Precondition Failed.`;
          break;
        case 500:
          errorMessage = (err.status) + `: Internal Server Error.`;
          break;
        case 503:
          errorMessage = (err.status) + `: The requested service is not available.`;
          break;
        default:
          errorMessage = 'Something went wrong!';
      }

      this.alertService.showAlert(errorMessage);
    }
  }

}
