import { Gender } from "../enum/gender";
import { NationalityModel } from "./NationalityModel";
import { TiersModel } from "./TiersModel";

export class EndUserModel {
  Id?: string = "";
  Name?: string;
  Gender?: Gender = null;
  ExpireDate: Date = null;
  Nationality: NationalityModel[] = [];
  Age: number = null;
  RedeemedDiscountsNum: number = null;
  Residance?: string = '';
  Tier?: TiersModel = null;
  TotalEstimatedSavings?: number = 0;
  TotalEstimatedSavingsCurrency?: string = '';
}