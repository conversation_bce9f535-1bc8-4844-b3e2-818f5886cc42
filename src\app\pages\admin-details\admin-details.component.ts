import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { UserModel } from 'src/app/Model/UserModel';
import { AuthService } from 'src/app/services/auth.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'admin-details',
  templateUrl: './admin-details.component.html',
  styleUrls: ['./admin-details.component.scss'],
})
export class AdminDetailsComponent implements OnInit {
  currentUser: UserModel;
  user :UserModel;
  displayresetPasswordModel: boolean;
  restPasswordForm = new FormGroup({
    newPassword: new FormControl('', [Validators.required, Validators.pattern('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[$@$!%*?&])[A-Za-z\\d$@$!%*?&]{8,}$')]),
    UserName: new FormControl('', [Validators.required,])
  });
  loading: boolean = true;
  constructor(private authService: AuthService, private router: Router) { }

  ngOnInit(): void {
    this.user = this.authService.getUserData();
    this.authService.getPersonalDetails().subscribe((data) => {
      this.currentUser = data;
      this.loading = false;
      this.restPasswordForm.controls['UserName'].setValue(this.currentUser?.Email);
    });;

  }

  onLogout(): void {
    this.authService.logout();
    this.router.navigate(["/login"],)
  }

  getImageUrl(image) {
    return image ? environment.imageSrc + image : 'assets/pages/icons/profile.png';
  }

  resetPassword() {
    if (this.restPasswordForm.valid) {
      // console.log('Password reset:', this.restPasswordForm.value);
      this.authService.resetPassword(this.restPasswordForm.value).subscribe(res => {
        if (!res.HasError) {
          this.displayresetPasswordModel = false;
          // console.log('Password reset successfully');
        }
      })

    } else {
      console.log('Invalid form');
    }
  }


  CheckValid(input: FormControl) {
    if (input.invalid && (this.displayresetPasswordModel == false || input.touched)) {
      return 'red';
    }
    return '#515C66';
  }
}
