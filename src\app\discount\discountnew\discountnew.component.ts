import { Location } from '@angular/common';
import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { DiscountModel } from 'src/app/Model/DiscountModel';

@Component({
  selector: 'app-discountnew',
  templateUrl: './discountnew.component.html',
  styleUrls: ['./discountnew.component.scss']
})
export class DiscountnewComponent {
  title: string;
  routeState: any;
  _discountData: DiscountModel = new DiscountModel();
  activeIndex = 0;
  editing: boolean = false;
  fileToUpload: any[] = [];

  constructor(private route: ActivatedRoute, private router: Router,private location: Location) {
    if (this.router.getCurrentNavigation()?.extras.state) {
      this.routeState = this.router.getCurrentNavigation()?.extras.state;
      if (this.routeState) {
        this._discountData = this.routeState.data
          ? this.routeState.data
          : new DiscountModel();
        if (this._discountData.Id == "") {
          this.editing = false;
        }
        else {
          this.editing = true;
        }

      }
    }
    this.route.data.subscribe((item) => {
      this.title = item['title'];
    });
  }
  goBack() {
    this.location.back();
  }
}
