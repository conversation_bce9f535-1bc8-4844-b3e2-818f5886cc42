import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class DashboardService {

  constructor(private httpClient: HttpClient) { }

  getGetGeneralDashboard(CountryId: string = null, Year = null) {

    let params = new HttpParams();
    if (CountryId) {
      params = params.set('CountryId', CountryId)
    }
    if (Year) {
      params = params.set('Year', Year);
    }

    return this.httpClient.get(`${environment.apiUrl}Dashboard/GetGeneralDashboard`, { params }).pipe(map((res: any) => {
      return res;
    })
    );
  }
  GetCompanyDetailsDashboard(CompanyId: string = null, Year = null) {

    let params = new HttpParams();
    if (CompanyId) {
      params = params.set('CompanyId', CompanyId)
    }
    if (Year) {
      params = params.set('Year', Year);
    }

    return this.httpClient.get(`${environment.apiUrl}Dashboard/GetCompanyDetailsDashboard`, { params }).pipe(map((res: any) => {
      return res;
    })
    );
  }
  formatBarData(yearlyTotals: any) {
    const colors = ['#1c80cf', '#a0d2fa']; // Predefined colors
    return {
      labels: yearlyTotals.Labels,
      datasets: yearlyTotals.Datasets.map((dataset: any, index: number) => ({
        label: dataset.Label,
        backgroundColor: colors[index % colors.length],
        borderColor: colors[index % colors.length],
        data: dataset.Data
      }))
    };
  }
  formatPieData(statistics: any) {
    const colors = ["#009688", "#3f51b5", "#9c27b0", "#ff5722", "#ffc107"]; // Predefined color palette

    return {
      labels: statistics.Labels,
      datasets: [
        {
          data: statistics.Datasets[0].Data, // Assuming one dataset for the Pie chart
          backgroundColor: colors.slice(0, statistics.Labels.length), // Assign colors based on the number of labels
          hoverBackgroundColor: colors.slice(0, statistics.Labels.length) // Use the same colors for hover
        }
      ]
    };
  }
}
