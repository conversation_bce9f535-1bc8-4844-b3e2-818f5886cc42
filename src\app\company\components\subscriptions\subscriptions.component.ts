import { ChangeDetectorRef, Component, EventEmitter, Input, Output } from "@angular/core";
import { Router } from "@angular/router";
import { MessageService } from "primeng/api";
import { Subject, } from "rxjs";
import { CompanyModel } from "src/app/Model/CompanyModel";
import { UserModel } from "src/app/Model/UserModel";
import { TiersModel } from "src/app/Model/TiersModel";
import { CompanyService } from "src/app/services/company.service";
import { SubscriptionModel } from "src/app/Model/SubscriptionModel";
import { AuthService } from "src/app/services/auth.service";

@Component({
    selector: "app-subscriptions",
    templateUrl: "./subscriptions.component.html",
    styleUrls: ["./subscriptions.component.scss"],
  
})
export class SubscriptionsComponent {
    @Input() _Company: CompanyModel;
    @Input() activeIndex: number;
    @Input() fileToUpload: any[];
    user: UserModel;
    @Output() activeIndexChange: EventEmitter<number> = new EventEmitter();
    routeState: any;
    enableEdit: boolean = false;
    serviceStatus: boolean = true;
    tireStatus: boolean = true;
    //tiers$: TiersModel[];
    adminDiscount$ = new Subject<number>();
    total$: number = 0;
    saveButtonDisable: boolean = false;
    // discountValue2 = '0';
    constructor(private companyService: CompanyService, private router: Router, private messageService: MessageService, private ref: ChangeDetectorRef, private authService: AuthService
    ) {
        this.user = this.authService.getUserData();
        if (this.router.getCurrentNavigation()?.extras.state) {
            this.routeState = this.router.getCurrentNavigation()?.extras.state;
            if (this.routeState) {
                this.enableEdit = this.routeState.command == 'edit'
            }
        }
        // this.sharedDataComponentService.Tieres.subscribe((data) => {
        //     this.tiers$ = data.sort((a, b) => { return a.Rank - b.Rank });
        // });

        /* tiersService.getAllTiers().subscribe(data => {
            this.tiers$ = data
            this.tiers$.forEach(element => {
                if (this._Company.CompanyTiers.find(i => i.Id == element.Id)) {
                    element.Checked = true;
                }
            });
        }); */
        this.adminDiscount$.subscribe((data) => {
            this.total$ = data;
        });
    }

    calcTotal(value) {
        try {
            this._Company.DiscountGranted = value;
            this._Company.PriceOfSubscription = 0

            this._Company.PriceOfSubscription = this._Company.Subscriptions
                .filter(subscription => subscription.Checked && subscription.FeePerYear)
                .reduce((sum, subscription) => sum + subscription.FeePerYear, 0);
            this._Company.Total = this._Company.PriceOfSubscription * (1 - Number(this._Company.DiscountGranted) / 100);
        } catch (error) {
            this._Company.Total = 0;
        }
    }

    checkTier(item: TiersModel) {
        if (item.Checked) {
            this._Company.Tiers.forEach((data) => {
                if (data.Rank > item.Rank) {
                    data.Checked = true;
                    data.disabled = true;
                }
            })
        } else if (item.Checked == false) {
            this._Company.Tiers.forEach((data) => {
                if (data.Rank > item.Rank) {
                    data.Checked = false;
                    data.disabled = false;
                }
            })
        }
    }
    checkSubscription(item: SubscriptionModel) {

        setTimeout(() => {
            this.ref.detectChanges();
            this.calcTotal(this._Company.DiscountGranted);
        });
    }

    back() {
        this.activeIndex--;
        this.activeIndexChange.emit(this.activeIndex);
    }

    addCompany() {
        if (this._Company.hasError == true) {
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please Check the tabs data !' });
            window.scroll({
                top: 0,
                left: 0,
                behavior: 'smooth'
            });
            return;
        }
        this.saveButtonDisable = true;
        // this._Company.Discounts = true;
        if (this._Company.Subscriptions.filter(x => x.Checked).length < 1) {
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'You Must Choose One Of Service At Least' });
            window.scroll({
                top: 0,
                left: 0,
                behavior: 'smooth'
            });
            this.serviceStatus = false;
            return;
        }
        this.serviceStatus = true;
        if (this._Company.Tiers.filter(x => x.Checked).length < 1) {
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'You Must Choose One Of Tier At Least' });
            window.scroll({
                top: 0,
                left: 0,
                behavior: 'smooth'
            });
            this.tireStatus = false;
            return;
        }
        this.tireStatus = true;
        this._Company.Tiers = this._Company.Tiers.filter(x => x.Checked);
        this._Company.CompanyPin = this._Company.CompanyPin.toString();
        this._Company.CompanyTiers = [];
        this._Company.CompanyTiers = this._Company.Tiers.filter(x => x.Checked == true);
        this._Company.CompanySubscriptions = [];
        this._Company.CompanySubscriptions = this._Company.Subscriptions;
        this._Company.CompanyFilters = [];
        this._Company.CompanyFilters = this._Company.Filters;
        this._Company.CompanyIndustries = [];
        this._Company.CompanyIndustries = this._Company.Industries;
        this._Company.CompanyCountries = [];
        if (this._Company.Countries) {
            let CountriesArray = [];
            // Define an array of attributes you want to extract
            let attributes = ['Id', 'EnName', 'ArName', 'FlagUrl'];
            // Use map() to create a new array with objects containing only the specified attributes
            CountriesArray = this._Company.Countries.map(obj => {
                let extractedAttributes = {};
                attributes.forEach(attr => {
                    extractedAttributes[attr] = obj[attr];
                });
                return extractedAttributes;
            });
            this._Company.CompanyCountries = CountriesArray;
        }

        ///
        delete this._Company.selectedCompanyCurrency;
        delete this._Company.LogoUrl;
        delete this._Company.AllCities;
        //delete this._Company.Logo;
        delete this._Company.Subscriptions;
        delete this._Company.Tiers;
        delete this._Company.Industries;
        delete this._Company.Filters;
        delete this._Company.Countries;


        // sending as FormData
        let data: FormData = new FormData();

        if (this._Company.Logo)
            data.append('Logo', this._Company.Logo, this._Company.Logo.name);

        data.append('request', JSON.stringify(this._Company))

        this.companyService.AddCompany(data).subscribe((data) => {
            this.saveButtonDisable = false;
            this._Company = new CompanyModel();
            this.ref.detectChanges();
            this.router.navigate(["companies"]);
        });
    }

    editCompany() {
        this.saveButtonDisable = true;
        if (this._Company.hasError == true) {
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please Check the tabs data !' });
            window.scroll({
                top: 0,
                left: 0,
                behavior: 'smooth'
            });
            return;
        }
        if (this.user.UserType == 0) {
            if (this._Company.Subscriptions.filter(x => x.Checked).length < 1) {
                this.messageService.add({ severity: 'error', summary: 'Error', detail: 'You Must Choose One Of Service At Least' });
                window.scroll({
                    top: 0,
                    left: 0,
                    behavior: 'smooth'
                });
                this.serviceStatus = false;
                return;
            }
            this.serviceStatus = true;
            if (this._Company.Tiers.filter(x => x.Checked).length < 1) {
                this.messageService.add({ severity: 'error', summary: 'Error', detail: 'You Must Choose One Of Tier At Least' });
                window.scroll({
                    top: 0,
                    left: 0,
                    behavior: 'smooth'
                });
                this.tireStatus = false;
                return;
            }
        }
        //this._Company.Discounts = true;
        // const options = { headers: headers };
        let data: FormData = new FormData();
        if (this._Company.Logo)
            data.append('Logo', this._Company.Logo, this._Company.Logo.name);
        this._Company.CompanyTiers = [];
        this._Company.CompanyTiers = this._Company.Tiers.filter(x => x.Checked == true);
        this._Company.CompanySubscriptions = [];
        this._Company.CompanySubscriptions = this._Company.Subscriptions.filter(x => x.Checked == true);
        this._Company.CompanyFilters = [];
        this._Company.CompanyFilters = this._Company.Filters;
        this._Company.CompanyIndustries = [];
        this._Company.CompanyIndustries = this._Company.Industries;
        this._Company.CompanyCountries = [];
        if (this._Company.Countries) {
            let CountriesArray = [];
            // Define an array of attributes you want to extract
            let attributes = ['Id', 'EnName', 'ArName', 'FlagUrl'];
            // Use map() to create a new array with objects containing only the specified attributes
            CountriesArray = this._Company.Countries.map(obj => {
                let extractedAttributes = {};
                attributes.forEach(attr => {
                    extractedAttributes[attr] = obj[attr];
                });
                return extractedAttributes;
            });
            this._Company.CompanyCountries = CountriesArray;
        }
        // delete this._Company.LogoUrl;
        delete this._Company.AllCities;
        //delete this._Company.Logo;
        delete this._Company.Subscriptions;
        delete this._Company.Tiers;
        delete this._Company.Industries;
        delete this._Company.Filters;
        delete this._Company.Countries;
        data.append('request', JSON.stringify(this._Company));
        // for (const key in this._Company) {
        //     // if (key !== 'Logo') {
        //       data.append(key, JSON.stringify(this._Company[key]));
        //     // }
        // }

        this.companyService.EditCompany(data).subscribe((data) => {
            if (data.HasError) {
                console.log("post result", data);
            } else {
                this.saveButtonDisable = false;
                this.router.navigate(["companies"]);
            }
        });
    }

    CheckValid(state: boolean) {
        if (!state) {
            return 'red';
        }
        return '#515C66';
    }

}
