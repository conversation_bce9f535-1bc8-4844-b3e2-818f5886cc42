<ul class="layout-menu">
    <li
        app-menuitem
        *ngFor="let item of model; let i = index"
        [item]="item"
        label="{{ item.label }}"
        [index]="i"
        [root]="true"
    ></li>
</ul>
<!-- <span class="form-inline">
    <select
        class="form-control"
        #selectedLang
        (change)="switchLang(selectedLang.value)"
    >
        <option
            *ngFor="let language of translate.getLangs()"
            [value]="language"
            [selected]="language === translate.currentLang"
        >
            {{ language }}
        </option>
    </select>
</span> -->
<!-- <label>test: {{ 'label' | translate }}</label>
<label>test: <span translate>label</span></label> -->
