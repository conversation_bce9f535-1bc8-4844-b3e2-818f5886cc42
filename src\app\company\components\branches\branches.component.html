<p-toast></p-toast>
<div class="container" [formGroup]="branchesForm">
    <div class="row" *ngIf="user.UserType == 0 || enableEdit">
        <h4 *ngIf="!enableEdit">Add new location</h4>
        <h4 *ngIf="enableEdit">Edit location</h4>

        <div class="row">
            <div class="col-4">
                <div class="flex flex-column gap-2 mb-3">
                    <label for="branchEnName" [style.color]="CheckValid(branchesForm.controls.branchEnName)">Branch
                        English Name</label>

                    <input id="branchEnName" type="text" pInputText placeholder="Branch English Name"
                        [(ngModel)]="_CompanyBranch.EnName" class="p-inputtext-sm w-50"
                        formControlName="branchEnName" />

                </div>
            </div>
            <div class="col-4">
                <div class="flex flex-column gap-2 mb-3">
                    <label for="branchArName" [style.color]="CheckValid(branchesForm.controls.branchArName)">Branch
                        Arabic
                        name</label>

                    <input id="branchArName" type="text" pInputText placeholder="Branch Arabic Name"
                        [(ngModel)]="_CompanyBranch.ArName" class="p-inputtext-sm w-50"
                        formControlName="branchArName" />

                </div>
            </div>

            <div class="col-4">
                <div class="flex flex-column gap-2 mb-3">
                    <label for="branchArName"> Is Branch Online? </label>
                    <p-inputSwitch [(ngModel)]="_CompanyBranch.IsOnline" [ngModelOptions]="{ standalone: true }"
                        class="me-2" (onChange)="toggleIsOnline()"></p-inputSwitch>
                </div>
            </div>

        </div>



        <div class="row" *ngIf="!_CompanyBranch.IsOnline">
            <div class="col-4">
                <div class="flex flex-column gap-2 mb-3">
                    <label for="branchCountry" [style.color]="CheckValid(branchesForm.controls.branchCountry)">Branch
                        Country </label>
                    <p-dropdown [style.color]="CheckValid(branchesForm.controls.branchCountry)"
                        [options]="_OverViewData.Countries!" [(ngModel)]="_CompanyBranch.Country"
                        placeholder="Select countries" optionLabel="EnName" display="chip" id="dd_country"
                        [disabled]="user.UserType != 0" [ngModelOptions]="{ standalone: true }"
                        (onChange)="changeCountry()" formControlName="branchCountry"></p-dropdown>
                </div>
            </div>
            <div class="col-4">
                <div class="flex flex-column gap-2 mb-3">
                    <label for="branchCity" [style.color]="CheckValid(branchesForm.controls.branchCity)">Branch
                        City </label>
                    <p-dropdown [style.color]="CheckValid(branchesForm.controls.branchCity)"
                        [options]="this._OverViewData.AllCities!" placeholder="Select The City" optionLabel="EnName"
                        display="chip" id="dd_city" [disabled]="user.UserType != 0"
                        [ngModelOptions]="{ standalone: true }" [(ngModel)]="_CompanyBranch.City"
                        formControlName="branchCity"></p-dropdown>
                </div>
            </div>
            <div class="col-4">
                <div class="flex flex-column gap-2 mb-3">
                    <label for="Region">Branch
                        Region </label>
                    <input type="text" pInputText placeholder="Branch Region" [(ngModel)]="_CompanyBranch.Region"
                        formControlName="branchRegion" class="p-inputtext-sm w50" [disabled]="user.UserType != 0"
                        [ngModelOptions]="{ standalone: true }" />
                </div>
            </div>
        </div>




        <div class="row" *ngIf="!_CompanyBranch.IsOnline">
            <div class="col-4">
                <div class="flex flex-column gap-2 mb-3">
                    <label for="mailName">Mall name</label>
                    <div class="row">
                        <p-dropdown [options]="malls" placeholder="Select The Mall" optionLabel="EnName" display="chip"
                            id="dd_malls" (click)="getMalls()" [(ngModel)]="_CompanyBranch.Mall"
                            [ngModelOptions]="{ standalone: true }"></p-dropdown>
                    </div>
                </div>
            </div>
            <div class="col-8">
                <div class="flex flex-column gap-2 mb-3">
                    <label for="googleMapLink">Google map link</label>
                    <input id="googleMapLink" type="text" pInputText placeholder="Google Map Link"
                        [(ngModel)]="_CompanyBranch.GoogleMapLink" formControlName="googleMapLink"
                        [value]="_CompanyBranch.GoogleMapLink?_CompanyBranch.GoogleMapLink:null"
                        (input)="onGoogleMapLinkChange($event.target.value)" class="p-inputtext-sm w-50" />
                    <span *ngIf="branchesForm.get('googleMapLink').hasError('pattern')"
                        [style.color]="CheckValid(branchesForm.controls.googleMapLink)">
                        Please enter a valid Google Maps link.
                    </span>
                </div>
            </div>

        </div>

        <div class="row">
            <div class="col-4">
                <div class="flex flex-column gap-2 mb-3">
                    <label for="PhoneNumber">Phone Number</label>
                    <div class="p-inputgroup w-10">
                        <span class="p-inputgroup-addon py-0 pe-0">
                            <i class="pi pi-phone" [ngStyle]="{ color: 'var(--green-500)' }"></i>
                            <p-dropdown [options]="allCountries.AllCOUNTRIES" id="allCOUNTRIES" optionLabel="mobileCode"
                                optionValue="mobileCode" class="p-inputtext-sm w-50 d-flex" [filter]="true"
                                filterBy="mobileCode" [(ngModel)]="_CompanyBranch.PhoneNumber.CountryCode" [ngModelOptions]="{ standalone: true }">

                                <ng-template let-option pTemplate="item">
                                    <img src="assets/demo/flags/flag_placeholder.png"
                                        [class]="'flag flag-' + option.code.toLowerCase()" alt="{{ option.name }}" />
                                    <span>{{ option.mobileCode }}</span>
                                </ng-template>
                            </p-dropdown>
                        </span>
                        <p-inputNumber [useGrouping]="false" type="tel" placeholder="Enter Phone Number"
                            [(ngModel)]="_CompanyBranch.PhoneNumber.Number" class="p-inputtext-sm w50"
                            [ngModelOptions]="{ standalone: true }" />
                    </div>
                </div>
            </div>
            <div class="col-4">
                <div class="flex flex-column gap-2 mb-3">
                    <label for="filters">Filters</label>
                    <div class="row">
                        <p-multiSelect selectedItemsLabel="No. of Selected filters: {0}" [maxSelectedLabels]="3"
                            [options]="filters" [(ngModel)]="_CompanyBranch.CompanyBranchFilters"
                            defaultLabel="Select filters" optionLabel="EnName" id="filters"
                            [ngModelOptions]="{ standalone: true }"></p-multiSelect>
                    </div>
                </div>
            </div>
            <div class="col-4">
                <div class="flex flex-column gap-2 mb-3">
                    <label for="ServiceListUrl">Service List Url</label>

                    <input type="text" pInputText placeholder="Service List Url"
                        [(ngModel)]="_CompanyBranch.ServiceListUrl" class="p-inputtext-sm w50"
                        [disabled]="user.UserType != 0" [ngModelOptions]="{ standalone: true }"
                        formControlName="serviceListUrl" />
                    <span *ngIf="branchesForm.get('serviceListUrl').hasError('pattern')"
                        [style.color]="CheckValid(branchesForm.controls.serviceListUrl)">
                        Please enter a valid Service List Url .
                    </span>
                </div>
            </div>

        </div>


        <p-button label="specify open hours" styleClass="p-button-link" (click)="showDialog()"></p-button>

        <p-dialog header="Specify open hours" [(visible)]="openHoursDialog" [modal]="true" [style]="{ width: '50vw' }"
            [draggable]="true" [resizable]="false" formGroupName="openHours">
            <div class="mb-3 d-flex align-items-center">
                <p-inputSwitch [(ngModel)]="_CompanyBranch.SameDay" [ngModelOptions]="{ standalone: true }"
                    class="me-2"></p-inputSwitch>
                same every day
            </div>

            <div class="mb-3" *ngIf="_CompanyBranch.SameDay; else defineEachDay">
                <div class="mb-3">
                    <p-selectButton [options]="openHours" [(ngModel)]="selectedOpenHours"
                        [ngModelOptions]="{ standalone: true }" [multiple]="true" optionLabel="dayChar"
                        optionValue="value"></p-selectButton>
                </div>

                <div class="d-flex align-items-center">
                    <div clprotonass="d-flex align-items-center">
                        <input type="time" pInputText [ngModelOptions]="{ standalone: true }" [(ngModel)]="
                                this._CompanyBranch.CompanyBranchWorkHours[0]!
                                    .OpenTime
                            " style="width: 110px" class="mx-2" formControlName="openTime" />
                        -
                        <input type="time" pInputText id="offerTime" [ngModelOptions]="{ standalone: true }"
                            [(ngModel)]="
                                this._CompanyBranch.CompanyBranchWorkHours[0]!
                                    .CloseTime
                            " style="width: 110px" class="mx-2" formControlName="closeTime" />
                    </div>

                    <!-- *ngIf=" this._CompanyBranch.CompanyBranchWorkHours.length == 1"
                        (click)="
                        this._CompanyBranch.CompanyBranchWorkHours.push({ openTime: '', closeTime: '' })" -->
                    <p-button label="splite service" styleClass="p-button-link" icon="pi pi-angle-right" iconPos="right"
                        (click)="
                         this._CompanyBranch.CompanyBranchWorkHours[0]!.IsSplite = true
                        "></p-button>

                    <!-- this._CompanyBranch.CompanyBranchWorkHours[0]!.IsSplite = true -->
                    <span *ngIf="
                            this._CompanyBranch.CompanyBranchWorkHours[0]!
                                .IsSplite
                        " class="mx-2">and</span>

                    <div class="d-flex align-items-center" *ngIf="
                            this._CompanyBranch.CompanyBranchWorkHours[0]!
                                .IsSplite
                        ">
                        <input type="time" pInputText [ngModelOptions]="{ standalone: true }" [(ngModel)]="
                                this._CompanyBranch.CompanyBranchWorkHours[0]!
                                    .OpenTime2
                            " style="width: 110px" class="mx-2" formControlName="openTime2" />
                        -
                        <input type="time" pInputText id="offerTime" [ngModelOptions]="{ standalone: true }"
                            [(ngModel)]="
                                this._CompanyBranch.CompanyBranchWorkHours[0]!
                                    .CloseTime2
                            " style="width: 110px" class="mx-2" formControlName="closeTime2" />
                    </div>
                    <p-button label="close splite" styleClass="p-button-link" icon="pi pi-angle-left" iconPos="right"
                        *ngIf="
                            this._CompanyBranch.CompanyBranchWorkHours[0]!
                                .IsSplite
                        " (click)="
                            this._CompanyBranch.CompanyBranchWorkHours[0]!.IsSplite = false
                        "></p-button>
                </div>
            </div>

            <ng-template #defineEachDay>
                <div class="row mb-3">
                    <app-open-hours #OpenHoursComponent
                        [workHours]="this._CompanyBranch.CompanyBranchWorkHours"></app-open-hours>
                </div>
            </ng-template>

            <span class="text-danger" *ngIf="branchesForm.controls.openHours.invalid">Please check that your times are
                valid (Open time must be less
                than close time).</span>

            <div class="d-flex justify-content-center">
                <p-button label="save" (click)="SaveButton(ErrorText)"></p-button>
            </div>
            <!-- [disabled]="isError" -->
        </p-dialog>

        <div *ngIf="user.UserType == 0 && !enableEdit" class="col-8">
            <div class="flex justify-content-end gap-2 mb-3">
                <p-button label="add" (onClick)="addToCompanyBranches()"></p-button>
            </div>
        </div>
        <div *ngIf="enableEdit" class="col-8">
            <div class="flex justify-content-end gap-2 mb-3">
                <p-button label="save" (onClick)="editBranch()"></p-button>
            </div>
        </div>
    </div>

    <div class="row">
        <p-table [value]="_OverViewData.CompanyBranches" [tableStyle]="{ 'min-width': '50rem' }"
            styleClass="p-datatable-gridlines p-datatable-sm">
            <ng-template pTemplate="caption"> Branches </ng-template>
            <ng-template pTemplate="header">
                <tr>
                    <th>Active</th>
                    <th>Main Branch</th>
                    <th>Branch Name</th>
                    <th>Branch Location</th>
                    <th>Mall</th>
                    <th>Phone Number</th>
                    <th></th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-branch>
                <tr [ngClass]="{'inactive-table-row': branch.Active === false}">
                    <td>
                        <p-inputSwitch *ngIf="canActiveBranch(branch)" [ngModelOptions]="{ standalone: true }"
                            [(ngModel)]="branch.Active" (onChange)="onChangeActiveBranch(branch)"></p-inputSwitch>
                    </td>
                    <td>
                        <p-inputSwitch [ngModelOptions]="{ standalone: true }" [(ngModel)]="branch.IsMainBranch"
                            (onChange)="onChangeMainBranch(branch, $event.checked)"></p-inputSwitch>
                    </td>
                    <td>{{ branch.EnName }}</td>
                    <td>
                        <ng-template *ngIf="!branch.IsOnline ; else online">
                            {{ branch.CountryName }} -
                            {{ branch.CityName }}
                        </ng-template>
                        <ng-template #online>
                            <img src="assets/pages/icons/online2.png" class="small-icon" alt="Online Branch"
                                title="Onlime Branch">
                            Online
                        </ng-template>

                    </td>
                    <td>{{ branch.Mall?.EnName }}</td>
                    <!-- <td class="d-flex align-items-center">
                        <p-avatar *ngIf="branch.Mall.LogoUrl" image="{{imageSrc + branch.Mall.LogoUrl}}"
                            styleClass="mr-2" size="xlarge" shape="circle"></p-avatar>
                        <p-avatar *ngIf="!branch.Mall.LogoUrl" image="https://primefaces.org/cdn/primeng/images/demo/avatar/amyelsner.png"
                            styleClass="mr-2" size="large" shape="circle"></p-avatar>
                        {{ branch.MallName }}
                    </td> -->
                    <td>
                        {{ branch.PhoneNumber?.CountryCode }}-{{branch.PhoneNumber?.Number}}
                    </td>
                    <td>
                        <p-button [disabled]="!branch.Active" icon="pi pi-pencil" styleClass="p-button-rounded"
                            class="mx-1" (click)="showSelectedBranch(branch)"></p-button>
                        <!-- <p-button icon="pi pi-trash" styleClass="p-button-rounded p-button-danger p-button-text"></p-button> -->
                        <p-button *ngIf="user.UserType == 0" icon="pi pi-trash"
                            styleClass="p-button-rounded p-button-danger" (click)="deleteSelectedBranch(branch)"
                            class="mx-1"></p-button>

                    </td>

                </tr>
            </ng-template>

        </p-table>
        <!-- Reusable Confirmation Dialog Component -->
        <app-confirmation-dialog #confirmationDialog
            (confirmResult)="handleConfirmationAction($event)"></app-confirmation-dialog>


        <div class="flex justify-content-end gap-2 my-3">
            <p-button label="back" styleClass="p-button-outlined p-button-secondary" (click)="back()"></p-button>
            <p-button label="Next" (click)="next()"></p-button>
        </div>
    </div>
</div>