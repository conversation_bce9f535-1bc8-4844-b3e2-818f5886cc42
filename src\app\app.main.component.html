<div
    class="layout-wrapper"
    [ngClass]="{
        'p-input-filled': app.inputStyle === 'filled',
        'p-ripple-disabled': !app.ripple,
        'layout-static': isStatic(),
        'layout-overlay': isOverlay(),
        'layout-overlay-active': overlayMenuActive && isOverlay(),
        'layout-horizontal': isHorizontal(),
        'layout-static-active': !staticMenuDesktopInactive && isStatic(),
        'layout-mobile-active': staticMenuMobileActive,
        'layout-rtl': app.isRTL,
        'layout-rightpanel-active': rightPanelActive
    }"
    [class]="
        'layout-menu-' + app.menuTheme + ' layout-topbar-' + app.topbarTheme
    "
>
    <app-topbar></app-topbar>

    <app-rightpanel></app-rightpanel>

    <div class="menu-wrapper" (click)="onMenuClick($event)">
        <div class="layout-menu-container">
            <!-- <app-menu></app-menu> -->
            <app-sidemenu></app-sidemenu>
        </div>
    </div>

    <div class="layout-main">
        <!-- <app-breadcrumb></app-breadcrumb> -->

        <main
            [@routing]="mainOutletRef?.isActivated ? mainOutletRef?.activatedRoute : ''"
            class="pb-5 px-5 layout-content"
        >
            <router-outlet #mainOutletRef="outlet"></router-outlet>
        </main>

        <app-footer></app-footer>
    </div>

    <!-- <app-config></app-config> -->

    <div *ngIf="staticMenuMobileActive" class="layout-mask modal-in"></div>
</div>
