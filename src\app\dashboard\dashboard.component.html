<div class=" grid p-fluid col-12 md:col-6">
      <div class="col-3 flex flex-column gap-2 mb-3">
            <label for="countries"> Viewed by Country</label>
            <p-dropdown [options]="countries" [showClear]="true" [(ngModel)]="Country" defaultLabel="Select country"
                  placeholder="Select country" optionLabel="EnName" (onChange)="changeCountry()"
                  display="chip"></p-dropdown>
      </div>
      <div class="col-3 flex flex-column gap-2 mb-3">
            <label for="countries"> Viewed by Year</label>
            <p-dropdown [options]="years" [showClear]="true" [(ngModel)]="Year" defaultLabel="Select year"
                  placeholder="Select Year" (onChange)="changeYear()"
                  display="chip"></p-dropdown>
      </div>
</div>

<loader [isVisible]="loading"></loader>

<div class="layout-dashboard" *ngIf="!loading">

      <div class="row">
            <div class="col-md-3">
                  <div class="card overview-box blue" [class.selected]="selectedCard === 'endUsers'"
                        (click)="selectCard('endUsers')">
                        <div class="card-header">
                              <h6 [class.selected]="selectedCard === 'endUsers'">End Users</h6>

                        </div>
                        <div class="overview-content">
                              <h3>{{EndUserWeeklyStatistics?.Total}}</h3>
                              <div class="overview-graph">
                                    <span *ngFor="let value of EndUserWeeklyStatistics?.WeekData"
                                          [style.height]="value + 'px'">
                                          <span class="graph-tooltip">{{ value }}</span>
                                    </span>
                              </div>
                        </div>
                        <div class="overview-footer">
                              <span><i class="pi pi-arrow-up"></i>
                                    <span>{{EndUserWeeklyStatistics?.ChangePercentage}}</span> increase in total
                                    users</span>
                        </div>
                  </div>
            </div>
            <div class="col-md-3">
                  <div class="card overview-box blue" [class.selected]="selectedCard === 'companies'"
                        (click)="selectCard('companies')">
                        <div class="card-header">
                              <h6 [class.selected]="selectedCard === 'companies'">Companies</h6>

                        </div>
                        <div class="overview-content">
                              <h3>{{CompanyWeeklyStatistics.Total}}</h3>
                              <div class="overview-graph">
                                    <span *ngFor="let value of CompanyWeeklyStatistics.WeekData"
                                          [style.height]="value + 'px'">
                                          <span class="graph-tooltip">{{ value }}</span>
                                    </span>
                              </div>
                        </div>
                        <div class="overview-footer">
                              <span><i class="pi pi-arrow-up"></i>
                                    <span>{{CompanyWeeklyStatistics.ChangePercentage}}</span> increase in total
                                    companies</span>
                        </div>
                  </div>
            </div>
            <div class="col-md-3">
                  <div class="card overview-box blue" [class.selected]="selectedCard === 'discounts'"
                        (click)="selectCard('discounts')">
                        <div class="card-header">
                              <h6 [class.selected]="selectedCard === 'discounts'">Discounts</h6>

                        </div>
                        <div class="overview-content">
                              <h3>{{DiscountWeeklyStatistics.Total}}</h3>
                              <div class="overview-graph">
                                    <span *ngFor="let value of DiscountWeeklyStatistics.WeekData"
                                          [style.height]="value + 'px'">
                                          <span class="graph-tooltip">{{ value }}</span>
                                    </span>
                              </div>
                        </div>
                        <div class="overview-footer">
                              <span><i class="pi pi-arrow-up"></i>
                                    <span>{{DiscountWeeklyStatistics.ChangePercentage}}</span> increase in total
                                    discounts</span>
                        </div>
                  </div>
            </div>
            <div class="col-md-3">
                  <div class="card overview-box blue" [class.selected]="selectedCard === 'discountsRedemption'"
                        (click)="selectCard('discountsRedemption')">
                        <div class="card-header">
                              <h6 [class.selected]="selectedCard === 'discountsRedemption'">Discounts Redemption Count
                              </h6>

                        </div>
                        <div class="overview-content">
                              <h3>{{DiscountRedemptionWeeklyStatistics.Total}}</h3>
                              <div class="overview-graph">
                                    <span *ngFor="let value of DiscountRedemptionWeeklyStatistics.WeekData"
                                          [style.height]="value + 'px'">
                                          <span class="graph-tooltip">{{ value }}</span>
                                    </span>
                              </div>
                        </div>
                        <div class="overview-footer">
                              <span><i class="pi pi-arrow-up"></i>
                                    <span>{{DiscountRedemptionWeeklyStatistics.ChangePercentage}}</span>
                                    increase in total count</span>
                        </div>
                  </div>
            </div>
      </div>
      &nbsp;
      <div class="row">
            <div class="col-md-3">
                  <div class="card overview-box blue" [class.selected]="selectedCard === 'coupons'"
                        (click)="selectCard('coupons')">
                        <div class="card-header">
                              <h6 [class.selected]="selectedCard === 'coupons'">Coupons</h6>

                        </div>
                        <div class="overview-content">
                              <h3>{{CouponWeeklyStatistics.Total}}</h3>
                              <div class="overview-graph">
                                    <span *ngFor="let value of CouponWeeklyStatistics.WeekData"
                                          [style.height]="value + 'px'">
                                          <span class="graph-tooltip">{{ value }}</span>
                                    </span>
                              </div>
                        </div>
                        <div class="overview-footer">
                              <span><i class="pi pi-arrow-up"></i>
                                    <span>{{CouponWeeklyStatistics.ChangePercentage}}</span> increase in total
                                    coupons</span>
                        </div>
                  </div>
            </div>
            <div class="col-md-3">
                  <div class="card overview-box blue" [class.selected]="selectedCard === 'couponsRedemption'"
                        (click)="selectCard('couponsRedemption')">
                        <div class="card-header">
                              <h6 [class.selected]="selectedCard === 'couponsRedemption'">Coupon Redemption Count</h6>

                        </div>
                        <div class="overview-content">
                              <h3>{{CouponRedemptionWeeklyStatistics?.Total}}</h3>
                              <div class="overview-graph">
                                    <span *ngFor="let value of CouponRedemptionWeeklyStatistics?.WeekData"
                                          [style.height]="value + 'px'">
                                          <span class="graph-tooltip">{{ value }}</span>
                                    </span>
                              </div>
                        </div>
                        <div class="overview-footer">
                              <span><i class="pi pi-arrow-up"></i>
                                    <span>{{CouponRedemptionWeeklyStatistics?.ChangePercentage}}</span> increase in
                                    total count
                              </span>
                        </div>
                  </div>
            </div>
            <!-- <div class="col-md-3">
                  <div class="card overview-box blue" [class.selected]="selectedCard === 'broadcastMessages'"
                        (click)="selectCard('broadcastMessages')">
                        <div class="card-header">
                              <h6 [class.selected]="selectedCard === 'broadcastMessages'">Broadcast Messages Launches
                              </h6>

                        </div>
                        <div class="overview-content">
                              <h3>{{BroadcastMessagesWeeklyStatistics.Total}}</h3>
                              <div class="overview-graph">
                                    <span *ngFor="let value of BroadcastMessagesWeeklyStatistics.WeekData"
                                          [style.height]="value + 'px'">
                                          <span class="graph-tooltip">{{ value }}</span>
                                    </span>
                              </div>
                        </div>
                        <div class="overview-footer">
                              <span><i class="pi pi-arrow-up"></i>
                                    <span>{{BroadcastMessagesWeeklyStatistics.ChangePercentage}}</span> increase in
                                    total launches</span>
                        </div>
                  </div>
            </div>
            <div class="col-md-3">
                  <div class="card overview-box blue" [class.selected]="selectedCard === 'broadcastMessagesSent'"
                        (click)="selectCard('broadcastMessagesSent')">
                        <div class="card-header">
                              <h6 [class.selected]="selectedCard === 'broadcastMessagesSent'">Broadcast Messages Sent
                              </h6>

                        </div>
                        <div class="overview-content">
                              <h3>{{BroadcastMessagesSentWeeklyStatistics.Total}}</h3>
                              <div class="overview-graph">
                                    <span *ngFor="let value of BroadcastMessagesSentWeeklyStatistics.WeekData"
                                          [style.height]="value + 'px'">
                                          <span class="graph-tooltip">{{ value }}</span>
                                    </span>
                              </div>
                        </div>
                        <div class="overview-footer">
                              <span><i class="pi pi-arrow-up"></i>
                                    <span>{{BroadcastMessagesSentWeeklyStatistics.ChangePercentage}}</span> increase in
                                    total messages</span>
                        </div>
                  </div>
            </div> -->
      </div>
      &nbsp;
      <!-- End Users Charts -->
      <div class="row grid p-fluid" *ngIf="selectedCard === 'endUsers'">
            <div class="col-6">
                  <div class="card">
                        <h5>End Users Yearly Chart</h5>
                        <p-chart type="line" [data]="EndUsersYearly" [options]="lineOptions"></p-chart>
                  </div>
                  <div class="card">
                        <h5>End Users Age Group Bar Chart</h5>
                        <p-chart type="bar" [data]="EndUserAgeStatistics" [options]="barOptions"></p-chart>
                  </div>
            </div>

            <div class="col-6">
                  <div class="card">
                        <h5>End Users Joining Method Chart</h5>
                        <p-chart type="bar" [data]="EndUserJoiningMethodStatistics" [options]="ColumnOptions"></p-chart>
                  </div>
                  <div class="card">
                        <h5>End Users Nationality Distribution Chart</h5>
                        <p-chart type="bar" [data]="EndUserNationalityStatistics" [options]="barOptions"></p-chart>
                  </div>
            </div>

            <div class="col-4">
                  <div class="card flex flex-column align-items-center">
                        <h5 class="text-left w-full">End Users Tiers Distribution Chart</h5>
                        <p-chart type="pie" [data]="EndUserTierStatistics" [options]="pieOptions"></p-chart>
                  </div>
            </div>
            <div class="col-4">
                  <div class="card flex flex-column align-items-center">
                        <h5>End Users Gender Distribution Chart</h5>
                        <p-chart type="pie" [data]="EndUserGenderStatistics" [options]="pieOptions"></p-chart>
                  </div>
            </div>
            <div class="col-4">
                  <div class="card flex flex-column align-items-center">
                        <h5 class="text-left w-full">End Users Location Distribution Chart</h5>
                        <p-chart type="pie" [data]="EndUserLocationStatistics" [options]="pieOptions"></p-chart>
                  </div>
            </div>

      </div>
      <!-- Companies Charts -->
      <div class="row grid p-fluid justify-content-center" *ngIf="selectedCard === 'companies'">
            <div class="col-6">
                  <div class="card">
                        <h5>Companies Yearly Chart</h5>
                        <p-chart type="line" [data]="CompaniesYearly" [options]="lineOptions"></p-chart>
                  </div>
            </div>
      </div>
      <!-- discounts Charts -->
      <div class="row grid p-fluid justify-content-center" *ngIf="selectedCard === 'discounts'">
            <div class="col-6">
                  <div class="card">
                        <h5>Discounts Yearly Chart</h5>
                        <p-chart type="line" [data]="DiscountYearly" [options]="lineOptions"></p-chart>
                  </div>
            </div>
      </div>
      <!-- discountsRedemption Charts -->
      <div class="row grid p-fluid justify-content-center" *ngIf="selectedCard === 'discountsRedemption'">
            <div class="col-6">
                  <div class="card">
                        <h5>Discounts Redemption Yearly Chart</h5>
                        <p-chart type="line" [data]="DiscountRedemptionYearly" [options]="lineOptions"></p-chart>
                  </div>
            </div>
      </div>
      <!-- coupons Charts -->
      <div class="row grid p-fluid justify-content-center" *ngIf="selectedCard === 'coupons'">
            <div class="col-6">
                  <div class="card">
                        <h5>Coupons Yearly Chart</h5>
                        <p-chart type="line" [data]="CouponsYearly" [options]="lineOptions"></p-chart>
                  </div>
            </div>
      </div>
      <!-- couponsRedemption Chart -->
      <div class="row grid p-fluid justify-content-center" *ngIf="selectedCard === 'couponsRedemption'">
            <div class="col-6">
                  <div class="card">
                        <h5>Coupons Redemption Yearly Chart</h5>
                        <p-chart type="line" [data]="CouponsRedemptionYearly" [options]="lineOptions"></p-chart>
                  </div>
            </div>
      </div>
      <!-- broadcastMessagesSent Chart -->
      <div class="row grid p-fluid justify-content-center" *ngIf="selectedCard === 'broadcastMessagesSent'">
            <div class="col-6">
                  <div class="card">
                        <h5>Broadcast Messages Sent Yearly Chart</h5>
                        <p-chart type="line" [data]="BroadcastMessagesSentYearly" [options]="lineOptions"></p-chart>
                  </div>
            </div>
      </div>
      <!-- broadcastMessages Chart -->
      <div class="row grid p-fluid justify-content-center" *ngIf="selectedCard === 'broadcastMessages'">
            <div class="col-6">
                  <div class="card">
                        <h5>Broadcast Messages Yearly Chart</h5>
                        <p-chart type="line" [data]="BroadcastMessagesYearly" [options]="lineOptions"></p-chart>
                  </div>
            </div>
      </div>
</div>