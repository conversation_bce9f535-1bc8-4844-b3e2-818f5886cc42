import {AbstractControl, ValidationErrors} from '@angular/forms';

export class UrlValidator {

   static isValidUrlFormat(control: AbstractControl) : ValidationErrors | null{
    let URL_REGEXP = new RegExp('^(http[s]?:\\/\\/(www\\.)?|ftp:\\/\\/(www\\.)?|www\\.){1}([0-9A-Za-z-\\.@:%_\+~#=]+)+((\\.[a-zA-Z]{2,3})+)(/(.)*)?(\\?(.)*)?');
    
    if (control.value != "" && control.value !== null) {
        if(control.value.length <= 8 || !URL_REGEXP.test(control.value)){
            return {'invalidUrlError': 'validationMessages.invalidUrl'};
        }
    }

    return null;
    }

}