<div>
      <p-confirmDialog header="Confirmation" key="confirm1" icon="pi pi-exclamation-triangle"
            message="Are you sure you want to proceed?" [style]="{ width: '350px' }"
            acceptButtonStyleClass="p-button-text" rejectButtonStyleClass="p-button-text"></p-confirmDialog>

      <p-table #dt [value]="malls" [paginator]="paginator" [rows]="pageSize" [totalRecords]="totalRecords" [lazy]="true"
            [loading]="loading" (onLazyLoad)="loadData($event)" [showCurrentPageReport]="true"
            [currentPageReportTemplate]="'Showing {first} to {last} of {totalRecords} records'"
            responsiveLayout="scroll" [rowHover]="true" styleClass="p-datatable-gridlines">

            <!-- (click)=" add()" -->
            <ng-template *ngIf="user.UserType == 0" pTemplate="caption">
                  <app-grid-headers [myDt]="malls" (SearchEvent)='ReceivedFilteredData($event)' addNewTxt="Add New mall"
                        goRoute="/mall-new" gridTitle="malls" [title]="title">
                  </app-grid-headers>
            </ng-template>
            <ng-template pTemplate="header">
                  <tr>
                        <th>
                              <div class="flex justify-content-between align-items-center my-center-text">
                                    {{ "ID" | translate }}
                              </div>
                        </th>
                        <th>
                              <div class="flex justify-content-between align-items-center">
                                    {{ "mall Name" | translate }}
                              </div>
                        </th>
                        <th>
                              <div class="flex justify-content-between align-items-center">
                                    {{ "Location" | translate }}
                              </div>
                        </th>
                        <th>
                              <div class="flex justify-content-between align-items-center">
                                    {{ "Number Of Companies" | translate }}
                              </div>
                        </th>
                        <th>
                              <div class="flex justify-content-between align-items-center">
                                    {{ "Actions" | translate }}
                              </div>
                        </th>
                  </tr>
            </ng-template>
            <ng-template pTemplate="body" let-mall>
                  <tr>

                        <td>
                              <div class="flex">
                                    {{ mall.UserFriendly }}
                              </div>
                        </td>
                        <td>
                              <div class="flex">
                                    <p-avatar *ngIf="mall.LogoUrl" [alt]="mall.EnName"
                                          image="{{getLogoUrl( mall.LogoUrl)}}" styleClass="mr-2" size="medium"
                                          shape="circle"></p-avatar>
                                    {{ mall.EnName }}
                              </div>
                        </td>
                        <td>
                              <div class="flex">
                                    {{ mall.Location }}
                              </div>
                        </td>
                        <td>
                              <div class="flex">
                                    {{ mall.AssociatedCompaniesNum }}
                              </div>
                        </td>
                        <!-- <td>
                              <span class="p-column-title">Phone Numbers</span>
                              <ng-container *ngIf="mall.PhoneNumbers?.length > 0">
                                    <span *ngFor="let phone of mall.PhoneNumbers">
                                          <img src="assets/demo/flags/flag_placeholder.png"
                                                [class]="'flag flag-' + phone.countryCode.toLowerCase()"
                                                alt="{{ phone.countryCode }}" />
                                          <span>{{phone.countryCode }}-{{phone.phone}} </span>
                                          <br>
                                          <br>
                                    </span>
                              </ng-container>
                        </td> -->
                        <td>
                              <div class="flex">
                                    <div class="d-flex align-items-center justify-content-between">
                                          <p-button icon="pi pi-pencil" styleClass="p-button-rounded" class="mx-1"
                                                (click)="
                                    edit(mall, 'edit' )"></p-button>
                                          <p-button *ngIf="user.UserType == 0" icon="pi pi-trash"
                                                styleClass="p-button-rounded p-button-danger" class="mx-1"
                                                (click)="delete(mall.Id)"></p-button>
                                    </div>
                              </div>
                        </td>
                  </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
                  <tr>
                        <td class="text-center" colspan="7">
                              {{ "No malls found." | translate }}
                        </td>
                  </tr>
            </ng-template>
      </p-table>
</div>