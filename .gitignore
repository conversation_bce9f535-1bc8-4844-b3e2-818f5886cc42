# See http://help.github.com/ignore-files/ for more about ignoring files.

# Compiled output
/dist
/tmp
/out-tsc
# Only exists if <PERSON><PERSON> was run
/bazel-out

# Dependencies
/node_modules
/.angular

# Cordova
/www
/plugins
/platforms

# Electron
/dist-electron
/dist-packages
/electron.main.js

# IDEs and editors
.idea/*
!.idea/runConfigurations/
!.idea/codeStyleSettings.xml
build
dist
node_modules
.DS_Store
.idea
sassdoc

# src/assets/theme/*/*.css
# src/assets/layout/css/*.css
# src/assets/demo/*.css
# src/assets/sass/theme/designer
# See http://help.github.com/ignore-files/ for more about ignoring files.


# profiling files
chrome-profiler-events.json
speed-measure-plugin.json

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
xcuserdata/
*.sublime-workspace

# IDE - VSCode
.vs/*
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Maven
/target
/log

# misc
/.angular/cache
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings
/reports
/src/translations/template.*
# /src/environments/.env.*

# System Files
.DS_Store
Thumbs.db