import { CompanyIndustryModel } from './CompanyIndustryModel';
import { CompanyModel } from './CompanyModel';
import { FilterModel } from './FiltersModel';

export class CompanyFilterModel {
    /**
     *
     */
    constructor() {

    }

    Id?: string = "";
    
    //SubCategoryId?: string = "";
    FilterId?: string = "";
    Filter?: FilterModel;
    
    CompanyId?: string = "";
    Company?: CompanyModel;
}