import { Injectable } from '@angular/core';
import { TiersModel } from '../Model/TiersModel';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SharedDataComponentService {

  public TieresDataSource = new BehaviorSubject<Array<TiersModel>>([]);
  public Tieres = this.TieresDataSource.asObservable();


  constructor() { }
  updateTieresDataSource(tiers: TiersModel[]) {
    this.TieresDataSource.next(tiers);
  }
  
}
