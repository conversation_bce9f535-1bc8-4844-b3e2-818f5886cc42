import { CouponModel } from '../Model/CouponModel';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Dto } from '../Model/Dto';
import { environment } from 'src/environments/environment';
import { Observable, catchError, map, of } from 'rxjs';
import { CouponStatusViewModel } from '../Model/CouponStatusViewModel';
import { DatePipe } from '@angular/common';
import { AuthService } from './auth.service';

@Injectable({
    providedIn: 'root'
})
export class CouponService {


    constructor(private httpClient: HttpClient, private datePipe: DatePipe, private authService: AuthService) {
    }

    getCouponForCompany( page: number = null, pageSize: number = null, CompanyOrCouponName: string = "", startDate: Date | null = null, endDate: Date | null = null) {

        let params = new HttpParams();
        if (CompanyOrCouponName) {
            params = params.set('CompanyNameOrCouponTitle', CompanyOrCouponName)
        }
        if (page) {
            params = params.set('PageNumber', page);
        }
        if (pageSize) {
            params = params.set('PageSize', pageSize);
        }
        if (startDate)
            // params = params.set('DateRange.StartDate', startDate.toISOString())
            params = params.set('DateRange.StartDate', this.datePipe.transform(startDate, 'yyyy-MM-dd'))
        if (endDate)
            params = params.set('DateRange.EndDate', this.datePipe.transform(endDate, 'yyyy-MM-dd'))
        // params = params.set('DateRange.EndDate', endDate.toISOString())

        return this.httpClient.get(`${environment.apiUrl}Coupon/GetCouponForCompany`
            , { params }
        ).pipe(
            map((res: any) => {

                return res;
            })
        );
    }

    getCouponForAdmin(page: number = null, pageSize: number = null, ReviewedByAdmin = true, Deactivated = false, CompanyOrCouponName: string = "", startDate: Date | null = null, endDate: Date | null = null) {
        let params = new HttpParams();
        if (CompanyOrCouponName) {
            params = params.set('CompanyNameOrCouponTitle', CompanyOrCouponName)
        }
        if (startDate)
            // params = params.set('DateRange.StartDate', startDate.toISOString())
            params = params.set('DateRange.StartDate', this.datePipe.transform(startDate, 'yyyy-MM-dd'))
        if (endDate)
            params = params.set('DateRange.EndDate', this.datePipe.transform(endDate, 'yyyy-MM-dd'))
        // params = params.set('DateRange.EndDate', endDate.toISOString())
        if (page) {
            params = params.set('PageNumber', page);
        }
        if (pageSize) {
            params = params.set('PageSize', pageSize);
        }
        return this.httpClient.get(`${environment.apiUrl}Coupon/GetCouponForAdmin?ReviewedByAdmin=${ReviewedByAdmin}&Deactivated=${Deactivated}`, { params })
            .pipe(
                map((res: any) => {

                    if (res.HasError) {
                        return of(false);
                    } else {
                        return res;
                    }
                })
            );
    }

    getCouponById(Id: string = "") {
        return this.httpClient.get<Dto<CouponModel>>(`${environment.apiUrl}Coupon/GetCouponById?Id=${Id}`)
            .pipe(
                map((res: any) => {
                    return res['ResultContent'];
                })
            );
    }
    public AddCoupon(data): Observable<Dto<CouponModel>> {
        var http;
        var url = `${environment.apiUrl}Coupon/AddCoupon`;
        http = this.httpClient.post(url, data);
        return http.pipe(
            map((res: Dto<CouponModel>) => {
                    return res;
            }),
            catchError(error => {
                return of(false);
            }));
    }
    public EditCoupon(data): Observable<Dto<CouponModel>> {
        var http;
        var url = `${environment.apiUrl}Coupon/EditCoupon`;
        http = this.httpClient.put(url, data);
        return http.pipe(
            map((res: Dto<CouponModel>) => {
                    return res;
            }),
            catchError(error => {
                return of(false);
            }));
    }
    public RejectCoupon(CouponModel: CouponStatusViewModel) {
        return this.httpClient.patch(`${environment.apiUrl}Coupon/ReviewCoupon`, CouponModel)
            .pipe(
                map((res: any) => {
                    return res;
                })
            );
    }
    public AcceptCoupon(CouponModel: CouponStatusViewModel) {
        return this.httpClient.patch(`${environment.apiUrl}Coupon/ReviewCoupon`, CouponModel)
            .pipe(
                map((res: any) => {
                    return res;
                })
            );
    }
    public EditActiveCoupon(CouponId: String): Observable<any> {
        var http;

        var url = `${environment.apiUrl}Coupon/EditCouponActive?id=${CouponId}`;
        http = this.httpClient.patch(url, {});
        return http.pipe(
            map((res: any) => {
                return res;
            }),
            catchError((error) => {
                return of(false);
            })
        );
    }
    public DeleteCoupon(CouponId: string): Observable<any> {
        var http;
        var url = `${environment.apiUrl}Coupon/DeleteCoupon?Id=${CouponId}`;

        http = this.httpClient.delete(url);
        return http.pipe(
            map((res: any) => {

                return res;
            }),
            catchError((error) => {

                return of(false);
            })
        );
    }
    //     public GetAllFiltered(CompanyOrCouponName: string, startDate: Date, endDate: Date): Observable<any> {
    //         let params = new HttpParams();

    //         if (CompanyOrCouponName) {
    //             params = params.set('CompanyNameOrCouponTitle', CompanyOrCouponName)
    //         }
    //         if (startDate)
    //             params = params.set('DateRange.StartDate', startDate.toISOString())
    //         if (endDate)
    //             params = params.set('DateRange.EndDate', endDate.toISOString())


    //         return this.httpClient.get(`${environment.apiUrl}Coupon/GetCouponForAdmin`, { params });
    //     }
}
