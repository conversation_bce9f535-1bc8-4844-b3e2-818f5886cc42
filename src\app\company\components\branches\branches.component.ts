import { WeekDay } from '@angular/common';
import { AfterViewInit, ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, ViewChild, ElementRef, ViewRef, Renderer2 } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { CompanyBranchModel } from 'src/app/Model/CompanyBranchModel';
import { CompanyModel } from 'src/app/Model/CompanyModel';
import { CompanyFilterModel } from 'src/app/Model/CompanyFilterModel';
import { UserModel } from 'src/app/Model/UserModel';
import { IndustryService } from 'src/app/services/industry.service';
import { openCloseHourssValidator } from 'src/app/validators/open-close-hours.directive';
import { OpenHoursComponent } from 'src/app/company/components/open-hours/open-hours.component'
import { CompanyService } from 'src/app/services/company.service';
import { ConfirmationDialogComponent } from 'src/app/components/confirmation-dialog/confirmation-dialog.component';
import { PhoneNumberModel } from 'src/app/Model/PhoneNumberModel';
import { Countries } from 'src/app/utilities/countries';
import { AuthService } from 'src/app/services/auth.service';

@Component({
    selector: 'app-branches',
    templateUrl: './branches.component.html',
    styleUrls: ['./branches.component.scss'],

})
export class BranchesComponent implements OnInit {

    warningMessage: string = '';
    @ViewChild('confirmationDialog') confirmationDialog: ConfirmationDialogComponent;
    confirmationAction: string = '';
    OverrideWarning: boolean = false;
    allCountries: Countries = new Countries();
    openHoursDialog: boolean;
    @ViewChild('OpenHoursComponent') openHoursComponent: OpenHoursComponent;

    @Input() _OverViewData: CompanyModel;
    @Input() activeIndex: number;
    @Input() fileToUpload: any[];
    @Output() activeIndexChange: EventEmitter<number> = new EventEmitter();
    branchesForm = new FormGroup({
        branchEnName: new FormControl('', [Validators.required, Validators.minLength(5)]),
        branchArName: new FormControl('', [Validators.required, Validators.minLength(5)]),
        branchCountry: new FormControl(''),
        branchCity: new FormControl('',),
        branchRegion: new FormControl('',),
        serviceListUrl: new FormControl('', [Validators.pattern(/^(ftp|http|https):\/\/[^ "]+$/)]),
        googleMapLink: new FormControl('', [Validators.pattern(/^(https?:\/\/)?(www\.)?google\.com\/maps\/(place\/)?[-\w]+(\/@\d+(\.\d+)?,\d+(\.\d+)?)?,\d+((\.\d+)?z|\w+=\w+)*$/),]),
        openHours: new FormGroup({
            openTime: new FormControl(''),
            closeTime: new FormControl(''),
            openTime2: new FormControl(''),
            closeTime2: new FormControl(''),
        }, { validators: openCloseHourssValidator }),

    });
    routeState: any;
    enableEdit: boolean = false;
    _CompanyBranch: CompanyBranchModel = new CompanyBranchModel();
    filters: CompanyFilterModel[];
    user: UserModel;
    pressedSave = false;
    malls = [];
    selectedMall: string = "";
    mainbranch: boolean = true;

    constructor(private ref: ChangeDetectorRef,
        private router: Router, private messageService: MessageService,
        private industryService: IndustryService, private companyService: CompanyService, private authService: AuthService) {
        this.user = this.authService.getUserData();
        if (this.router.getCurrentNavigation()?.extras.state) {
            this.routeState = this.router.getCurrentNavigation()?.extras.state;
            if (this.routeState) {
                this._OverViewData = this.routeState.data ? this.routeState.data : new CompanyModel();
                this._OverViewData.CompanyBranches = this.routeState.data.CompanyBranches ?
                    this.routeState.data.CompanyBranches : [];

            }
        }
        industryService.GetAllFilters().subscribe((data) => {
            this.filters = data;
        });
    }
    ngOnInit(): void {
        this.openHoursDialog = false;

        this._CompanyBranch.SameDay = true;
        this._CompanyBranch.initCompanyBranchWorkHours();
        //this._CompanyBranch.Country = this._OverViewData.Countries[0];
        if (this.user.UserType != 0) {
            this.branchesForm.get('branchEnName')?.disable();
            this.branchesForm.get('branchArName')?.disable();
        }
        this.toggleIsOnline();
        if (this._OverViewData.hasError == true) {
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Check your inputs prev Tab' });
            return;
        }
        if (this._CompanyBranch.PhoneNumber == null) {
            this._CompanyBranch.PhoneNumber = new PhoneNumberModel();

        }

    }

    SaveButton() {
        if (this._CompanyBranch.SameDay) {
            if (!this.branchesForm.invalid)
                this.openHoursDialog = false;
        }
        else if (!this.openHoursComponent.openHoursGroup.invalid)
            this.openHoursDialog = false;

    }

    showDialog() {
        // if (!this.branchesForm.invalid) sometimes the from invalid from work Hours and we can't open a condition to branch Name
        // if (this.branchesForm.get('branchEnName').valid && this.branchesForm.get('branchArName').valid)
        if (this._CompanyBranch.EnName != null && this._CompanyBranch.ArName != null)
            this.openHoursDialog = true;
    }

    changeCountry() {
        this._OverViewData.AllCities = this._CompanyBranch.Country.Cities;
        // this.countryService.Countries.subscribe((data) => {
        //     this._OverViewData.AllCities = data.filter(x => x.Id == this._CompanyBranch.City.CountryId)[0].Cities;
        // })
    }

    getMalls() {
        if (!this._CompanyBranch.Country) {
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please select Country' });
            return;
        }
        if (!this._CompanyBranch.City) {
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please select City' });
            return;
        }

        //this.malls = this._CompanyBranch.City.Malls;


        this.malls = this._CompanyBranch.Country.Cities.find(x => x.Id == this._CompanyBranch.City.Id).Malls;

        /*this.countryService.Countries.subscribe((data) => {
            this.malls = data.filter(x => x.Id == this._CompanyBranch.Country.Id)[0].Cities[0].Malls;
        })*/
    }

    addToCompanyBranches() {
        this.pressedSave = true;
        if (!this.branchesForm.valid) {
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Check your input' });
            return;
        }
        if (!this._OverViewData.CompanyBranches) {
            this._OverViewData.CompanyBranches = [];
        }
        if (this._OverViewData.CompanyBranches.findIndex(x => x.IsMainBranch) == -1) {
            this._CompanyBranch.IsMainBranch = true;
        }
        else {
            this._CompanyBranch.IsMainBranch = false;
        }
        var x = 0;
        if (this._OverViewData.CompanyBranches.length > 0) {
            x = this._OverViewData.CompanyBranches.reduce((a, b) => a.RowId > b.RowId ? a : b).RowId;
        }
        if (this._CompanyBranch.PhoneNumber?.Number)
            this._CompanyBranch.PhoneNumber.Number = this._CompanyBranch.PhoneNumber?.Number.toString();
        else {
            this._CompanyBranch.PhoneNumber = null;
        }
        // this.countryService.Countries.subscribe((data) => {
        //     this._OverViewData.AllCities = data.filter(x => x.Id == this._CompanyBranch.Country.Id)[0].Cities;
        // });
        // first Inti To x when new added 
        if (x == undefined) this._CompanyBranch.RowId = 1; else
            this._CompanyBranch.RowId = ++x;
        this._CompanyBranch.CityName = this._CompanyBranch.City != null ? this._CompanyBranch.City.EnName : '';
        this._CompanyBranch.CountryName = this._CompanyBranch.Country != null ? this._CompanyBranch.Country.EnName : '';
        this._OverViewData.CompanyBranches.push(this._CompanyBranch)

        this._CompanyBranch = new CompanyBranchModel();
        this.openHoursDialog = false;
        this._CompanyBranch.SameDay = true;
        this._CompanyBranch.initCompanyBranchWorkHours();
        this._CompanyBranch.PhoneNumber = new PhoneNumberModel();
        this.branchesForm.reset();
        this.pressedSave = false;
        // this.ref.detectChanges();
    }

    next() {
        if (this._OverViewData.CompanyBranches.length == 0) {
            this._OverViewData.hasError = true;
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please Enter One Branch at least' });
            return;
        }
        if (this.isBranchesContainsCountries() == false) {
            this._OverViewData.hasError = true;
            this.messageService.add({ severity: 'error', summary: 'Error', detail: "Branch locations don't match the company's countries.Please change branch locations" });
            return;
        }
        this._OverViewData.CompanyBranches?.forEach((branch) => {
            delete branch.City?.Malls;
        })
        this._OverViewData.CompanyBranches?.forEach((branch) => {
            delete branch.Country;
        })
        this._OverViewData.hasError = false;
        this.activeIndex++;
        this.activeIndexChange.emit(this.activeIndex);
    }

    back() {
        this.activeIndex--;
        this.activeIndexChange.emit(this.activeIndex);
    }

    showSelectedBranch(branch: CompanyBranchModel) {
        if (branch.Active == false) {
            this.messageService.add({
                severity: "warn",
                summary: "Warning",
                detail: "Please activate the company Branch before Editing ! ",
            });

        } else {
            // Scroll to the top of the page
            window.scrollTo({ top: 0, behavior: 'smooth' });
            this.enableEdit = true;

            if (branch.Id == null)
                this._CompanyBranch = JSON.parse(JSON.stringify(this._OverViewData.CompanyBranches.find((x) => x.RowId == branch.RowId)));
            else
                this._CompanyBranch = JSON.parse(JSON.stringify(this._OverViewData.CompanyBranches.find((x) => x.Id == branch.Id)));
            this._CompanyBranch.CompanyBranchWorkHours?.sort((a, b) => a.Day > b.Day ? 1 : -1);
            // this._CompanyBranch.City = branch.City;
            if (branch.City) {
                this._CompanyBranch.Country = this._OverViewData.Countries.find(x => x.Id == this._CompanyBranch.City.CountryId);
                this.changeCountry();
                // this._CompanyBranch.City = branch.City;
                this.getMalls();
            }

            //this._CompanyBranch.City = branch.City;
            this.mainbranch = this._CompanyBranch.IsMainBranch;
            // if (this._CompanyBranch.CompanyBranchFilters) {
            //     this.industryService.GetAllFilters().subscribe((data) => {
            //         if (this._CompanyBranch && this._CompanyBranch.CompanyBranchFilters.length > 0) {
            //             this._CompanyBranch.Filter = data.filter(x => this._CompanyBranch.CompanyBranchFilters.findIndex(y => y.FilterId == x.Id) != -1)
            //         }
            //     });
            // }

            if (!this._CompanyBranch.CompanyBranchWorkHours || this._CompanyBranch.CompanyBranchWorkHours.length < 7) {
                this._CompanyBranch.SameDay = true;
                this._CompanyBranch.CompanyBranchWorkHours = [
                    { Day: WeekDay.Sunday, DayChar: 'S', OpenTime: '', CloseTime: '', OpenTime2: '', CloseTime2: '', Active: true, IsSplite: false, CompanyBranchId: "" },
                    { Day: WeekDay.Monday, DayChar: 'M', OpenTime: '', CloseTime: '', OpenTime2: '', CloseTime2: '', Active: false, IsSplite: false, CompanyBranchId: "" },
                    { Day: WeekDay.Tuesday, DayChar: 'T', OpenTime: '', CloseTime: '', OpenTime2: '', CloseTime2: '', Active: false, IsSplite: false, CompanyBranchId: "" },
                    { Day: WeekDay.Wednesday, DayChar: 'W', OpenTime: '', CloseTime: '', OpenTime2: '', CloseTime2: '', Active: false, IsSplite: false, CompanyBranchId: "" },
                    { Day: WeekDay.Thursday, DayChar: 'T', OpenTime: '', CloseTime: '', OpenTime2: '', CloseTime2: '', Active: false, IsSplite: false, CompanyBranchId: "" },
                    { Day: WeekDay.Friday, DayChar: 'F', OpenTime: '', CloseTime: '', OpenTime2: '', CloseTime2: '', Active: false, IsSplite: false, CompanyBranchId: "" },
                    { Day: WeekDay.Saturday, DayChar: 'S', OpenTime: '', CloseTime: '', OpenTime2: '', CloseTime2: '', Active: false, IsSplite: false, CompanyBranchId: "" },
                ];
                // this._CompanyBranch.CompanyBranchWorkHours.push({ Day: WeekDay.Sunday, DayChar: 'S', OpenTime: '', CloseTime: '', OpenTime2: '', CloseTime2: '', Active: true, IsSplite: false, CompanyBranchId: "" },);
            }
            if (this._CompanyBranch.PhoneNumber == null) {
                this._CompanyBranch.PhoneNumber = new PhoneNumberModel();

            }

            setTimeout(() => {
            }, 1000);

            // this._CompanyBranch.Mall = this.malls.find((x => x.Id == this._CompanyBranch.MallId));
            this._CompanyBranch.Mall = branch.Mall;
            // this._CompanyBranch.City = branch.City;
            //   this.ref.detectChanges();
        }
    }

    editBranch() {

        this._CompanyBranch.IsMainBranch = this.mainbranch;
        this.ref.detectChanges();
        this.openHoursDialog = false;
        this._CompanyBranch.CityName = this._CompanyBranch.City != null ? this._CompanyBranch.City.EnName : '';
        this._CompanyBranch.CountryName = this._CompanyBranch.Country != null ? this._CompanyBranch.Country.EnName : '';
        if (this._CompanyBranch.PhoneNumber?.Number)
            this._CompanyBranch.PhoneNumber.Number = this._CompanyBranch.PhoneNumber?.Number.toString();
        else {
            this._CompanyBranch.PhoneNumber.Number = null;
            this._CompanyBranch.PhoneNumber.CountryCode = null;
        }

        this._OverViewData.CompanyBranches[this._OverViewData.CompanyBranches.findIndex((x) => x.Id == this._CompanyBranch.Id)] = this._CompanyBranch;
        this.ref.detectChanges();
        this.enableEdit = false;
        this._CompanyBranch = new CompanyBranchModel();
        this._CompanyBranch.SameDay = true;
        this._CompanyBranch.initCompanyBranchWorkHours();
        this._CompanyBranch.PhoneNumber = new PhoneNumberModel();
        // Mark all controls as Untouched to turnoff trigger validation
        this.branchesForm.markAsUntouched();
        this.branchesForm.reset();
        this.ref.detectChanges();
    }

    onGoogleMapLinkChange(value: string) {
        this._CompanyBranch.GoogleMapLink = value;
    }

    onChangeMainBranch(branch, isChecked: boolean) {
        var CompanyBranchIndex = new CompanyBranchModel();
        if (branch.Id == null)
            CompanyBranchIndex = this._OverViewData.CompanyBranches.find((x) => x.RowId == branch.RowId);
        else
            CompanyBranchIndex = this._OverViewData.CompanyBranches.find((x) => x.Id == branch.Id);
        /*this._OverViewData.CompanyBranches.forEach((x) => {
            x.IsMainBranch = false;
            this.mainbranch = false;

        });
      CompanyBranchIndex.IsMainBranch = !CompanyBranchIndex.IsMainBranch;
         */
        // Loop through items and set only the selected one as main
        this._OverViewData.CompanyBranches.forEach(item => {
            item.IsMainBranch = false;
            if (item === CompanyBranchIndex)
                item.IsMainBranch = isChecked;
            this.mainbranch = false;
        });

        // if (x.RowId != index) {
        //     console.log('mainBranchNotIndex', x);
        //     x.IsMainBranch = false;
        //     this.mainbranch = false;
        // }
        // else {
        //     x.IsMainBranch = true;
        //     this.mainbranch = true;
        //     console.log('mainBranchIndex', x);
        // }
    }

    onChangeActiveBranch(branch) {
        if (branch.Id != null) {
            this.companyService.EditCompanyBranchActive(branch.Id, this.OverrideWarning).subscribe((data) => {
                if (data['HasError'] == true) {
                    this.OverrideWarning = true;
                    this.confirmationAction = 'active';
                    this.confirmationDialog.item = this._CompanyBranch;
                    this._CompanyBranch = branch;
                    this.confirmationDialog.openDialog();
                    this.confirmationDialog.message = data['EnErrorMessage'];
                }
            });
        }
    }

    deleteSelectedBranch(branch: CompanyBranchModel) {
        // Assuming branches is your array of branches
        const branchIndex = this._OverViewData.CompanyBranches.findIndex(branch => branch.Id === branch.Id);
        // Check if the branch to delete is the last item
        const isLastBranch = branchIndex === this._OverViewData.CompanyBranches.length - 1;

        if (isLastBranch) {
            console.log("This is the last branch in the array.");
            this.messageService.add({
                severity: "error",
                summary: "Delete Branch",
                detail: "The last branch cannot be deleted. Please ensure at least one branch remains for the company. ",
            });
            return;
        }
        // Method to show the confirmation dialog
        this.confirmationDialog.message = 'Do you want to delete this Company Branch ' + branch.EnName;
        this.confirmationDialog.item = branch;
        this.confirmationDialog.openDialog();
        this._CompanyBranch = branch;
        this.confirmationAction = 'delete';

    }

    // Method to handle the confirmation result
    handleConfirmationAction(result: boolean): void {
        if (this.confirmationAction == 'delete') {
            if (result) {
                if (this._CompanyBranch.Id == null || this._CompanyBranch.Id == undefined) {
                    this._OverViewData.CompanyBranches = this._OverViewData.CompanyBranches.filter(currentItem => currentItem.Id != this._CompanyBranch.Id);
                    this._CompanyBranch = new CompanyBranchModel();
                    this.OverrideWarning = false;
                    this.confirmationAction = '';
                }
                else {
                    this.companyService.DeleteCompanyBranch(this._CompanyBranch.Id, this.OverrideWarning).subscribe((data) => {
                        if (data['HasError'] == false) {
                            // Update the data array (remove the deleted item)
                            this._OverViewData.CompanyBranches = this._OverViewData.CompanyBranches.filter(currentItem => currentItem.Id != this._CompanyBranch.Id);
                            this._CompanyBranch = new CompanyBranchModel();
                            this.OverrideWarning = false;
                            this.confirmationAction = '';

                        } else {
                            this.OverrideWarning = true;
                            this.confirmationDialog.item = this._CompanyBranch;
                            this.confirmationDialog.openDialog();
                            this.confirmationDialog.message = data['EnErrorMessage'];
                        }
                    });
                }
            } else {
                this.confirmationAction = '';
                this._CompanyBranch = new CompanyBranchModel();
            }


        } else if (this.confirmationAction == 'active') {
            if (result) {
                if (this._CompanyBranch.Id == null || this._CompanyBranch.Id == undefined) {

                    this._CompanyBranch = new CompanyBranchModel();
                    this.OverrideWarning = false;
                    this.confirmationAction = '';
                }
                else {
                    this.companyService.EditCompanyBranchActive(this._CompanyBranch.Id, this.OverrideWarning).subscribe((data) => {
                        if (data['HasError'] == true) {
                            this.OverrideWarning = true;
                            this.confirmationDialog.item = this._CompanyBranch;
                            this.confirmationDialog.openDialog();
                            this.confirmationDialog.message = data['EnErrorMessage'];
                        } else {
                            this.confirmationAction = '';
                            this.OverrideWarning = false;
                            this._CompanyBranch = new CompanyBranchModel();
                        }
                    });
                }
            } else {
                this.confirmationAction = '';
                this.OverrideWarning = false;
                this._CompanyBranch = new CompanyBranchModel();
            }


        }
    }

    CheckValid(input: FormControl) {
        if (input.invalid && (this.pressedSave || input.touched)) {
            return 'red';
        }
        return '#515C66';
    }

    confirmDeleteBranch(branch: CompanyBranchModel) {

        if (branch.Id == null || branch.Id == undefined) {
            const index = this._OverViewData.CompanyBranches.indexOf(branch, 0);
            if (index > -1) {
                this._OverViewData.CompanyBranches.splice(index, 1);
                this.ref.detectChanges();

            }
            this._CompanyBranch = new CompanyBranchModel();
        }
        else {
            this.companyService.DeleteCompanyBranch(branch.Id, true).subscribe((data) => {
                if (data['HasError'] == false) {
                    const index = this._OverViewData.CompanyBranches.indexOf(branch, 0);
                    if (index > -1) {
                        this._OverViewData.CompanyBranches.splice(index, 1);
                        this.ref.detectChanges();

                    }
                    this._CompanyBranch = new CompanyBranchModel();
                    //  this.deleteBranchDialog = false;

                } else {
                    this.confirmationDialog.item = this._CompanyBranch;
                    this.confirmationDialog.openDialog();
                    this.confirmationDialog.message = data['EnErrorMessage'];
                }
            });
        }

    }

    isBranchesContainsCountries() {
        const isSubset = this._OverViewData.CompanyBranches.filter(branch => branch.IsOnline == false).every(branch => this._OverViewData.Countries.some(country => branch.City?.CountryId == country.Id));
        return isSubset;

    }

    canActiveBranch(branch: CompanyBranchModel): boolean {
        // Multi-condition logic to determine if the branch can be actived

        // if (branch.Id == null)
        //     return false;

        // If none of the above conditions are met, return true
        return true;
    }

    toggleIsOnline() {

        if (this._CompanyBranch.IsOnline) {
            // Clear validators if isOnline is true
            this.branchesForm.get('branchCountry')?.clearValidators();
            this.branchesForm.get('branchCity')?.clearValidators();
            this.branchesForm.get('branchCountry')?.reset();
            this.branchesForm.get('branchCity')?.reset();
            this.branchesForm.get('googleMapLink')?.reset();
            this.branchesForm.get('branchRegion')?.reset();
        } else {
            // Set required validators if isOnline is false
            this.branchesForm.get('branchCountry')?.setValidators([Validators.required]);
            this.branchesForm.get('branchCity')?.setValidators([Validators.required]);
        }
        // Update validity after setting validators
        this.branchesForm.get('branchCountry')?.updateValueAndValidity();
        this.branchesForm.get('branchCity')?.updateValueAndValidity();
    }

}
