import { HttpClient } from '@angular/common/http';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { MessageService, TreeNode } from 'primeng/api';
import { Country } from 'src/app/Model/Country';
import { IndustryService } from 'src/app/services/industry.service';

@Component({
	selector: 'country-available-categories',
	templateUrl: './country-available-categories.component.html',
	styleUrls: ['./country-available-categories.component.scss'],
	providers: [MessageService]
})
export class CountryAvailableCategoriesComponent implements OnInit {

	@Input() _CountryData: Country;
	@Input() isEditing: boolean;
	@Input() activeIndex: number;
	@Output() activeIndexChange: EventEmitter<number> = new EventEmitter();
	routeState: any;
	pressedNext = false;
	showMessage: boolean = false;
	selectedIndsutries: any[] = [];
	industries: any[] = [];

	constructor(private http: HttpClient, private router: Router, private messageService: MessageService, private ref: ChangeDetectorRef, private industryService: IndustryService,
	) {
	}

	ngOnInit(): void {
		this.industryService.GetAllIndustry(true).subscribe((data) => {
			// this.industries = data.ListResultContent;
			this.industries = this.convertToTree(data.ListResultContent);
			if (this._CountryData.CountryIndustries.length > 0) {
				this.markSelectedNodes();

			}
		})
	}

	next() {

		if (this.selectedIndsutries.length == 0) {
			this.showMessage = true;
			return;
		}
		this.showMessage = false;
		this._CountryData.CountryIndustries = this.convertSelectedNodesToIndustries(this.selectedIndsutries);

		// if (this._CountryData.HasError == true) {
		//   this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please Check the tabs data !' });
		//   window.scroll({
		//     top: 0,
		//     left: 0,
		//     behavior: 'smooth'
		//   });
		//   return;
		// } else {
		this.pressedNext = true;
		this._CountryData.HasError = false;
		// The activeIndex was set to a direct value because during editing, the navigate tabs, a problem in navigation when pressing the buttons (next, back).
		this.activeIndex = 3;
		this.activeIndexChange.emit(this.activeIndex);
		// }
	}

	back() {
		// The activeIndex was set to a direct value because during editing, the navigate tabs, a problem in navigation when pressing the buttons (next, back).
		this.activeIndex = 1;
		this.activeIndexChange.emit(this.activeIndex);
	}

	convertToTree(industries: any[]): any[] {
		return industries.map(industry => ({
			key: industry.Id.toString(),
			label: industry.EnName,
			data: `industry`,
			// icon: "pi pi-fw pi-folder",
			expanded: true,
			children: (industry.Filters || []).map(filter => ({
				key: `${industry.Id}:${filter.Id}`,
				label: filter.EnName,
				data: `filter`,
				// icon: "pi pi-fw pi-file",
				expanded: true,
			}))
		}));
	}

	markSelectedNodes() {
		// Flatten tree nodes to enable easy matching
		const flattenTreeNodes = (nodes: any[]): any[] => {
			let result = [];
			for (const node of nodes) {
				result.push(node);
				if (node.children) {
					result = result.concat(flattenTreeNodes(node.children));
				}
			}
			return result;
		};

		const allNodes = flattenTreeNodes(this.industries);

		// Collect keys from selectedIndustries
		const selectedKeys = this._CountryData.CountryIndustries?.flatMap(industry => {
			const industryKey = industry.Id.toString();
			const filterKeys = (industry.Filters || []).map(filter => `${industry.Id}:${filter.Id}`);
			return [industryKey, ...filterKeys];
		});

		// Match keys with tree nodes
		this.selectedIndsutries = allNodes.filter(node => selectedKeys.includes(node.key));
	}

	convertSelectedNodesToIndustries(selectedTreeNodes: any[]): any[] {
		const industriesMap: { [Id: string]: { Id: number; EnName: string; Filters: any[] } } = {};

		selectedTreeNodes.forEach(node => {
			const keys = node.key.split(':'); // Split key into industryId and filterId
			const industryId = keys[0]; // First part is the industry UUID
			const filterId = keys.length > 1 ? keys[1] : null; // Second part (if present) is the filter UUID

			if (!industriesMap[industryId]) {
				industriesMap[industryId] = {
					Id: industryId,
					EnName: node.label, // Industry Name
					Filters: []
				};
			}

			if (filterId) {
				industriesMap[industryId].Filters.push({
					Id: filterId,
					EnName: node.label // Filter Name
				});
			}
		});

		// Convert the map to an array
		return Object.values(industriesMap);
	}

}
