broadcast-message-action {
      border-radius: var(--border-radius);
      padding: 0.25em 0.5rem;
      text-transform: uppercase;
      font-weight: 700;
      font-size: 12px;
      letter-spacing: 0.3px;
}

.broadcast-message-action.action-0 {
      @extend broadcast-message-action;
      background: #8a23df;
      color: #ffffffff;


}

.broadcast-message-action.action-1 {
      @extend broadcast-message-action;
      background: #f1f11d;
      color: rgb(0, 0, 0);
}

.broadcast-message-action.action-2 {
      @extend broadcast-message-action;
      background: #23df36;
      color: #ffffffff;
}

.broadcast-message-action.action-3 {
      @extend broadcast-message-action;
      background: #f10a0a;
      color: #ffffffff;
}

.broadcast-message-action.action-4 {
      @extend broadcast-message-action;
      background: #f10a0a;
      color: #ffffffff;
}

.broadcast-message-action.action-5 {
      @extend broadcast-message-action;
      background: #0c46e8;
      color: #ffffffff;
}

//     .broadcast-message-action.action-renewal {
//       background: #ECCFFF;
//       color: #694382;
//     }
//     .broadcast-message-action.action-proposal {
//       background: #FFD8B2;
//       color: #805B36;
//     }