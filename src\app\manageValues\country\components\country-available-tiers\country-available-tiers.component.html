<p-toast></p-toast>
<div>
      <div class="mb-1">Upgrading Methods: </div>
      <a *ngIf="selectedMethods.length==0" style="color: red">Pick One At Least</a>
      <p-card>
            <div *ngFor="let method of CountryTierUpgradingMethods" class="field-checkbox">
                  <p-checkbox name="CountryTierUpgradingMethods" [value]="method.value" [(ngModel)]="selectedMethods"
                        (click)="checkUpgradingMethods(method)" inputId="{{ method.label }}">
                  </p-checkbox>
                  <label for="{{ method.label }}">{{ method.label }}</label>
            </div>
      </p-card>
      <br />
      <!--  Tiers -->
      <div class="mb-1">Customer Tiers: </div>
      <a *ngIf="TierStatusMessage!=''" style="color: red">{{TierStatusMessage}}</a>
      <p-card>
            <p-table [value]="Tiers" [tableStyle]="{ 'min-width': '50rem' }"
                  styleClass="p-datatable-gridlines p-datatable-sm">
                  <ng-template pTemplate="caption"> Tiers </ng-template>
                  <ng-template pTemplate="header">
                        <tr>
                              <th>Tier Name</th>
                              <th *ngIf="isPointsPerYear">Tier Points Per Year</th>
                              <th *ngIf="isPricePerYear">Tier Price Per Year</th>
                        </tr>
                  </ng-template>
                  <ng-template pTemplate="body" let-tier>
                        <tr [ngClass]="{'inactive-table-row': tier.Checked === false}">
                              <td><p-checkbox [binary]="true" inputId="binary" [(ngModel)]="tier.Checked"
                                          (click)="Subscription(tier)"></p-checkbox>
                                    <i class="pi pi-euro ms-3" style="color: slateblue"></i>
                                    {{ tier.EnName }}
                              </td>
                              <td *ngIf="isPointsPerYear"><p-inputNumber [min]="0" [readonly]="tier.Checked === false"
                                          id="PointsPerYear" [(ngModel)]="tier.PointsPerYear"
                                          class="p-inputtext-sm w-50" />
                              </td>
                              <td *ngIf="isPricePerYear">
                                    <p-inputNumber [readonly]="tier.Checked === false" [min]="0"
                                          [suffix]="' ' + _CountryData.Currency" [useGrouping]="false"
                                          class="p-inputtext-sm w-50" id="FeePerYear" [(ngModel)]="tier.PricePerYear" />
                              </td>
                        </tr>
                  </ng-template>
            </p-table>
      </p-card>
      <p-card>
            <div class="flex flex-column gap-2 mb-3">
                  <label class="mb-1">Tiers Discount</label>
                  <p-inputSwitch [ngModelOptions]="{ standalone: true }"
                        [(ngModel)]="_CountryData.IsTierDiscountEnabled"
                        (onChange)="onChangeIsTierDiscountEnabled()"></p-inputSwitch>
                  <!-- <span>{{ isTierDiscount ? 'On' : 'Off' }}</span> -->
                  <a *ngIf="_CountryData.IsTierDiscountEnabled && !_CountryData.TierDiscountValue"
                        style="color: red">Please Insert Tiers Discount Value </a>
                  <div *ngIf="_CountryData.IsTierDiscountEnabled">


                        <label class="mb-1">Value:</label>
                        <p-inputNumber id="TierDiscountValue" [min]="0" [(ngModel)]="_CountryData.TierDiscountValue"
                              class="p-inputtext-sm w-50" class="p-inputtext-sm w-50" />
                  </div>

            </div>
      </p-card>
      <div class="row">
            <div class="flex justify-content-end gap-2 my-3">
                  <p-button label="back" styleClass="p-button-outlined p-button-secondary" (click)="back()"></p-button>
                  <p-button label="Next" (click)="next()"></p-button>
            </div>
      </div>

</div>