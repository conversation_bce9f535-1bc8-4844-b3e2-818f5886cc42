import { Component, Input, OnInit, ViewChild } from "@angular/core";
import { UserModel } from "src/app/Model/UserModel";
import { MessageService, ConfirmationService } from "primeng/api";
import { Router } from "@angular/router";
import { ConfirmationDialogComponent } from "src/app/components/confirmation-dialog/confirmation-dialog.component";
import { Country } from "src/app/Model/Country";
import { CountryService } from "src/app/services/country.service";
import { Countries } from "src/app/utilities/countries";

@Component({
    selector: "app-countrylist",
    templateUrl: "./countrylist.component.html",
    styleUrls: ["./countrylist.component.scss"],
    providers: [MessageService, ConfirmationService],
})
export class CountrylistComponent implements OnInit {
    @Input() _Country: Country;
    Countries?: Country[];
    Country: Country = new Country();
    loading: boolean = true;
    formData: Country;
    title: string;

    warningMessage: string = '';
    @ViewChild('confirmationDialog') confirmationDialog: ConfirmationDialogComponent;
    confirmationAction: string = '';
    OverrideWarning: boolean = false;


    totalRecords: number = 0;
    pageSize: number = 10;
    pageIndex: number = 0;
    paginator: boolean = false;

    //filters
    Name: string = '';

    //  track the first load
    private isInitialLoad: boolean = false;

    allCountries: Countries = new Countries();

    constructor(
        private CountryService: CountryService,
        private router: Router,
        private messageService: MessageService,) { }

    ngOnInit(): void {
        this.loadData({ first: 0, rows: this.pageSize });
        this.isInitialLoad = true;
        this.title = "Countries Management";
    }

    loadData(event: any) {
        // Avoid double loading during the first render
        if (this.isInitialLoad) {
            this.isInitialLoad = false; // Set the flag to false after the first load
            return;
        }
        const pageNumber = event.first / event.rows + 1; // Calculate the page number
        const pageSize = event.rows; // Rows per page
        this.paginator = true;

        this.fetchCountriesData(pageNumber, pageSize);

    }

    fetchCountriesData(pageNumber, pageSize) {
        this.CountryService
            .getAllCountriesTable(pageNumber, pageSize,this.Name)
            .subscribe((data) => {
                this.Countries = data.ListResultContent;
                this.totalRecords = data.TotalRecords; // Set total records for pagination
                this.loading = false;
            });
    }

    edit(e: { Country?: Country; state: string }) {

        this.formData = e.Country;

        this.CountryService.getCountryById(e.Country.Id.toString()).subscribe((data) => {
            this.router.navigate(["country-edit"], {
                state: {
                    data: data,
                    command: "edit",
                },
            });
        });


    }
    ReceivedFilteredData(event) {
        this.Name = event.Name;
        this.fetchCountriesData(1, this.pageSize);

        this.Countries = event.ListResultContent;
        this.totalRecords = event.TotalRecords; // Set total records for pagination
        //this.paginator = false;

    }

    delete(Country) {
        this.Country = Country;
        if (Country.TotalCompanies || Country.TotalEndUsers) {
            this.messageService.add({ severity: 'error', summary: 'Error', detail: `Cannot delete this country. It has ${Country.TotalEndUsers} associated end users and It has ${Country.TotalCompanies} associated Companies.` });
            return;
        }
        this.confirmationDialog.message = 'Do you want to delete this Country ' + this.Country.EnName;
        this.confirmationDialog.item = this.Country;
        this.confirmationDialog.openDialog();
        this.confirmationAction = 'delete';
    }

    // Method to handle the confirmation result
    handleConfirmationAction(result: boolean): void {
        if (this.confirmationAction == 'delete') {
            if (result) {
                this.CountryService.DeleteCountry(this.Country.Id).subscribe((data) => {
                    if (data['HasError'] == false) {
                        this.messageService.add({
                            severity: "success",
                            summary: "Success Message",
                            detail: "Delete Country Successfully",
                        });
                        // Update the data array (remove the deleted item)
                        this.Countries = this.Countries.filter(currentItem => currentItem.Id !== this.Country.Id);
                        this.Country = new Country();
                        this.confirmationAction = '';

                    } else {
                        this.confirmationDialog.item = this.Country;
                        this.confirmationDialog.openDialog();
                        this.confirmationDialog.message = data['EnErrorMessage'];
                    }
                });
            } else {
                // console.log('Cancelled!');
                this.confirmationAction = '';
                this.Country = new Country();
            }

        }

    }
}
