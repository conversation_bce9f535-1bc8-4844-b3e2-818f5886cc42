import { Component, ViewChild } from '@angular/core';
import { Message, MessageService } from 'primeng/api';
import { ActivatedRoute, Router } from '@angular/router';
import { SharedDataComponentService } from 'src/app/services/shared-data-component.service';
import { MallMainInfoComponent } from '../components/mall-main-info/mall-main-info.component';
import { MallLinkedComapniesComponent } from '../components/mall-linked-comapnies/mall-linked-comapnies.component';
import { MallModel } from 'src/app/Model/MallModel';

@Component({
  selector: 'app-mall-new',
  templateUrl: './mall-new.component.html',
  styleUrls: ['./mall-new.component.scss'],

})
export class MallNewComponent {
  newMall: MallModel = new MallModel();
  routeState: any;
  activeIndex = 0;
  previousTabIndex: number = 0; // Track the previous tab index
  isEditing: boolean = false;
  title: string = 'Add New Mall';

  tabsDisabled: boolean = true;  // Control whether tabs are disabled or not
  msgs: Message[] = [];

  @ViewChild(MallMainInfoComponent) MallMainInfoComponent!: MallMainInfoComponent;
  @ViewChild(MallLinkedComapniesComponent) MallLinkedComapniesComponent!: MallLinkedComapniesComponent;

  constructor(private router: Router, private sharedDataComponentService: SharedDataComponentService, private messageService: MessageService, private route: ActivatedRoute) {
    if (this.router.getCurrentNavigation()?.extras.state) {

      this.routeState = this.router.getCurrentNavigation()?.extras.state;
      if (this.routeState) {
        this.newMall = this.routeState.data
          ? this.routeState.data
          : new MallModel();
        if (this.newMall.Id == "") {
          this.isEditing = false;
        }
        else {
          this.isEditing = true;
        }
      }
    }
    if (this.route.snapshot.url[this.route.snapshot.url.length - 1].path === 'mall-edit') {
      this.isEditing = true;
      this.title = 'Edit Mall';
      this.tabsDisabled = false;

    }

  }
  ngOnInit() {
    console.log('isEditing', this.isEditing, this.newMall)
    if (this.isEditing == true && this.newMall.Id == "") {
      this.router.navigate(['/malls']);
      return;
    }
  }
  onTabChange(event: any) {
  }

  // Method to handle child component event and disable tabs
  handleDisableTabs(disable: boolean) {
    this.tabsDisabled = disable;  // If true, disable all tabs

  }
}
