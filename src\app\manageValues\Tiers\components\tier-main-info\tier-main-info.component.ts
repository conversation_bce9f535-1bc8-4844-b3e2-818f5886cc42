
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { TiersModel } from 'src/app/Model/TiersModel';
import { TierService } from 'src/app/services/tier.service';

@Component({
  selector: 'tier-main-info',
  templateUrl: './tier-main-info.component.html',
  styleUrls: ['./tier-main-info.component.scss'],

})
export class TierMainInfoComponent {
  @Input() _TierData: TiersModel;
  @Input() isEditing: boolean;
  @Input() activeIndex: number;
  @Output() activeIndexChange: EventEmitter<number> = new EventEmitter();
  pressedSave = false;

  tierMainInfoForm = new FormGroup({
    EnName: new FormControl('', [Validators.required, Validators.minLength(3)]),
    ArName: new FormControl('', [Validators.required, Validators.minLength(3)]),
    Rank: new FormControl('', [Validators.required]),
    Color: new FormControl('', [Validators.required]),

  });

  constructor(private messageService: MessageService, private tierService: TierService, private router: Router) { }

  ngOnInit(): void { }

  save() {
    this.pressedSave = true;
    if (!this.tierMainInfoForm.valid) {
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Check your input' });
      return;
    }
    delete this._TierData.PointsPerYear;
    delete this._TierData.PricePerYear;
    delete this._TierData.TotalCountries;
    delete this._TierData.TotalEndUsers;
    delete this._TierData.TotalUsers;
    delete this._TierData.HasError;
    delete this._TierData.Checked;
    delete this._TierData.UserStatistics;
    delete this._TierData.disabled;

    if (this._TierData.Id) {
      this.tierService.EditTier(this._TierData).subscribe(data => {
        if (data.HasError == true) {
          return;
        } else {
          this.router.navigate(['tiers']);
        }
      });
    }
    else {
      this.tierService.AddTier(this._TierData).subscribe((data) => {
        if (data.HasError == true) {
          return;
        }
        else {
          this.router.navigate(['tiers']);
        }
      })
    }
  }

  CheckValid(input: FormControl) {
    if (input.invalid && (this.pressedSave || input.touched)) {
      return 'red';
    }
    return '#515C66';
  }
}
