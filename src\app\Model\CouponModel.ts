
import { CouponStatusEnum } from "../enum/coupon-status-enum";
import { DiscountValueEnum } from "../enum/discount-value-enum";
import { ProtectionType } from "../enum/protection-type";
import { CompanyBranchModel } from "./CompanyBranchModel";
import { CompanyModel } from "./CompanyModel";
import { ConditionModel } from "./ConditionModel";
import { CouponLog } from "./CoupontLog";
import { CouponStatus } from "./CoupontStatus";
import { FilterModel } from "./FiltersModel";

export class CouponModel {
      constructor() {
      }
      Id: string = "";
      UserFriendly: number = null;
      EnTitle: string = "";
      ArTitle: string = "";
      CouponValue: DiscountValueEnum = 0; //B1G1 by default 
      EstimatedSaving: number = 0;
      OriginalPrice: number = 0;
      //Image
      ImageUrl: string = "";
      image: File;
      Company?: CompanyModel;
      SwipeCode: string = "";
      ProtectionType: ProtectionType;
      EnTerms: string = "";
      ArTerms: string = "";
      StartDate: Date;
      EndDate: Date;
      UpdatedDate?: Date;
      TargetedUserNum?: number = null;
      CouponBranches: CompanyBranchModel[] = [];
      CouponFilters: FilterModel[] = [];
      CouponConditions: ConditionModel[] = [];
      Active: boolean = false;
      CouponStatuses?: CouponStatus[]; // Not used
      CouponLog?: CouponLog[];
      Status: CouponStatusEnum;
      CompanyId?: string;
      Value: number = 0.0;
      NoteCoupon?: string = "";
      //region NotMapped


      VisibleDialog = false;
      //


}
