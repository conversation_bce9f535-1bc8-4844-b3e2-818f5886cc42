import { CompanyModel } from "./CompanyModel";
import { CityModel } from "./CityModel";
import { CompanyBranchFilterModel } from "./CompanyBranchFilterModel";
import { CompanyBranchWorkHours } from "./CompanyBranchWorkHours";
import { Country } from "./Country";
import { MallModel } from "./MallModel";
import { WeekDay } from "@angular/common";
import { PhoneNumberModel } from "./PhoneNumberModel";



export class CompanyBranchModel {
    /**
     *
     */
    constructor() {
        // this.Location = new LocationModel();
        // this.Company = new CompanyModel();
    }
    Id: string;
    EnName: string;
    ArName: string;
    WorkDays: string;
    WorkHours: string;
    PhoneNumber: PhoneNumberModel;
    CountryName: string;
    CityName: string;
    Region: string;
    Longitude: number;
    Latitude: number;
    GoogleMapLink: string = "";
    ServiceListUrl: string = "";
    SameDay: boolean = false;
    Active: boolean = true;
    IsMainBranch: boolean = false;
    IsOnline: boolean = false;


    //NotMapped
    RowId: number = 0;
    Country?: Country = null;
    Company?: CompanyModel;
    //Filter?: FilterModel[];
    //Relation

    City?: CityModel = null;
    Mall?: MallModel = null;

    CompanyBranchFilters?: CompanyBranchFilterModel[] = [];
    CompanyBranchWorkHours?: CompanyBranchWorkHours[] = [];


    initCompanyBranchWorkHours() {

        this.CompanyBranchWorkHours = [
            { Day: WeekDay.Sunday, DayChar: 'S', OpenTime: '', CloseTime: '', OpenTime2: '', CloseTime2: '', Active: true, IsSplite: false, CompanyBranchId: "" },
            { Day: WeekDay.Monday, DayChar: 'M', OpenTime: '', CloseTime: '', OpenTime2: '', CloseTime2: '', Active: false, IsSplite: false, CompanyBranchId: "" },
            { Day: WeekDay.Tuesday, DayChar: 'T', OpenTime: '', CloseTime: '', OpenTime2: '', CloseTime2: '', Active: false, IsSplite: false, CompanyBranchId: "" },
            { Day: WeekDay.Wednesday, DayChar: 'W', OpenTime: '', CloseTime: '', OpenTime2: '', CloseTime2: '', Active: false, IsSplite: false, CompanyBranchId: "" },
            { Day: WeekDay.Thursday, DayChar: 'T', OpenTime: '', CloseTime: '', OpenTime2: '', CloseTime2: '', Active: false, IsSplite: false, CompanyBranchId: "" },
            { Day: WeekDay.Friday, DayChar: 'F', OpenTime: '', CloseTime: '', OpenTime2: '', CloseTime2: '', Active: false, IsSplite: false, CompanyBranchId: "" },
            { Day: WeekDay.Saturday, DayChar: 'S', OpenTime: '', CloseTime: '', OpenTime2: '', CloseTime2: '', Active: false, IsSplite: false, CompanyBranchId: "" },
        ];
    }
}