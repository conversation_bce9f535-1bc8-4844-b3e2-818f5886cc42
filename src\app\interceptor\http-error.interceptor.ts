import { Injectable } from '@angular/core';
import {
  HttpInterceptor,
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpErrorResponse,
  HttpResponse,
} from '@angular/common/http';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { catchError, filter, map, switchMap, take, tap } from 'rxjs/operators';
import { HandleErrorService } from '../services/handle-error.service';
import { MessageService } from 'primeng/api';
import { Router } from '@angular/router';
import { AuthService } from '../services/auth.service';


@Injectable()
export class ErrorInterceptor implements HttpInterceptor {

  private isRefreshing = false; // Flag to prevent multiple token refresh attempts
  private refreshTokenSubject: BehaviorSubject<string | null> = new BehaviorSubject<string | null>(null);

  constructor(private handleErrorService: HandleErrorService, private messageService: MessageService, private router: Router, private authService: AuthService,) { }





  intercept(req: HttpRequest<any>, next: HttpHand<PERSON>): Observable<HttpEvent<any>> {
    return next.handle(req).pipe(
      tap((event) => {
        if (event instanceof HttpResponse && event.body) {
          const { HasError, EnErrorMessage } = event.body;
          if (HasError && EnErrorMessage) {
            this.messageService.add({ severity: 'error', summary: 'Error', detail: EnErrorMessage });
          }
        }
      }),
      catchError((error: HttpErrorResponse) => {
        console.log('ErrorInterceptor', error)
        if (error.status === 401) {
          // Handle 401 errors by refreshing the token
          return this.handle401Error(req, next);
        } else if (error.status === 403) {
          // Navigate to the "No Access" route
          this.router.navigate(['/access']);
        } else {
          // Handle other errors
          this.handleErrorService.handleError(error); // General error handling
        }
        return throwError(() => error);
      })
    );
  }

  private handle401Error(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    if (!this.isRefreshing) {
      this.isRefreshing = true;
      this.refreshTokenSubject.next(null);

      return this.authService.refreshAccessToken().pipe(
        switchMap(() => {
          const newToken = this.authService.getAccessToken();
          // Clone the original request and set the new access token
          const clonedRequest = request.clone({
            setHeaders: { Authorization: `Bearer ${newToken}` }
          });
          return next.handle(clonedRequest);
        }),
        catchError((err) => {
          this.isRefreshing = false;
          // Handle refresh token failure (e.g., redirect to login)
          this.authService.logout();
          return throwError(() => err);
        })
      );
    } else {
      // If a refresh is already in progress, wait until it completes
      return this.refreshTokenSubject.pipe(
        filter(token => token != null),
        take(1),
        switchMap((newToken) => {
          const clonedRequest = request.clone({
            setHeaders: { Authorization: `Bearer ${newToken}` }
          });
          return next.handle(clonedRequest);
        })
      );
    }
  }
}
