import { Component, ViewChild } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { SharedDataComponentService } from "src/app/services/shared-data-component.service";
import { Message, MessageService } from "primeng/api";
import { Country } from "src/app/Model/Country";
import { CountryAvailableTiersComponent } from "../components/country-available-tiers/country-available-tiers.component";
import { CountryMainInfoComponent } from "../components/main-info/country-main-info.component";
import { CountryAvailableCategoriesComponent } from "../components/country-available-categories/country-available-categories.component";
import { CountryCitiesComponent } from "../components/country-cities/country-cities.component";
import { CountrySubscriptionsPricesComponent } from "../components/country-subscriptions-prices/country-subscriptions-prices.component";

@Component({
  selector: "app-country-new",
  templateUrl: "./country-new.component.html",
  styleUrls: ["./country-new.component.scss"],

})
export class CountryNewComponent {
  newCountry: Country = new Country();
  routeState: any;
  activeIndex = 0;
  previousTabIndex: number = 0; // Track the previous tab index
  isEditing: boolean = false;
  title: string = 'Add New Country';

  tabsDisabled: boolean = true;  // Control whether tabs are disabled or not
  msgs: Message[] = [];

  @ViewChild(CountryMainInfoComponent) CountryMainInfoComponent!: CountryMainInfoComponent;
  @ViewChild(CountryAvailableTiersComponent) CountryAvailableTiersComponent!: CountryAvailableTiersComponent;
  @ViewChild(CountryAvailableCategoriesComponent) CountryAvailableCategoriesComponent!: CountryAvailableCategoriesComponent;
  @ViewChild(CountryCitiesComponent) CountryCitiesComponent!: CountryCitiesComponent;
  @ViewChild(CountrySubscriptionsPricesComponent) CountrySubscriptionsPricesComponent!: CountrySubscriptionsPricesComponent;
  constructor(private router: Router, private sharedDataComponentService: SharedDataComponentService, private messageService: MessageService, private route: ActivatedRoute) {
    if (this.router.getCurrentNavigation()?.extras.state) {

      this.routeState = this.router.getCurrentNavigation()?.extras.state;
      if (this.routeState) {
        this.newCountry = this.routeState.data
          ? this.routeState.data
          : new Country();
        if (this.newCountry.Id == "") {
          this.isEditing = false;
        }
        else {
          this.isEditing = true;
        }
      }
    }
    if (this.route.snapshot.url[this.route.snapshot.url.length - 1].path === 'country-edit') {
      this.isEditing = true;
      this.title = 'Edit Country';
      this.tabsDisabled = false;

    }

  }
  ngOnInit() {
    if (this.isEditing == true && this.newCountry.Id == "") {
      this.router.navigate(['/countries']);
      return;
    }
  }
  onTabChange(event: any) {

    const newTabIndex = event.index; // Get the newly selected tab index
    if (newTabIndex !== 0) {
      // If Tab 1 is selected, trigger function in app-child-one-component
      this.CountryMainInfoComponent.next();

      if (this.newCountry.HasError == true) {
        this.msgs = [];
        this.msgs.push({ severity: 'error', summary: 'Error Message', detail: 'Please Check the Main Info Tab !' });
        return;
      }
    }
    if (newTabIndex !== 1) {

      this.CountryCitiesComponent.next();
      if (this.newCountry.HasError == true) {
        this.msgs = [];
        this.msgs.push({ severity: 'error', summary: 'Error Message', detail: 'Please Check the Cities Tab !' });
        return;
      }
    }
    if (newTabIndex !== 2) {

      this.CountryAvailableCategoriesComponent.next();
      if (this.newCountry.HasError == true) {
        this.msgs = [];
        this.msgs.push({ severity: 'error', summary: 'Error Message', detail: 'Please Check the Available Categories Tab !' });
        return;
      }
    }
    if (newTabIndex !== 3) {
      this.CountryAvailableTiersComponent.next();
      if (this.newCountry.HasError == true) {
        this.msgs = [];
        this.msgs.push({ severity: 'error', summary: 'Error Message', detail: 'Please Check the Available Tiers & Prices Tab !' });
        return;
      }
    }

    // this.previousTabIndex = newTabIndex;

  }

  // Method to handle child component event and disable tabs
  handleDisableTabs(disable: boolean) {
    this.tabsDisabled = disable;  // If true, disable all tabs

  }
}
