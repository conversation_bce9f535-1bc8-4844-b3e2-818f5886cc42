<h3>
      <p-button icon="pi pi-arrow-left" styleClass="p-button-rounded p-button-secondary p-button-text"
            routerLink="/website-admins-management">
      </p-button>
      {{title}}
</h3>
<div class="grid p-fluid">
      <div class="col-12 md:col-6">
            <p-card class="container">
                  <div class="row" [formGroup]="websiteAdminForm">
                        <div class="col-md-1" style="margin-right: 9%;">
                              <div class="text-center">
                                    <div class="image-avatar-container">
                                          <label for="image" class="w-100">
                                                <img [src]="
                                                      ProfilePhotoUrl| placeholder: 'assets/layout/images/upload-image.png' "
                                                      class="roundeisSelectedFiled-circle w-100" alt="..." />
                                                <p-button icon="pi pi-pencil" class="edit-button"
                                                      styleClass="p-button-rounded" (click)="file.click()"></p-button>
                                          </label>
                                          <input type="file" id="ProfilePhotoUrl" class="d-none"
                                                (change)="uploadFile(file.files)" accept="image/*" #file
                                                formControlName="ProfilePhotoUrl" />
                                    </div>
                                    <br>
                                    <label *ngIf="pressedSave && !this.ProfilePhotoUrl" for="logoImage"
                                          style="color: red">Please
                                          upload
                                          Profile Photo</label>
                              </div>
                        </div>
                        <div class="col-md-8">
                              <div class="flex flex-column gap-3 mb-4">
                                    <label for="Name" [style.color]="CheckValid(websiteAdminForm.controls.Name)">Admin
                                          Name</label>
                                    <input id="Name" type="text" pInputText placeholder="Name"
                                          [(ngModel)]="websiteAdmin.Name" class="p-inputtext-sm w-50"
                                          formControlName="Name" />

                              </div>
                              <div class="flex flex-column gap-2 mb-4">
                                    <label for="Email" [style.color]="CheckValid(websiteAdminForm.controls.Email)">Admin
                                          Email</label>
                                    <!-- [Email]="true" -->
                                    <input id="Email" (focusout)="onEmailInputFocusOut()" type="Email" pInputText
                                          placeholder="Admin Email" [(ngModel)]="websiteAdmin.Email"
                                          class="p-inputtext-sm w-50" formControlName="Email" #EmailInput />
                                    <span *ngIf="websiteAdminForm.get('Email').hasError('invalidEmailError')"
                                          [style.color]="CheckValid(websiteAdminForm.controls.Email)">
                                          Please enter a valid Email .
                                    </span>
                                    <!-- autocomplete='false' -->
                                    <!-- </form> -->
                              </div>
                              <div class="flex flex-column gap-2 mb-4">
                                    <label *ngIf="!isEditing" for="adminPassword"
                                          [style.color]="CheckValid(websiteAdminForm.controls.Password)">Admin
                                          Password
                                    </label>
                                    <label *ngIf="isEditing" for="adminPassword">Admin Password
                                    </label>
                                    <p-password [(ngModel)]="websiteAdmin.Password" class="p-inputtext-sm w-50"
                                          [toggleMask]="true" formControlName="Password" id="adminPassword"
                                          placeholder="Admin Password" #PasswordInput></p-password>
                                    <span *ngIf="websiteAdminForm.get('Password').hasError('pattern')"
                                          [style.color]="CheckValid(websiteAdminForm.controls.Password)">
                                          Password must contain at least one lowercase letter, one uppercase
                                          letter, one
                                          digit, and one special character ($@$!%*?&), and be at least 8
                                          characters
                                          long.
                                    </span>
                              </div>
                              <div class="flex flex-column gap-2 mb-4">
                                    <label for="type"
                                          [style.color]="CheckValid(websiteAdminForm.controls.AdminType)">Admin
                                          Type</label>
                                    <p-dropdown [options]="adminTypes" [(ngModel)]="websiteAdmin.AdminType"
                                          class="p-inputtext-sm w-50" placeholder="select Admin Type"
                                          formControlName="AdminType"></p-dropdown>
                              </div>

                              <div class="flex flex-column gap-2 mb-4" *ngIf="websiteAdmin.AdminType == 1">
                                    <label [style.color]="CheckValid(websiteAdminForm.controls.AdministeredCountries)"
                                          for="AdministeredCountries">Administered Countries</label>
                                    <p-multiSelect selectedItemsLabel="No. of Selected Countries: {0}"
                                          [options]="countries" [(ngModel)]="websiteAdmin.AdministeredCountries"
                                          defaultLabel="Select Countries" optionLabel="EnName"
                                          id="AdministeredCountries" formControlName="AdministeredCountries"
                                          class="multiselect-custom p-inputtext-sm w-50">
                                    </p-multiSelect>
                              </div>
                              <div class="flex flex-column gap-2 mb-4">
                                    <label for="MaxServiceDiscount" [style.color]="CheckValid( websiteAdminForm.controls.MaxServiceDiscount)
                                                ">Max Servic Discount</label>
                                    <p-inputNumber [useGrouping]="false" id="MaxServiceDiscount"
                                          [(ngModel)]="websiteAdmin.MaxServiceDiscount"
                                          formControlName="MaxServiceDiscount" mode="decimal" suffix="%"
                                          class="p-inputtext-sm w-50" [min]="0" [max]="100" />
                              </div>
                              <div class="flex flex-column gap-2 mb-4" *ngIf="websiteAdmin.AdminType == 1">
                                    <label for="AdministeredPages"
                                          [style.color]="CheckValid( websiteAdminForm.controls.AdministeredPages)">Administered
                                          Pages</label>
                                    <a *ngIf="websiteAdmin.AdministeredPages.length==0 && pressedSave && websiteAdmin.AdminType == 1"
                                          style="color: red">Pick One At Least</a>
                                    <p-card class="container">
                                          <div class="row">
                                                <div class="col-md-6" *ngFor="let column of columns">
                                                      <div *ngFor="let page of column" class="field-checkbox">
                                                            <p-checkbox [value]="page?.value" inputId="{{page?.name}}"
                                                                  [(ngModel)]="websiteAdmin.AdministeredPages"
                                                                  formControlName="AdministeredPages"
                                                                  [ngModelOptions]="{standalone: true}">
                                                            </p-checkbox>
                                                            &nbsp;
                                                            <i [class]="WebsiteAdminPageEnumIconMap[page?.value]"></i>
                                                            <label for="{{page?.name}}">{{page?.name}}</label>
                                                      </div>
                                                </div>
                                          </div>
                                    </p-card>
                              </div>
                        </div>

                        <br />


                        <br />
                        <div class="flex justify-content-end gap-2 mb-3">
                              <p-button label="save" (onClick)="save()"></p-button>
                              <!-- [disabled]="!this.timingDetailsForm.valid" -->
                        </div>
                  </div>
            </p-card>
      </div>
</div>