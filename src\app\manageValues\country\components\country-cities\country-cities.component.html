<p-toast></p-toast>
<div class="container" [formGroup]="cityForm">
    <div class="row">
        <h4 *ngIf="!enableEdit">Add new City</h4>
        <h4 *ngIf="enableEdit">Edit City</h4>

        <div class="row">
            <div class="col-4">
                <div class="flex flex-column gap-2 mb-3">
                    <label for="EnName" [style.color]="CheckValid(cityForm.controls.EnName)">City
                        English Name</label>

                    <input id="EnName" type="text" pInputText placeholder="City English Name" [(ngModel)]="_City.EnName"
                        class="p-inputtext-sm w-50" formControlName="EnName" />

                </div>
            </div>
            <div class="col-4">
                <div class="flex flex-column gap-2 mb-3">
                    <label for="ArName" [style.color]="CheckValid(cityForm.controls.ArName)">City
                        Arabic Name </label>
                    <input id="ArName" type="text" pInputText placeholder="City Arabic Name" [(ngModel)]="_City.ArName"
                        class="p-inputtext-sm w-50" formControlName="ArName" />
                </div>
            </div>
        </div>

        <div *ngIf="!enableEdit" class="col-8">
            <div class="flex justify-content-end gap-2 mb-3">
                <p-button label="add" (onClick)="addToCities()"></p-button>
            </div>
        </div>
        <div *ngIf="enableEdit" class="col-8">
            <div class="flex justify-content-end gap-2 mb-3">
                <p-button label="save" (onClick)="editCity()"></p-button>
            </div>
        </div>
    </div>

    <div class="row">
        <p-table [value]="_CountryData.Cities" [tableStyle]="{ 'min-width': '50rem' }"
            styleClass="p-datatable-gridlines p-datatable-sm">
            <ng-template pTemplate="caption"> Cities </ng-template>
            <ng-template pTemplate="header">
                <tr>
                    <th>English Name</th>
                    <th>Arabic Name</th>
                    <th>Total Malls</th>
                    <th>Total Branches</th>
                    <th></th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-City>
                <tr>

                    <td>{{ City.EnName }}</td>
                    <td>{{ City.ArName }}</td>
                    <td>{{ City.MallsNum }}</td>
                    <td>{{ City.CompanyBranchesNum }}</td>
                    <td>
                        <p-button icon="pi pi-pencil" styleClass="p-button-rounded" class="mx-1"
                            (click)="showSelectedcity(City)"></p-button>
                        <!-- <p-button icon="pi pi-trash" styleClass="p-button-rounded p-button-danger p-button-text"></p-button> -->
                        <p-button icon="pi pi-trash" styleClass="p-button-rounded p-button-danger"
                            (click)="deleteSelectedCity(City)" class="mx-1"></p-button>

                    </td>

                </tr>
            </ng-template>

        </p-table>
        <!-- Reusable Confirmation Dialog Component -->
        <app-confirmation-dialog #confirmationDialog
            (confirmResult)="handleConfirmationAction($event)"></app-confirmation-dialog>


        <div class="flex justify-content-end gap-2 my-3">
            <p-button label="back" styleClass="p-button-outlined p-button-secondary" (click)="back()"></p-button>
            <p-button label="Next" (click)="next()"></p-button>
        </div>
    </div>
</div>