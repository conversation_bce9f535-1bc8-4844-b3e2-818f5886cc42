import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { AuthService } from './auth.service';
import { Dto } from '../Model/Dto';
import { EndUserModel } from '../Model/EndUserModel';
import { environment } from 'src/environments/environment';
import { catchError, map, of } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class EndUsersService {

  constructor(private httpClient: HttpClient, private authService: AuthService) {

  }

  GetAllEndUsers(page: number = null, pageSize: number = null, Name: string = null, Gender: string = null, Residance: string = null, TierId: string = null) {
    let params = new HttpParams();

    if (page) {
      params = params.set('PageNumber', page);
    }
    if (pageSize) {
      params = params.set('PageSize', pageSize);
    }
    if (Name) {
      params = params.set('Name', Name);
    }
    if (Gender!=null) {
      params = params.set('Gender', Gender);
    }
    if (Residance) {
      params = params.set('Residance', Residance);
    }
    if (TierId) {
      params = params.set('TierId', TierId);
    }
    return this.httpClient.get<Dto<EndUserModel>>(`${environment.apiUrl}` + 'EndUser/GetAllEndUsers', { params })
      .pipe(
        map((res: any) => {
          if (!res.HasError) {
            return res;
          }
        })
      );
  }
  MakeEndUserVipUser(data) {
    var http;
    var url = `${environment.apiUrl}EndUser/MakeEndUserVipUser`;
    http = this.httpClient.put(url, data);
    return http.pipe(
      map((res) => { return res; }), catchError(error => { return of(false); }));

  }
}
