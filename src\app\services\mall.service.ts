import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { Dto } from '../Model/Dto';
import { MallModel } from '../Model/MallModel';
import { catchError, map, Observable, of } from 'rxjs';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class MallService {


  constructor(private httpClient: HttpClient, private authService: AuthService) {
  }

  GetAllMalls(fullView = false, page: number = null, pageSize: number = null, mallName: string = "", location: string = null) {
    let params = new HttpParams();
    params = params.set('FullView', fullView);
    if (page) {
      params = params.set('PageNumber', page);
    }
    if (pageSize) {
      params = params.set('PageSize', pageSize);
    }
    if (mallName) {
      params = params.set('Name', mallName);
    }
    if (location) {
      params = params.set('Location', location);
    }
    return this.httpClient.get<Dto<MallModel>>(`${environment.apiUrl}` + 'Mall/GetAll', { params })
      .pipe(
        map((res: any) => {
          return res;
        })
      );
  }

  getMallById(Id: string = "") {
    return this.httpClient.get<Dto<MallModel>>(`${environment.apiUrl}Mall/GetMallById?id=${Id}`)
      .pipe(
        map((res: any) => {
          return res['ResultContent'];
        })
      );
  }

  public DeleteMall(mallId: String): Observable<any> {
    var http;
    var url = `${environment.apiUrl}Mall/DeleteMall?id=${mallId}`;
    http = this.httpClient.delete(url);
    return http.pipe(
      map((res: any) => {
        return res;
      }),
      catchError((error) => {

        return of(false);

      })
    );
  }


  public AddMall(data): Observable<Dto<MallModel>> {
    var http;
    var url = `${environment.apiUrl}Mall/AddMall`;
    http = this.httpClient.post(url, data);
    return http.pipe(
      map((res: Dto<MallModel>) => {
        return res;
      }),
      catchError(error => {
        return of(false);
      }));
  }

  public EditMall(data): Observable<Dto<MallModel>> {
    var http;
    var url = `${environment.apiUrl}Mall/EditMall`;
    http = this.httpClient.put(url, data);
    return http.pipe(
      map((res: Dto<MallModel>) => {
        return res;
      }),
      catchError(error => {

        return of(false);
      }));
  }
}
