import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, catchError, map, Observable, of } from 'rxjs';
import { environment } from 'src/environments/environment';
import { Country } from '../Model/Country';
import { Dto } from '../Model/Dto';
@Injectable({
  providedIn: 'root'
})
export class CountryService {
  public CountriesDataSource = new BehaviorSubject<Array<Country>>([]);
  public Countries = this.CountriesDataSource.asObservable();
  constructor(private httpClient: HttpClient) { }

  getCountries() {
    return this.httpClient.get<Dto<Country>>(`${environment.apiUrl}` + 'Country/getAll')
      .pipe(
        map((res: Dto<Country>) => {
          this.CountriesDataSource.next(res.ListResultContent);
          return res.ListResultContent;
        })
      );
  }

  getAllCountriesTable(page: number = null, pageSize: number = null, Name: string = null) {
    var http;
    var url = `${environment.apiUrl}Country/getAllAdminTable`;
    // var params = {};
    // if (page != null) {
    let params = new HttpParams();;
    if (page) {
      params = params.set('PageNumber', page);
    }
    if (pageSize) {
      params = params.set('PageSize', pageSize);
    }
    if (Name) {
      params = params.set('Name', Name);
    }
    http = this.httpClient.get(url, { params });
    return http.pipe(
      map((res: Dto<Country>) => {
        if (res.HasError) {
          return of(false);
        } else {
          return res;
        }
      }),
      catchError((error) => {

        return of(false);
      })
    );
  }
  getCountryById(Id: string = "") {
    return this.httpClient.get<Dto<Country>>(`${environment.apiUrl}Country/GetCountryById?Id=${Id}`)
      .pipe(
        map((res: any) => {

          return res['ResultContent'];
        })
      );
  }
  public AddCountry(data): Observable<Dto<Country>> {
    var http;
    var url = `${environment.apiUrl}Country/AddCountry`;
    http = this.httpClient.post(url, data);
    return http.pipe(
      map((res: Dto<Country>) => {
        return res;
      }),
      catchError(error => {

        return of(false);
      }));
  }
  public EditCountry(data): Observable<Dto<Country>> {
    var http;
    var url = `${environment.apiUrl}Country/EditCountry`;
    http = this.httpClient.put(url, data);
    return http.pipe(
      map((res: Dto<Country>) => {
        return res;
      }),
      catchError(error => {

        return of(false);
      }));
  }
  public DeleteCountry(CountryId: String): Observable<any> {
    var http;
    var url = `${environment.apiUrl}Country/DeleteCountry?Id=${CountryId}`;
    http = this.httpClient.delete(url);
    return http.pipe(
      map((res: any) => {
        return res;
      }),
      catchError((error) => {

        return of(false);
      })
    );
  }
}
