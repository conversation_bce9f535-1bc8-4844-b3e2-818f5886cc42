import { AbstractControl, ValidationErrors } from '@angular/forms';

export class EmailValidator {

      static isValidEmailFormat(control: AbstractControl): ValidationErrors | null {
            let EMAIL_REGEXP = /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$/i;
            // console.log('email', control.value);
            //if (control.value != "" && (control.value.length <= 5 || !EMAIL_REGEXP.test(control.value)))
            if (control.value != "" && control.value !== null && control.value != undefined) {
                  if (control.value.length <= 5 || !EMAIL_REGEXP.test(control.value)) {
                        return { 'invalidEmailError': 'validationMessages.invalidEmail' };
                  }
            }

            return null;
      }

}