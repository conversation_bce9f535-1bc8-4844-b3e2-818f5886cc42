<div class=" grid p-fluid col-12 md:col-6">
      <div  *ngIf="user.UserType !== 1" class="col-3 flex flex-column gap-2 mb-3">
            <label for="companies"> Viewed by Company</label>
            <p-dropdown [options]="companies" [showClear]="true" [(ngModel)]="Company" defaultLabel="Select Company"
                  placeholder="Select Company" optionLabel="EnName" (onChange)="changeCompany()"
                  display="chip"></p-dropdown>
      </div>
      <div class="col-3 flex flex-column gap-2 mb-3">
            <label for="years"> Viewed by Year</label>
            <p-dropdown [options]="years" [showClear]="true" [(ngModel)]="Year" defaultLabel="Select year"
                  placeholder="Select Year" (onChange)="changeYear()" display="chip"></p-dropdown>
      </div>
</div>

<loader [isVisible]="loading"></loader>

<div class="layout-dashboard" *ngIf="!loading">

      <div class="row">
            <div class="col-md-3">
                  <div class="card overview-box blue" [class.selected]="selectedCard === 'discountRedemption'"
                        (click)="selectCard('discountRedemption')">
                        <div class="card-header">
                              <h6 [class.selected]="selectedCard === 'discountRedemption'">Discounts Redemption
                                    Count
                              </h6>

                        </div>
                        <div class="overview-content">
                              <h3>{{DiscountRedemptionWeeklyStatistics.Total}}</h3>
                              <div class="overview-graph">
                                    <span *ngFor="let value of DiscountRedemptionWeeklyStatistics.WeekData"
                                          [style.height]="value + 'px'">
                                          <span class="graph-tooltip">{{ value }}</span>
                                    </span>
                              </div>
                        </div>
                        <div class="overview-footer">
                              <span><i class="pi pi-arrow-up"></i>
                                    <span>{{DiscountRedemptionWeeklyStatistics.ChangePercentage}}</span>
                                    increase in total count</span>
                        </div>
                  </div>
            </div>
            <div class="col-md-3">
                  <div class="card overview-box blue" [class.selected]="selectedCard === 'discounts'"
                        (click)="selectCard('discounts')">
                        <div class="card-header">
                              <h6 [class.selected]="selectedCard === 'discounts'">Discounts</h6>

                        </div>
                        <div class="overview-content">
                              <h3>{{DiscountWeeklyStatistics.Total}}</h3>
                              <div class="overview-graph">
                                    <span *ngFor="let value of DiscountWeeklyStatistics.WeekData"
                                          [style.height]="value + 'px'">
                                          <span class="graph-tooltip">{{ value }}</span>
                                    </span>
                              </div>
                        </div>
                        <div class="overview-footer">
                              <span><i class="pi pi-arrow-up"></i>
                                    <span>{{DiscountWeeklyStatistics.ChangePercentage}}</span> increase in total
                                    discounts</span>
                        </div>
                  </div>
            </div>
            <div class="col-md-3">
                  <div class="card overview-box blue" [class.selected]="selectedCard === 'discountsSalesEstimation'"
                        (click)="selectCard('discountsSalesEstimation')">
                        <div class="card-header">
                              <h6 [class.selected]="selectedCard === 'discountsSalesEstimation'">Discounts Sales
                                    Estimation
                              </h6>

                        </div>
                        <div class="overview-content">
                              <h3>{{DiscountSalesWeeklyStatistics.Total}}</h3>
                              <div class="overview-graph">
                                    <span *ngFor="let value of DiscountSalesWeeklyStatistics.WeekData"
                                          [style.height]="value + 'px'">
                                          <span class="graph-tooltip">{{ value }}</span>
                                    </span>
                              </div>
                        </div>
                        <div class="overview-footer">
                              <span><i class="pi pi-arrow-up"></i>
                                    <span>{{DiscountSalesWeeklyStatistics.ChangePercentage}}</span>
                                    increase in total sales</span>
                        </div>
                  </div>
            </div>
            <div class="col-md-3">
                  <div class="card overview-box blue" [class.selected]="selectedCard === 'couponsRedemption'"
                        (click)="selectCard('couponsRedemption')">
                        <div class="card-header">
                              <h6 [class.selected]="selectedCard === 'couponsRedemption'">Coupon Redemption Count</h6>

                        </div>
                        <div class="overview-content">
                              <h3>{{CouponRedemptionWeeklyStatistics?.Total}}</h3>
                              <div class="overview-graph">
                                    <span *ngFor="let value of CouponRedemptionWeeklyStatistics?.WeekData"
                                          [style.height]="value + 'px'">
                                          <span class="graph-tooltip">{{ value }}</span>
                                    </span>
                              </div>
                        </div>
                        <div class="overview-footer">
                              <span><i class="pi pi-arrow-up"></i>
                                    <span>{{CouponRedemptionWeeklyStatistics?.ChangePercentage}}</span> increase in
                                    total count
                              </span>
                        </div>
                  </div>
            </div>
      </div>
      &nbsp;
      <div class="row">
            <div class="col-md-3">
                  <div class="card overview-box blue" [class.selected]="selectedCard === 'couponsSalesEstimation'"
                        (click)="selectCard('couponsSalesEstimation')">
                        <div class="card-header">
                              <h6 [class.selected]="selectedCard === 'couponsSalesEstimation'">Coupons Sales Estimation
                              </h6>
                        </div>
                        <div class="overview-content">
                              <h3>{{CouponsSalesEstimationWeeklyStatistics.Total}}</h3>
                              <div class="overview-graph">
                                    <span *ngFor="let value of CouponsSalesEstimationWeeklyStatistics.WeekData"
                                          [style.height]="value + 'px'">
                                          <span class="graph-tooltip">{{ value }}</span>
                                    </span>
                              </div>
                        </div>
                        <div class="overview-footer">
                              <span><i class="pi pi-arrow-up"></i>
                                    <span>{{CouponsSalesEstimationWeeklyStatistics.ChangePercentage}}</span>
                                    increase in total sales</span>
                        </div>
                  </div>
            </div>

            <!-- <div class="col-md-3">
                  <div class="card overview-box blue" [class.selected]="selectedCard === 'broadcastMessages'"
                        (click)="selectCard('broadcastMessages')">
                        <div class="card-header">
                              <h6 [class.selected]="selectedCard === 'broadcastMessages'">Broadcast Messages Launches
                              </h6>

                        </div>
                        <div class="overview-content">
                              <h3>{{BroadcastMessagesWeeklyStatistics.Total}}</h3>
                              <div class="overview-graph">
                                    <span *ngFor="let value of BroadcastMessagesWeeklyStatistics.WeekData"
                                          [style.height]="value + 'px'">
                                          <span class="graph-tooltip">{{ value }}</span>
                                    </span>
                              </div>
                        </div>
                        <div class="overview-footer">
                              <span><i class="pi pi-arrow-up"></i>
                                    <span>{{BroadcastMessagesWeeklyStatistics.ChangePercentage}}</span> increase in
                                    total launches</span>
                        </div>
                  </div>
            </div>
            <div class="col-md-3">
                  <div class="card overview-box blue" [class.selected]="selectedCard === 'broadcastMessagesSent'"
                        (click)="selectCard('broadcastMessagesSent')">
                        <div class="card-header">
                              <h6 [class.selected]="selectedCard === 'broadcastMessagesSent'">Broadcast Messages Sent
                              </h6>

                        </div>
                        <div class="overview-content">
                              <h3>{{BroadcastMessagesSentWeeklyStatistics.Total}}</h3>
                              <div class="overview-graph">
                                    <span *ngFor="let value of BroadcastMessagesSentWeeklyStatistics.WeekData"
                                          [style.height]="value + 'px'">
                                          <span class="graph-tooltip">{{ value }}</span>
                                    </span>
                              </div>
                        </div>
                        <div class="overview-footer">
                              <span><i class="pi pi-arrow-up"></i>
                                    <span>{{BroadcastMessagesSentWeeklyStatistics.ChangePercentage}}</span> increase in
                                    total messages</span>
                        </div>
                  </div>
            </div> -->
      </div>
      &nbsp;

      <!-- discounts Charts -->
      <div class="row grid p-fluid justify-content-center" *ngIf="selectedCard === 'discounts'">
            <div class="col-6">
                  <div class="card">
                        <h5>Discounts Yearly Chart</h5>
                        <p-chart type="line" [data]="DiscountYearly" [options]="lineOptions"></p-chart>
                  </div>
            </div>
      </div>
      <!-- discount Redemption Charts -->
      <div class="row grid p-fluid justify-content-center" *ngIf="selectedCard === 'discountRedemption'">
            <div class="col-6">
                  <div class="card">
                        <h5>Discounts Redemption Yearly Chart</h5>
                        <p-chart type="line" [data]="DiscountRedemptionYearly" [options]="lineOptions"></p-chart>
                  </div>
            </div>
      </div>
      <!-- discountsSalesEstimation Charts -->
      <div class="row grid p-fluid justify-content-center" *ngIf="selectedCard === 'discountsSalesEstimation'">
            <div class="col-6">
                  <div class="card">
                        <h5>Discounts Sales Estimation Yearly Chart</h5>
                        <p-chart type="line" [data]="DiscountsSalesEstimationsYearly" [options]="lineOptions"></p-chart>
                  </div>
            </div>
      </div>
      <!-- coupons Charts -->
      <div class="row grid p-fluid justify-content-center" *ngIf="selectedCard === 'coupons'">
            <div class="col-6">
                  <div class="card">
                        <h5>Coupons Yearly Chart</h5>
                        <p-chart type="line" [data]="CouponsYearly" [options]="lineOptions"></p-chart>
                  </div>
            </div>
      </div>
      <!-- couponsRedemption Chart -->
      <div class="row grid p-fluid justify-content-center" *ngIf="selectedCard === 'couponsRedemption'">
            <div class="col-6">
                  <div class="card">
                        <h5>Coupons Redemption Yearly Chart</h5>
                        <p-chart type="line" [data]="CouponsRedemptionYearly" [options]="lineOptions"></p-chart>
                  </div>
            </div>
      </div>
      <!-- coupons Chart -->
      <div class="row grid p-fluid justify-content-center" *ngIf="selectedCard === 'coupons'">
            <div class="col-6">
                  <div class="card">
                        <h5>Coupons Yearly Chart</h5>
                        <p-chart type="line" [data]="CouponsYearly" [options]="lineOptions"></p-chart>
                  </div>
            </div>
      </div>
      <!-- couponsSalesEstimation Chart -->
      <div class="row grid p-fluid justify-content-center" *ngIf="selectedCard === 'couponsSalesEstimation'">
            <div class="col-6">
                  <div class="card">
                        <h5>Coupons Sales Estimation Yearly Chart</h5>
                        <p-chart type="line" [data]="CouponsSalesEstimationYearly" [options]="lineOptions"></p-chart>
                  </div>
            </div>
      </div>
      <!-- broadcastMessages Chart -->
      <div class="row grid p-fluid justify-content-center" *ngIf="selectedCard === 'broadcastMessages'">
            <div class="col-6">
                  <div class="card">
                        <h5>Broadcast Messages Yearly Chart</h5>
                        <p-chart type="line" [data]="BroadcastMessagesYearly" [options]="lineOptions"></p-chart>
                  </div>
            </div>
      </div>
      <!-- broadcastMessagesSent Chart -->
      <div class="row grid p-fluid justify-content-center" *ngIf="selectedCard === 'broadcastMessagesSent'">
            <div class="col-6">
                  <div class="card">
                        <h5>Broadcast Messages Sent Yearly Chart</h5>
                        <p-chart type="line" [data]="BroadcastMessagesSentYearly" [options]="lineOptions"></p-chart>
                  </div>
            </div>
      </div>
</div>