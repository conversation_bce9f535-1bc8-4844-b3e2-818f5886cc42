import { Component, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ConfirmationDialogComponent } from 'src/app/components/confirmation-dialog/confirmation-dialog.component';
import { UserModel } from 'src/app/Model/UserModel';
import { AuthService } from 'src/app/services/auth.service';
import { CompanyAdminService } from 'src/app/services/company-admin.service';
import { Countries } from 'src/app/utilities/countries';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'company-admins-list',
  templateUrl: './company-admins-list.component.html',
  styleUrls: ['./company-admins-list.component.scss'],
  providers: [MessageService, ConfirmationService]
})
export class CompanyAdminsListComponent {
  CompanyAdmins?: UserModel[];
  CompanyAdmin: UserModel = new UserModel();
  loading: boolean = true;
  formData: UserModel;
  title: string;
  warningMessage: string = '';
  @ViewChild('confirmationDialog') confirmationDialog: ConfirmationDialogComponent;
  confirmationAction: string = '';
  OverrideWarning: boolean = false;
  imageSrc: string = environment.imageSrc;
  totalRecords: number = 0;
  pageSize: number = 10;
  pageIndex: number = 0;
  paginator: boolean = false;

  //filters
  Name: string = '';
  adminType: any;
  CompanyId: any;

  //  track the first load
  private isInitialLoad: boolean = false;
  allCountries: Countries = new Countries();
  currentUser: UserModel;


  constructor(
    private CompanyAdminService: CompanyAdminService,
    private router: Router,
    private messageService: MessageService, private authService: AuthService) { }


  ngOnInit(): void {
    this.loadData({ first: 0, rows: this.pageSize });
    this.isInitialLoad = true;
    this.title = "Company Admins Management";
    this.currentUser = this.authService.getUserData();
  }

  loadData(event: any) {
    // Avoid double loading during the first render
    if (this.isInitialLoad) {
      this.isInitialLoad = false; // Set the flag to false after the first load
      return;
    }
    const pageNumber = event.first / event.rows + 1; // Calculate the page number
    const pageSize = event.rows; // Rows per page
    this.paginator = true;

    this.fetchCompanyAdminsData(pageNumber, pageSize);

  }

  fetchCompanyAdminsData(pageNumber, pageSize) {
    this.CompanyAdminService
      .GetAllCompanyAdmins(pageNumber, pageSize, this.Name, this.adminType, this.CompanyId)
      .subscribe((data) => {
        this.CompanyAdmins = data.ListResultContent;
        this.totalRecords = data.TotalRecords; // Set total records for pagination
        this.loading = false;
      });
  }

  edit(e: { CompanyAdmin?: UserModel; state: string }) {
    if (e.CompanyAdmin.Active == false) {
      this.messageService.add({
        severity: "warn",
        summary: "Warning",
        detail: "Please activate the Admin before Editing ! ",
      });

    } else {
      this.formData = e.CompanyAdmin;

      this.CompanyAdminService.getCompanyAdminById(e.CompanyAdmin.Id.toString()).subscribe((data) => {
        this.router.navigate(["company-admin-edit"], {
          state: {
            data: data,
            command: "edit",
          },
        });
      });
    }


  }
  ReceivedFilteredData(event) {
    this.Name = event.Name;
    this.adminType = event.AdminType;
    this.CompanyId = event.CompanyId;
    this.fetchCompanyAdminsData(1, this.pageSize);

    this.CompanyAdmins = event.ListResultContent;
    this.totalRecords = event.TotalRecords; // Set total records for pagination
    //this.paginator = false;

  }

  delete(CompanyAdmin) {

    this.CompanyAdmin = CompanyAdmin;
    this.confirmationDialog.message = 'Do you want to delete this CompanyAdmin ' + this.CompanyAdmin.Name;
    this.confirmationDialog.item = this.CompanyAdmin;
    this.confirmationDialog.openDialog();
    this.confirmationAction = 'delete';
  }

  // Method to handle the confirmation result
  handleConfirmationAction(result: boolean): void {
    if (this.confirmationAction == 'delete') {
      if (result) {
        this.CompanyAdminService.DeleteCompanyAdmin(this.CompanyAdmin.Id).subscribe((data) => {
          if (data['HasError'] == false) {
            if (this.CompanyAdmin.Name == this.currentUser.UserName) {
              this.authService.logout();
            }
            this.messageService.add({
              severity: "success",
              summary: "Success Message",
              detail: "Delete CompanyAdmin Successfully",
            });
            // Update the data array (remove the deleted item)
            this.CompanyAdmins = this.CompanyAdmins.filter(currentItem => currentItem.Id !== this.CompanyAdmin.Id);
            this.CompanyAdmin = new UserModel();
            this.confirmationAction = '';

          } else {
            this.confirmationDialog.item = this.CompanyAdmin;
            this.confirmationDialog.openDialog();
            this.confirmationDialog.message = data['EnErrorMessage'];
          }
        });
      } else {
        // console.log('Cancelled!');
        this.confirmationAction = '';
        this.CompanyAdmin = new UserModel();
      }
    }
  }
  editActiveCompanyAdmin(Id: String) {
    this.CompanyAdminService
      .EditActiveCompanyAdmin(Id)
      .subscribe((data: any) => {
        this.CompanyAdmin = this.CompanyAdmins.find((x) => x.Id == Id);
        if (data['HasError'] == true) {
          this.confirmationDialog.message = data['EnErrorMessage'];
        }
      });
  }

}
