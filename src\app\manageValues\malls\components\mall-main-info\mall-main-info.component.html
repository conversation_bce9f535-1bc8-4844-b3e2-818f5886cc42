<p-toast></p-toast>

<div class="row" [formGroup]="mallForm">
      <div class="container">
            <div class="row">
                  <div class="col-md-1" style="margin-right: 9%;">
                        <div class="text-center">
                              <div class="image-avatar-container">
                                    <label for="image" class="w-100">
                                          <img [src]="
                                                      imageUrl| placeholder: 'assets/layout/images/upload-image.png' "
                                                class="roundeisSelectedFiled-circle w-100" alt="..." />
                                          <p-button icon="pi pi-pencil" class="edit-button"
                                                styleClass="p-button-rounded" (click)="file.click()"></p-button>
                                    </label>
                                    <input type="file" id="imageUrl" class="d-none" (change)="uploadFile(file.files)"
                                          accept="image/*" #file formControlName="imageUrl" />
                              </div>
                              <br>
                              <label *ngIf="pressedSave && !this.imageUrl" for="logoImage" style="color: red">Please
                                    upload Logo
                                    Image </label>
                        </div>
                  </div>

                  <div class="col-md-8">
                        <div class="flex flex-column gap-3 mb-4">
                              <label for="EnName" [style.color]="CheckValid(mallForm.controls.EnName)">English
                                    Name</label>
                              <input id="EnName" type="text" pInputText placeholder="Mall English Name"
                                    [(ngModel)]="_MallData.EnName" class="p-inputtext-sm w-50"
                                    formControlName="EnName" />

                        </div>
                        <div class="flex flex-column gap-3 mb-4">
                              <label for="ArName" [style.color]="CheckValid(mallForm.controls.ArName)"> Arabic
                                    Name</label>
                              <input id="ArName" type="text" pInputText placeholder="Mall Arabic Name"
                                    [(ngModel)]="_MallData.ArName" class="p-inputtext-sm w-50"
                                    formControlName="ArName" />

                        </div>
                        <div class="flex flex-column gap-3 mb-4">
                              <label for="City" [style.color]="CheckValid(mallForm.controls.City)"> Location
                              </label>
                              <p-dropdown [style.color]="CheckValid(mallForm.controls.City)" [options]="cities"
                                    placeholder="Select The City" optionLabel="name" display="chip" id="dd_city"
                                    [(ngModel)]="_MallData.City" formControlName="City"></p-dropdown>
                        </div>
                        <div class="flex flex-column gap-3 mb-4">
                              <label for="Phone Numbers"> Phone Numbers
                              </label>

                              <div formArrayName="PhoneNumbers">
                                    <div *ngFor="let phone of mallForm.controls.PhoneNumbers.controls let i = index"
                                          [formGroupName]="i" class="flex align-items-center mb-3">
                                          <div class="p-inputgroup w-50">
                                                <span class="p-inputgroup-addon py-0 pe-0">
                                                      <i class="pi pi-phone me-2"
                                                            [ngStyle]="{ color: 'var(--green-500)' }"></i>
                                                      <p-dropdown [options]="allCountries.AllCOUNTRIES"
                                                            id="allCOUNTRIES" optionLabel="mobileCode"
                                                            optionValue="mobileCode" class="p-inputtext-sm w-50 d-flex"
                                                            [filter]="true" filterBy="mobileCode"
                                                            formControlName="CountryCode">

                                                            <ng-template let-option pTemplate="item">
                                                                  <img src="assets/demo/flags/flag_placeholder.png"
                                                                        [class]="'flag flag-' + option.code.toLowerCase()"
                                                                        alt="{{ option.name }}" />
                                                                  <span>{{ option.mobileCode }}</span>
                                                            </ng-template>
                                                      </p-dropdown>
                                                </span>
                                                <p-inputNumber [useGrouping]="false" inputId="mainPhoneNumber"
                                                      class="p-inputtext-sm w-50" formControlName="Number" />
                                          </div>
                                          &nbsp;&nbsp;
                                          <!-- Add/Remove Buttons -->
                                          <button *ngIf="i === mallForm.controls.PhoneNumbers.length - 1" type="button"
                                                pButton icon="pi pi-plus"
                                                class="p-button-rounded p-button-success p-mr-2"
                                                (click)="addPhoneNumber()"></button>
                                          &nbsp;
                                          <button *ngIf="mallForm.controls.PhoneNumbers.length > 1" type="button"
                                                pButton icon="pi pi-minus" class="p-button-rounded p-button-danger"
                                                (click)="removePhoneNumber(i)"></button>
                                    </div>
                              </div>
                        </div>
                        <div class="flex flex-column gap-3 mb-4">
                              <label for="about">English About</label>
                              <textarea rows="5" pInputTextarea [(ngModel)]="_MallData.EnAbout"
                                    [ngModelOptions]="{ standalone: true }" id="enAbout"></textarea>

                              <label for="about">Arabic About</label>
                              <textarea rows="5" pInputTextarea [(ngModel)]="_MallData.ArAbout"
                                    [ngModelOptions]="{ standalone: true }" id="arAbout"></textarea>
                        </div>

                        <!-- <div class="col-6">
                                    <button type="button" pButton
                                          label="show Linked Companies: {{_MallData.AssociatedCompanies.length}}"
                                          (click)="companiesOverlayPanel.toggle($event)"
                                          class="p-button-success"></button>
                                    <p-overlayPanel #companiesOverlayPanel [showCloseIcon]="true"
                                          [style]="{width: '450px'}">
                                          <ng-template pTemplate>
                                                <p-table [value]="_MallData.AssociatedCompanies" selectionMode="single"
                                                      [paginator]="true" [rows]="5" responsiveLayout="scroll">
                                                      <ng-template pTemplate="header">
                                                            <tr>
                                                                  <th pSortableColumn="name">Company Name<p-sortIcon
                                                                              field="name"></p-sortIcon></th>
                                                                  <th pSortableColumn="name">Company Branches</th>
                                                            </tr>
                                                      </ng-template>
                                                      <ng-template pTemplate="body" let-rowData let-company>
                                                            <tr [pSelectableRow]="rowData">
                                                                  <td style="min-width: 10rem;">
                                                                        <img width="60"
                                                                              [src]="
                                                                        companyProfileLogo(company.LogoUrl)| placeholder: 'assets/pages/icons/company.png' "
                                                                              alt="company.EnName" />
                                                                        &nbsp;&nbsp;
                                                                        {{company.EnName}}
                                                                  </td>
                                                                  <td style="min-width: 10rem;">
                                                                        {{getBranchNames(company.CompanyBranches) }}
                                                                  </td>
                                                            </tr>
                                                      </ng-template>
                                                      <ng-template pTemplate="emptymessage">
                                                            <tr>
                                                                  <td class="text-center" colspan="7">
                                                                        {{ "No Linked Companies." | translate }}
                                                                  </td>
                                                            </tr>
                                                      </ng-template>
                                                </p-table>
                                          </ng-template>

                                    </p-overlayPanel>
                              </div> -->
                  </div>

            </div>

      </div>

      <br />


      <br />
      <div class="flex justify-content-end gap-2 mb-3">
            <p-button label="save" (onClick)="save()"></p-button>
            <!-- [disabled]="!this.timingDetailsForm.valid" -->
      </div>
</div>