


<h3>
    <p-button icon="pi pi-arrow-left" styleClass="p-button-rounded p-button-secondary p-button-text"
        routerLink="/industries">
    </p-button>
    {{title}}
</h3>

<p-card class="container">
    <div class="row" [formGroup]="industryForm">
        <div class="row ">
            <div class="col-md-1" style="margin-right: 9%;">
                <div class="text-center">
                    <div class="image-avatar-container">
                        <label for="industryImage" class="w-100">
                            <img [src]="
                                        imageUrl
                                            | placeholder
                                                : 'assets/layout/images/upload-image.png'
                                    " class="roundeisSelectedFiled-circle w-100" alt="..." />
                            <p-button icon="pi pi-pencil" class="edit-button" styleClass="p-button-rounded"
                                (click)="file.click()"></p-button>
                        </label>
                        <input type="file" id="industryImage" class="d-none" (change)="uploadFile(file.files)"
                            accept="image/*" #file />
                    </div>
                    <br>
                    <div class="row"> <span class="font-light" *ngIf="pressedSave && !this.imageUrl"
                            class="text-danger">
                            Please upload image</span> </div>
                </div>
            </div>

            <div class="col-md-8">
                <div class="flex flex-column gap-3 mb-4">
                    <label for="enName" [style.color]="CheckValid(industryForm.controls.enName)">English
                        Name</label>
                    <input id="enName" type="text" pInputText placeholder="Industry English Name"
                        [(ngModel)]="_industry.EnName" class="p-inputtext-sm w-50" formControlName="enName" />

                </div>
                <div class="flex flex-column gap-3 mb-4">
                    <label for="arName" [style.color]="CheckValid(industryForm.controls.arName)"> Arabic
                        Name</label>
                    <input id="arName" type="text" pInputText placeholder="Industry Arabic Name"
                        [(ngModel)]="_industry.ArName" class="p-inputtext-sm w-50" formControlName="arName" />

                </div>
            </div>

            <div class="flex flex-column gap-3 mb-4">
                <div class="row"> </div>
                <div>
                    <h4 for="Filters">Filters Group : </h4>
                    <div class="card" [formGroup]="filterForm">
                        <h4 *ngIf="!enableEditFilter"> Add Filter</h4>
                        <h4 *ngIf="enableEditFilter">Edit Filter</h4>
                        <div class="field grid">

                            <label htmlFor="filterEnName" class="col-12 mb-2 md:col-2 md:mb-0"
                                [style.color]="CheckValidFilterInput(filterForm.controls.filterEnName)">English
                                Name:</label>

                            <div class="col-12 md:col-10">
                                <!-- formControlName="filterEnName"  -->
                                <input [(ngModel)]="selectedFilter.EnName" placeholder='Filter English Name'
                                    formControlName="filterEnName">
                            </div>
                        </div>

                        <div class="field grid">

                            <label htmlFor="filterArName" class="col-12 mb-2 md:col-2 md:mb-0"
                                [style.color]="CheckValidFilterInput(filterForm.controls.filterArName)">Arabic Name:
                            </label>
                            <div class="col-12 md:col-10">
                                <!-- formControlName="filterArName"  -->
                                <input [(ngModel)]="selectedFilter.ArName" placeholder='Filter Arabic Name'
                                    formControlName="filterArName">
                            </div>
                        </div>
                        <div class="flex justify-content-center gap-2 mb-3">
                            <p-button (click)="saveFilter()"> Save Filter</p-button>
                        </div>
                    </div>

                    <hr />

                </div>


                <!-- <div *ngFor="let filter of _industry?.Filters">
                        <div>EnName: {{filter.EnName}}</div>
                        <div>ArName: {{filter.ArName}}</div>
                    </div> -->

                <div class="row">
                    <p-table [value]="_industry?.Filters" [tableStyle]="{ 'min-width': '50rem' }"
                        styleClass="p-datatable-gridlines p-datatable-sm">
                        <ng-template pTemplate="header">
                            <tr>
                                <th>Filter English Name</th>
                                <th>Filter Arabic Name</th>
                                <th>Actions</th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-filter>
                            <tr>
                                <td>{{ filter.EnName }}</td>
                                <td>{{ filter.ArName }} </td>
                                <td>
                                    <p-button icon="pi pi-pencil" styleClass="p-button-rounded" class="mx-1"
                                        (click)="showSelectedFilter(filter)"></p-button>
                                    <p-button *ngIf="user.UserType == 0" icon="pi pi-trash"
                                        styleClass="p-button-rounded p-button-danger" (click)="removeFilter(filter)"
                                        class="mx-1"></p-button>

                                </td>
                            </tr>
                        </ng-template>
                        <!-- <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td class="text-center" colspan="7">
                                        {{ "No Filters found." | translate }}
                                    </td>
                                </tr>
                            </ng-template> -->
                    </p-table>
                </div>
            </div>



        </div>



        <br />


        <br />
        <div class="flex justify-content-center gap-2 mb-3">
            <p-button label="save" (onClick)="save()"></p-button>
            <!-- [disabled]="!this.timingDetailsForm.valid" -->
        </div>
    </div>

</p-card>