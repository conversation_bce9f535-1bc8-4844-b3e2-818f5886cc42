<p-toast></p-toast>
<div class="grid">
      <div class="col-12">
            <div>
                  <p-confirmDialog header="Confirmation" key="confirm1" icon="pi pi-exclamation-triangle"
                        message="Are you sure you want to proceed?" [style]="{ width: '350px' }"
                        acceptButtonStyleClass="p-button-text" rejectButtonStyleClass="p-button-text"></p-confirmDialog>
                  <p-table #dt [value]="CompanyAdmins" [paginator]="paginator" [rows]="pageSize"
                        [totalRecords]="totalRecords" [lazy]="true" [loading]="loading" (onLazyLoad)="loadData($event)"
                        [showCurrentPageReport]="true"
                        [currentPageReportTemplate]="'Showing {first} to {last} of {totalRecords} records'"
                        responsiveLayout="scroll" [rowHover]="true" styleClass="p-datatable-gridlines">

                        <ng-template pTemplate="caption">
                              <app-grid-headers [myDt]="CompanyAdmins" (SearchEvent)='ReceivedFilteredData($event)'
                                    addNewTxt="Add New Company Admin" goRoute="/company-admin-new"
                                    gridTitle="CompanyAdminsManagement" [title]="title">
                              </app-grid-headers>
                        </ng-template>
                        <ng-template pTemplate="header">
                              <tr>
                                    <th>

                                    </th>
                                    <th>
                                          <div class="flex justify-content-between align-items-center">
                                                {{ "Admin Name" | translate }}
                                          </div>
                                    </th>
                                    <th>
                                          <div class="flex justify-content-between align-items-center">
                                                {{ "Type" | translate }}
                                          </div>
                                    </th>
                                    <th *ngIf="currentUser.UserType != 1">
                                          <div class="flex justify-content-between align-items-center">
                                                {{ "Company" | translate }}
                                          </div>
                                    </th>
                                    <th>
                                          <div class="flex justify-content-between align-items-center">
                                                {{ "Edit" | translate }}
                                          </div>
                                    </th>
                              </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-CompanyAdmin>
                              <tr [ngClass]="{'inactive-table-row': CompanyAdmin.Active === false}">
                                    <td>
                                          <div class="d-flex align-items-center">
                                                <p-inputSwitch [(ngModel)]="CompanyAdmin.Active"
                                                      (onChange)="editActiveCompanyAdmin(CompanyAdmin.Id)"></p-inputSwitch>
                                          </div>
                                    </td>
                                    <td *ngIf="currentUser.UserType != 1">
                                          <div class="flex">
                                                <p-avatar *ngIf="CompanyAdmin.ProfilePhotoUrl"
                                                      image="{{imageSrc + CompanyAdmin.ProfilePhotoUrl}}"
                                                      styleClass="mr-2" size="medium" shape="circle"></p-avatar>
                                                {{ CompanyAdmin.Name }}
                                          </div>
                                    </td>
                                    <td>
                                          <div class="flex">
                                                <p-tag severity="success" *ngIf="CompanyAdmin.AdminType==1"
                                                      styleClass="mr-2" icon="pi pi-user"
                                                      value="{{CompanyAdmin.AdminType | AdminType}}"></p-tag>
                                                <p-tag severity="warning" *ngIf="CompanyAdmin.AdminType==0"
                                                      styleClass="mr-2" icon="fas fa-user-shield"
                                                      value="{{CompanyAdmin.AdminType | AdminType}}"></p-tag>
                                          </div>
                                    </td>
                                    <td>
                                          <div class="flex">
                                                <p-avatar *ngIf="CompanyAdmin.Company?.LogoUrl"
                                                      image="{{imageSrc + CompanyAdmin.Company?.LogoUrl}}"
                                                      styleClass="mr-2" size="medium" shape="circle"></p-avatar>
                                                {{ CompanyAdmin.Company?.EnName }}
                                          </div>
                                    </td>

                                    <td>
                                          <div class="flex">
                                                <div class="d-flex align-items-center justify-content-between">
                                                      <p-button icon="pi pi-pencil" styleClass="p-button-rounded"
                                                            class="mx-1"
                                                            (click)=" edit({ CompanyAdmin: CompanyAdmin, state: 'edit' })"></p-button>
                                                      <p-button icon="pi pi-trash"
                                                            styleClass="p-button-rounded p-button-danger" class="mx-1"
                                                            (click)="delete(CompanyAdmin)"></p-button>
                                                </div>
                                          </div>
                                    </td>
                              </tr>
                        </ng-template>
                        <ng-template pTemplate="emptymessage">
                              <tr>
                                    <td class="text-center" colspan="7">
                                          {{ "No Website Admins found." | translate }}
                                    </td>
                              </tr>
                        </ng-template>
                  </p-table>
                  <!-- Reusable Confirmation Dialog Component -->
                  <app-confirmation-dialog #confirmationDialog
                        (confirmResult)="handleConfirmationAction($event)"></app-confirmation-dialog>

            </div>
      </div>
</div>