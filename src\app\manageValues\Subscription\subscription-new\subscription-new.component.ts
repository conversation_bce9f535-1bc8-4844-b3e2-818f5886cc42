import { Component, ViewChild } from '@angular/core';
import { SubscriptionMainInfoComponent } from '../Components/subscription-main-info/subscription-main-info.component';
import { SubscriptionStatisticsComponent } from '../Components/subscription-statistics/subscription-statistics.component';
import { Message, MessageService } from 'primeng/api';
import { ActivatedRoute, Router } from '@angular/router';
import { SubscriptionModel } from 'src/app/Model/SubscriptionModel';

@Component({
  selector: 'app-subscription-new',
  templateUrl: './subscription-new.component.html',
  styleUrls: ['./subscription-new.component.scss']
})
export class SubscriptionNewComponent {
newSubscription: SubscriptionModel = new SubscriptionModel();
  routeState: any;
  activeIndex = 0;
  previousTabIndex: number = 0; // Track the previous tab index
  isEditing: boolean = false;
  title: string = 'Add New Subscription';

  tabsDisabled: boolean = true;  // Control whether tabs are disabled or not
  msgs: Message[] = [];

  @ViewChild(SubscriptionMainInfoComponent) SubscriptionMainInfoComponent!: SubscriptionMainInfoComponent;
  @ViewChild(SubscriptionStatisticsComponent) SubscriptionStatisticsComponent!: SubscriptionStatisticsComponent;

  constructor(private router: Router, private messageService: MessageService, private route: ActivatedRoute) {
    if (this.router.getCurrentNavigation()?.extras.state) {

      this.routeState = this.router.getCurrentNavigation()?.extras.state;
      if (this.routeState) {
        this.newSubscription = this.routeState.data
          ? this.routeState.data
          : new SubscriptionModel();
        if (this.newSubscription.Id == "") {
          this.isEditing = false;
        }
        else {
          this.isEditing = true;
        }
      }
    }
    if (this.route.snapshot.url[this.route.snapshot.url.length - 1].path === 'subscription-edit') {
      this.isEditing = true;
      this.title = 'Edit Subscription';
      this.tabsDisabled = false;

    }

  }
  ngOnInit() {
    if (this.isEditing == true && this.newSubscription.Id == "") {
      this.router.navigate(['/subscriptions']);
      return;
    }
  }
  onTabChange(event: any) {
  }

  // Method to handle child component event and disable tabs
  handleDisableTabs(disable: boolean) {
    this.tabsDisabled = disable;  // If true, disable all tabs

  }
}
