import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { ConfirmationService, MessageService } from 'primeng/api';
import { EndUserModel } from 'src/app/Model/EndUserModel';
import { TiersModel } from 'src/app/Model/TiersModel';
import { AuthService } from 'src/app/services/auth.service';
import { EndUsersService } from 'src/app/services/end-users.service';
import { TierService } from 'src/app/services/tier.service';

@Component({
  selector: 'app-end-users-list',
  templateUrl: './end-users-list.component.html',
  styleUrls: ['./end-users-list.component.scss'],
  providers: [MessageService, ConfirmationService],
})
export class EndUsersListComponent {
  EndUsers: EndUserModel[];
  loading: boolean = true;
  //filter
  Name: any;
  Gender: any;
  Residance: any;
  TierId: any;
  totalRecords: number = 0;
  pageSize: number = 10;
  pageIndex: number = 0;
  paginator: boolean = false;
  //  track the first load
  private isInitialLoad: boolean = false;
  title: string;

  // Dialog
  displayDialog: boolean = false;
  pressedSave: boolean = false;
  selectedEndUser: EndUserModel = null;
  tiers: TiersModel[] = [];
  selectedTier: TiersModel;



  constructor(private EndUsersService: EndUsersService, private router: Router, private messageService: MessageService, private confirmationService: ConfirmationService, private authService: AuthService, private tierService: TierService) {
  }


  ngOnInit() {
    this.loadData({ first: 0, rows: this.pageSize });
    this.isInitialLoad = true;
    this.title = "EndUsersManagement";
  }

  loadData(event: any) {
    // Avoid double loading during the first render
    if (this.isInitialLoad) {
      this.isInitialLoad = false; // Set the flag to false after the first load
      return;
    }
    const pageNumber = event.first / event.rows + 1; // Calculate the page number
    const pageSize = event.rows; // Rows per page
    this.paginator = true;
    this.fetchData(pageNumber, pageSize);
  }

  fetchData(pageNumber, pageSize) {
    this.EndUsersService
      .GetAllEndUsers(pageNumber, pageSize, this.Name, this.Gender, this.Residance, this.TierId)
      .subscribe((data) => {
        this.EndUsers = data.ListResultContent;
        this.totalRecords = data.TotalRecords; // Set total records for pagination
        this.loading = false;
      });
  }

  ReceivedFilteredData(event) {
    this.Name = event.Name;
    this.Gender = event.Gender;
    this.Name = event.Name;
    this.Residance = event.Residance;
    this.TierId = event.TierId;
    this.fetchData(1, this.pageSize);
    this.EndUsers = event.ListResultContent;
    this.totalRecords = event.TotalRecords; // Set total records for pagination
  }

  openDialog(endUser: any) {
    this.selectedEndUser = endUser;
    this.selectedTier = this.selectedEndUser.Tier;
    // Fetch Tiers from API only if the list is empty
    if (this.tiers.length === 0) {
      this.tierService.getAllTiers(true).subscribe((data) => {
        this.tiers = data;
      });
    }
    this.displayDialog = true;
  }
  MakeEndUserVipUser() {
    this.pressedSave = true;
    if (this.selectedTier) {
      var data = {
        'EndUserId': this.selectedEndUser.Id,
        'TierId': this.selectedTier.Id
      }
      this.EndUsersService.MakeEndUserVipUser(data).subscribe(res => {
        if (!res.HasError) {
          // Update the end user's tier in the EndUsers array
          const userIndex = this.EndUsers.findIndex(user => user.Id === this.selectedEndUser.Id);
          if (userIndex !== -1) {
            this.EndUsers[userIndex].Tier = this.selectedTier;
          }
          this.displayDialog = false;
          this.messageService.add({
            severity: "success",
            summary: "Success Message",
            detail: res['EnErrorMessage'],
          });
        }
      })

    } else {
      console.log('Invalid form');
    }
  }
}
