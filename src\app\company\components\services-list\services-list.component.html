<p-toast></p-toast>
<div class="flex flex-column gap-2 mb-3">
    <label for="serviceLinkUrl" [style.color]="CheckValid()">Service link URL</label>
    <!-- [style.color]="CheckValid()" -->
    <input type="text" pInputText placeholder="Service Link URL" [(ngModel)]="_Company.ServiceListUrl"
        (ngModelChange)="CheckValid()" class="p-inputtext-sm w-50" />
    <!-- placeholder="https://example.com/file.pdf" -->
</div>
<div class="iframe-container">
    <iframe *ngIf="_Company.ServiceListUrl" [src]="sanitizeURL(_Company.ServiceListUrl)" width="100%"
        height="400px"></iframe>
</div>

<div class="flex justify-content-end gap-2 mb-3">
    <p-button label="back" styleClass="p-button-outlined p-button-secondary" (click)="back()"></p-button>
    <p-button label="Next" (click)="next()"></p-button>
</div>