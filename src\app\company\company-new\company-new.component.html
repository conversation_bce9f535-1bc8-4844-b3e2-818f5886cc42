<p-toast></p-toast>
<div>
    <h3>
        <p-button *ngIf="userType == 0" icon="pi pi-arrow-left"
            styleClass="p-button-rounded p-button-secondary p-button-text" routerLink="/companies">
        </p-button>
        {{title | translate }}
    </h3>
    <p-messages *ngIf="this.newCompany.hasError" [value]="msgs"></p-messages>
    <!-- <p-tabView [activeIndex]="1"> -->
    <p-tabView [(activeIndex)]="activeIndex" (onChange)="onTabChange($event)">
        <p-tabPanel header="Overview" [ngStyle]="{ color: 'var(--cyan-300-color)' }">
            <p-card role="region">
                <app-overview [(_OverViewData)]="newCompany" [(fileToUpload)]="fileToUpload"
                    [(activeIndex)]="activeIndex" [isEditing]="isEditing"></app-overview>
            </p-card>
        </p-tabPanel>
        <p-tabPanel header="Branches" [disabled]="tabsDisabled">
            <p-card role="region">
                <app-branches [(_OverViewData)]="newCompany" [(fileToUpload)]="fileToUpload"
                    [(activeIndex)]="activeIndex"></app-branches>
            </p-card>
        </p-tabPanel>
        <p-tabPanel header="Services List" [disabled]="tabsDisabled">
            <p-card role="region">
                <app-services-list [(_Company)]="newCompany" [(activeIndex)]="activeIndex"></app-services-list>
            </p-card>
        </p-tabPanel>
        <p-tabPanel header="Subscriptions" [disabled]="tabsDisabled">
            <p-card role="region">
                <app-subscriptions [(_Company)]="newCompany" [(activeIndex)]="activeIndex"
                    [(fileToUpload)]="fileToUpload"></app-subscriptions>
            </p-card>
        </p-tabPanel>
    </p-tabView>
</div>