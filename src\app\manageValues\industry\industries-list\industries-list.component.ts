import { Component } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { ConfirmationService, MessageService } from 'primeng/api';
import { Country } from 'src/app/Model/Country';
import { IndustryModel } from 'src/app/Model/IndustryModel';
import { UserModel } from 'src/app/Model/UserModel';
import { AuthService } from 'src/app/services/auth.service';
import { CountryService } from 'src/app/services/country.service';
import { IndustryService } from 'src/app/services/industry.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-industries-list',
  templateUrl: './industries-list.component.html',
  styleUrls: ['./industries-list.component.scss'],
  providers: [MessageService, ConfirmationService]
})
export class IndustriesListComponent {
  industries: IndustryModel[];
  loading: boolean = true;
  user: UserModel;

  //filter
  industryName: any;

  totalRecords: number = 0;
  pageSize: number = 10;
  pageIndex: number = 0;
  paginator: boolean = false;
  imageSrc: string = environment.imageSrc;

  //  track the first load
  private isInitialLoad: boolean = false;

  constructor(private industryService: IndustryService, private router: Router, private messageService: MessageService, private confirmationService: ConfirmationService, private sanitizer: DomSanitizer, private authService: AuthService) {

  }


  ngOnInit() {
    this.loadData({ first: 0, rows: this.pageSize });
    this.isInitialLoad = true;

  }

  loadData(event: any) {
    // Avoid double loading during the first render
    if (this.isInitialLoad) {
      this.isInitialLoad = false; // Set the flag to false after the first load
      return;
    }

    this.user =  this.authService.getUserData();
    const pageNumber = event.first / event.rows + 1; // Calculate the page number
    const pageSize = event.rows; // Rows per page
    this.paginator = true;
    if (this.user.UserType == 0) {
      this.fetchData(pageNumber, pageSize);
    }
  }

  fetchData(pageNumber, pageSize) {
    this.industryService
      .GetAllIndustry(true, pageNumber, pageSize, this.industryName,)
      .subscribe((data) => {
        this.industries = data.ListResultContent;
        this.totalRecords = data.TotalRecords; // Set total records for pagination
        this.loading = false;
      });
  }

  edit(indusrty, state: string) {
    // this.formData = e.company;

    // this.companyService.GetCompanyById(e.company.Id.toString()).subscribe((data) => {
    this.router.navigate(["industry-edit"], {
      state: {
        data: indusrty,
        command: "edit",
      },
      // });
    });

  }

  delete(id) {

    this.confirmationService.confirm({
      key: "confirm1",
      target: event.target,
      message: "Are You Sure Delete This Industry",
      icon: "pi pi-exclamation-triangle",
      accept: () => {
        this.industryService
          .DeleteIndustry(id)
          .subscribe((data: any) => {
            if (data['HasError'] == true) {
              this.messageService.add({
                severity: "error",
                summary: "Error Message",
                detail: data['EnErrorMessage'],
              });
            }
            else if (data['HasError'] == false) {
              this.messageService.add({
                severity: "success",
                summary: "Success Message",
                detail: "Delete Industry Successfully",
              });
              var company = this.industries.find((x) => x.Id == id);
              const index = this.industries.indexOf(company, 0);
              // Update the data array (remove the deleted item)
              this.industries = this.industries.filter(currentItem => currentItem.Id !== company.Id);
            }
            else {
              this.messageService.add({
                severity: "error",
                summary: "Error",
                detail: "Can't Delete This Industry",
              });
            }
          });
      },
      reject: () => {
        this.messageService.add({
          severity: "error",
          summary: "Rejected",
          detail: "You have rejected",
        });
      },
    });

  }


  ReceivedFilteredData(event) {

    this.industryName = event.Name;

    this.fetchData(1, this.pageSize);
  }

}
