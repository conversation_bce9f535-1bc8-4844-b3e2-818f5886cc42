import { Pipe, PipeTransform } from '@angular/core';
import { ProtectionType } from '../enum/protection-type';

@Pipe({
  name: 'ProtectionType'
})
export class ProtectionTypePipe implements PipeTransform {

  transform(value: number, ...args: unknown[]): any {
    console.log('protictionTypePipe',value ,'val',Object.values(ProtectionType)[value]);
    return Object.values(ProtectionType)[value];
  }

}
