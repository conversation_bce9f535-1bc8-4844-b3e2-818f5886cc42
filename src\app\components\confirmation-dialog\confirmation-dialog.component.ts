import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-confirmation-dialog',
  templateUrl: './confirmation-dialog.component.html',
  styleUrls: ['./confirmation-dialog.component.scss']
})
export class ConfirmationDialogComponent {
 // Inputs for message and item
 @Input() message: string = 'Are you sure you want to proceed?';
 @Input() item: any = null;

 
 // Output to emit the user's response
 @Output() confirmResult = new EventEmitter<boolean>();

 // Property to show or hide the dialog
 visible: boolean = false;

 // Method to open the dialog
 openDialog(): void {
   this.visible = true;
 }

 // Method to handle the confirmation action
 confirm(): void {
   this.confirmResult.emit(true);
   this.visible = false;
 }

 // Method to handle the cancellation action
 cancel(): void {
   this.confirmResult.emit(false);
   this.visible = false;
 }
}
