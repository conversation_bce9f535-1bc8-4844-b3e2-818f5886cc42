import { CompanylistComponent } from './company/companylist/companylist.component';
import { RouterModule } from '@angular/router';
import { NgModule } from '@angular/core';
import { AppMainComponent } from './app.main.component';
import { AppNotfoundComponent } from './pages/app.notfound.component';
import { AppErrorComponent } from './pages/app.error.component';
import { AppAccessdeniedComponent } from './pages/app.accessdenied.component';
import { AppLoginComponent } from './pages/app.login.component';
import { AuthGuard } from './guard/auth.guard';
import { DiscountlistComponent } from './discount/discountlist/discountlist.component';
import { DiscountnewComponent } from './discount/discountnew/discountnew.component';
import { CompanyNewComponent } from './company/company-new/company-new.component';
import { DicountApprovmentComponent } from './discount/dicount-approvment/dicount-approvment.component';
import { PreviewGuard } from './guard/preview.guard';
import { IndustryNewComponent } from './manageValues/industry/industry-new/industry-new.component';
import { IndustriesListComponent } from './manageValues/industry/industries-list/industries-list.component';
import { MallsListComponent } from './manageValues/malls/malls-list/malls-list.component';
import { MallNewComponent } from './manageValues/malls/mall-new/mall-new.component';
import { CouponlistComponent } from './coupon/coupon-list/coupon-list.component';
import { CouponPreviewComponent } from './coupon/coupon-preview/coupon-preview.component';
import { CouponNewComponent } from './coupon/coupon-new/coupon-new.component';
import { BroadcastMessagesListComponent } from './broadcastMessages/broadcast-messages-list/broadcast-messages-list.component';
import { BroadcastMessagePreviewComponent } from './broadcastMessages/broadcast-message-preview/broadcast-message-preview.component';
import { BroadcastMessageNewComponent } from './broadcastMessages/broadcast-message-new/broadcast-message-new.component';
import { CountrylistComponent } from './manageValues/country/countryList/countryList.component';
import { CountryNewComponent } from './manageValues/country/country-new/country-new.component';
import { TiersListComponent } from './manageValues/Tiers/tiers-list/tiers-list.component';
import { TierNewComponent } from './manageValues/Tiers/tier-new/tier-new.component';
import { AdminManagementListComponent } from './manageValues/adminManagement/admin-management-list/admin-management-list.component';
import { WebsiteAdminNewComponent } from './manageValues/adminManagement/websiteAdmins/website-admin-new/website-admin-new.component';
import { CompanyAdminsNewComponent } from './manageValues/adminManagement/companyAdmins/company-admins-new/company-admins-new.component';
import { WebsiteAdminPageEnum } from './enum/website-admin-pages-enum';
import { CompanyAdminPageEnum } from './enum/company-admin-pages-enum';
import { PermissionsGuard } from './guard/permissions.guard';
import { WebsiteAdminsListComponent } from './manageValues/adminManagement/websiteAdmins/website-admins-list/website-admins-list.component';
import { CompanyAdminsListComponent } from './manageValues/adminManagement/companyAdmins/company-admins-list/company-admins-list.component';
import { AuthService } from './services/auth.service';
import { AdminDetailsComponent } from './pages/admin-details/admin-details.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { DashboardCompanyComponent } from './dashboard-company/dashboard-company.component';
import { SubscriptionListComponent } from './manageValues/Subscription/subscription-list/subscription-list.component';
import { SubscriptionNewComponent } from './manageValues/Subscription/subscription-new/subscription-new.component';
import { EndUsersListComponent } from './endUsers/end-users-list/end-users-list.component';


@NgModule({
	imports: [
		RouterModule.forRoot([
			{
				path: '', component: AppMainComponent, canActivate: [AuthGuard], data: {},
				children: [
					{ path: '', component: AdminDetailsComponent },
					{ path: 'dashboard', component: DashboardComponent, canActivate: [PermissionsGuard], data: { permission: WebsiteAdminPageEnum.Dashboard } },

					{ path: 'company-dashboard', component: DashboardCompanyComponent, canActivate: [PermissionsGuard], data: { permission: WebsiteAdminPageEnum.Dashboard } },

					{ path: 'companies', component: CompanylistComponent, canActivate: [PermissionsGuard], data: { permission: WebsiteAdminPageEnum.Companies } },
					{ path: 'company-new', component: CompanyNewComponent, canActivate: [PermissionsGuard], data: { permission: WebsiteAdminPageEnum.Companies } },

					// { path: 'discounts', component: DiscountlistComponent, canActivate: [ PermissionsGuard],  data: { permission: CompanyAdminPageEnum.Discounts } },
					{ path: 'discounts-inbox', component: DiscountlistComponent, canActivate: [PermissionsGuard], data: { reviewed: false, deactivated: false, permission: WebsiteAdminPageEnum.Discounts } },
					{ path: 'discounts-reviewed', component: DiscountlistComponent, canActivate: [PermissionsGuard], data: { reviewed: true, deactivated: false, permission: WebsiteAdminPageEnum.Discounts } },
					{ path: 'discounts-deactivated', component: DiscountlistComponent, canActivate: [PermissionsGuard], data: { deactivated: true, reviewed: false, permission: WebsiteAdminPageEnum.Discounts } },
					{ path: 'company-discounts', component: DiscountlistComponent, canActivate: [PermissionsGuard], data: { permission: CompanyAdminPageEnum.Discounts } },
					{ path: 'discount-preview', component: DicountApprovmentComponent, canActivate: [PermissionsGuard, PreviewGuard], data: { permission: WebsiteAdminPageEnum.Discounts } },
					{ path: 'discount-new', component: DiscountnewComponent, canActivate: [PermissionsGuard], data: { title: 'New Discount', permission: CompanyAdminPageEnum.Discounts } },
					{ path: 'discount-edit', component: DiscountnewComponent, canActivate: [PermissionsGuard], data: { title: 'Edit Discount', permission: CompanyAdminPageEnum.Discounts } },

					// { path: 'coupons', component: CouponlistComponent },

					{ path: 'company-coupons', component: CouponlistComponent, canActivate: [PermissionsGuard], data: { permission: CompanyAdminPageEnum.Coupons } },
					{ path: 'coupons-inbox', component: CouponlistComponent, canActivate: [PermissionsGuard], data: { reviewed: false, deactivated: false, permission: WebsiteAdminPageEnum.Coupons } },
					{ path: 'coupons-reviewed', component: CouponlistComponent, canActivate: [PermissionsGuard], data: { reviewed: true, deactivated: false, permission: WebsiteAdminPageEnum.Coupons } },
					{ path: 'coupons-deactivated', component: CouponlistComponent, canActivate: [PermissionsGuard], data: { deactivated: true, reviewed: false, permission: WebsiteAdminPageEnum.Coupons } },
					{ path: 'coupons', component: CouponlistComponent, canActivate: [PermissionsGuard], data: { permission: WebsiteAdminPageEnum.Coupons } },
					{ path: 'coupon-preview', component: CouponPreviewComponent, canActivate: [PermissionsGuard, PreviewGuard], data: { permission: WebsiteAdminPageEnum.Coupons } },
					{ path: 'coupon-new', component: CouponNewComponent, canActivate: [PermissionsGuard], data: { title: 'New Coupon', permission: WebsiteAdminPageEnum.Coupons } },
					{ path: 'coupon-edit', component: CouponNewComponent, canActivate: [PermissionsGuard], data: { title: 'Edit Coupon', permission: WebsiteAdminPageEnum.Coupons } },

					{ path: 'company-broadcast-messages', component: BroadcastMessagesListComponent, canActivate: [PermissionsGuard], data: { permission: WebsiteAdminPageEnum.BroadcastMessages } },
					{ path: 'broadcast-messages-inbox', component: BroadcastMessagesListComponent, canActivate: [PermissionsGuard], data: { reviewed: false, deactivated: false, permission: WebsiteAdminPageEnum.BroadcastMessages } },
					{ path: 'broadcast-messages-reviewed', component: BroadcastMessagesListComponent, canActivate: [PermissionsGuard], data: { reviewed: true, deactivated: false, permission: WebsiteAdminPageEnum.BroadcastMessages } },
					{ path: 'broadcast-messages-deactivated', component: BroadcastMessagesListComponent, canActivate: [PermissionsGuard], data: { deactivated: true, reviewed: false, permission: WebsiteAdminPageEnum.BroadcastMessages } },
					{ path: 'broadcast-messages', component: BroadcastMessagesListComponent, canActivate: [PermissionsGuard], data: { permission: WebsiteAdminPageEnum.BroadcastMessages } },
					{ path: 'broadcast-message-preview', component: BroadcastMessagePreviewComponent, canActivate: [PermissionsGuard, PreviewGuard], data: { permission: WebsiteAdminPageEnum.BroadcastMessages } },
					{ path: 'broadcast-message-new', component: BroadcastMessageNewComponent, canActivate: [PermissionsGuard], data: { permission: WebsiteAdminPageEnum.BroadcastMessages } },
					{ path: 'broadcast-message-edit', component: BroadcastMessageNewComponent, canActivate: [PermissionsGuard], data: { permission: WebsiteAdminPageEnum.BroadcastMessages } },
					{ path: 'end-users', component: EndUsersListComponent, canActivate: [PermissionsGuard], data: { permission: WebsiteAdminPageEnum.EndUsers } },
					{ path: 'industries', component: IndustriesListComponent, canActivate: [PermissionsGuard], data: { permission: WebsiteAdminPageEnum.Industries } },
					{ path: 'industry-new', component: IndustryNewComponent, canActivate: [PermissionsGuard], data: { permission: WebsiteAdminPageEnum.Industries } },
					{ path: 'industry-edit', component: IndustryNewComponent, canActivate: [PermissionsGuard], data: { permission: WebsiteAdminPageEnum.Industries } },

					{ path: 'malls', component: MallsListComponent, canActivate: [PermissionsGuard], data: { permission: WebsiteAdminPageEnum.Malls } },
					{ path: 'mall-new', component: MallNewComponent, canActivate: [PermissionsGuard], data: { permission: WebsiteAdminPageEnum.Malls } },
					{ path: 'mall-edit', component: MallNewComponent, canActivate: [PermissionsGuard], data: { permission: WebsiteAdminPageEnum.Malls } },

					{ path: 'countries', component: CountrylistComponent, canActivate: [PermissionsGuard], data: { permission: WebsiteAdminPageEnum.Countries } },
					{ path: 'country-new', component: CountryNewComponent, canActivate: [PermissionsGuard], data: { permission: WebsiteAdminPageEnum.Countries } },
					{ path: 'country-edit', component: CountryNewComponent, canActivate: [PermissionsGuard], data: { permission: WebsiteAdminPageEnum.Countries } },

					{ path: 'tiers', component: TiersListComponent, canActivate: [PermissionsGuard], data: { permission: WebsiteAdminPageEnum.Tiers } },
					{ path: 'tier-new', component: TierNewComponent, canActivate: [PermissionsGuard], data: { title: 'New Tier', permission: WebsiteAdminPageEnum.Tiers } },
					{ path: 'tier-edit', component: TierNewComponent, canActivate: [PermissionsGuard], data: { title: 'Edit Tier', permission: WebsiteAdminPageEnum.Tiers } },

					{ path: 'subscriptions', component: SubscriptionListComponent, canActivate: [PermissionsGuard], data: { permission: WebsiteAdminPageEnum.Subscriptions } },
					{ path: 'subscription-new', component: SubscriptionNewComponent, canActivate: [PermissionsGuard], data: { title: 'New Subscription', permission: WebsiteAdminPageEnum.Subscriptions } },
					{ path: 'subscription-edit', component: SubscriptionNewComponent, canActivate: [PermissionsGuard], data: { title: 'Edit Subscription', permission: WebsiteAdminPageEnum.Subscriptions } },
					{ path: 'admins-management', component: AdminManagementListComponent },

					{ path: 'website-admins-management', component: WebsiteAdminsListComponent, canActivate: [PermissionsGuard], data: { permission: WebsiteAdminPageEnum.WebsiteAdmins } },
					{ path: 'website-admin-new', component: WebsiteAdminNewComponent, canActivate: [PermissionsGuard], data: { permission: WebsiteAdminPageEnum.WebsiteAdmins } },
					{ path: 'website-admin-edit', component: WebsiteAdminNewComponent, canActivate: [PermissionsGuard], data: { permission: WebsiteAdminPageEnum.WebsiteAdmins } },

					{ path: 'company-admins-management', component: CompanyAdminsListComponent, canActivate: [PermissionsGuard], data: { permission: JSON.parse(localStorage.getItem('currentUser'))?.UserType == 0 ? WebsiteAdminPageEnum.CompanyAdmins : CompanyAdminPageEnum.CompanyAdmins } },
					{ path: 'company-admin-new', component: CompanyAdminsNewComponent, canActivate: [PermissionsGuard], data: { permission: JSON.parse(localStorage.getItem('currentUser'))?.UserType == 0 ? WebsiteAdminPageEnum.CompanyAdmins : CompanyAdminPageEnum.CompanyAdmins } },
					{ path: 'company-admin-edit', component: CompanyAdminsNewComponent, canActivate: [PermissionsGuard], data: { permission: JSON.parse(localStorage.getItem('currentUser'))?.UserType == 0 ? WebsiteAdminPageEnum.CompanyAdmins : CompanyAdminPageEnum.CompanyAdmins } },

				]
			},
			{ path: 'error', component: AppErrorComponent },
			{ path: 'access', component: AppAccessdeniedComponent },
			{ path: 'notfound', component: AppNotfoundComponent },
			{ path: 'login', component: AppLoginComponent },
			{ path: '**', redirectTo: '/notfound' },
		], { scrollPositionRestoration: 'enabled' })
	],
	exports: [RouterModule]
})
export class AppRoutingModule {

	constructor(private authService: AuthService) { }
}
