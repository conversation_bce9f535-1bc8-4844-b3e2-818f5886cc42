<p-table selectionMode="single" [paginator]="true" [rows]="5" responsiveLayout="scroll" [rowHover]="true"
      styleClass="p-datatable-gridlines" [value]="tierDataArray">
      <ng-template pTemplate="header">
            <tr>
                  <th>
                        <div class="flex"> Total Countries</div>
                  </th>
                  <th>
                        <div class="flex"> Total Users </div>
                  </th>
            </tr>
      </ng-template>
      <ng-template pTemplate="body" let-rowData>
            <tr>
                  <td style="min-width: 10rem;">
                        <div class="flex"> {{rowData.TotalCountries}} </div>
                  </td>
                  <td style="min-width: 10rem;">
                        <div class="flex"> {{rowData.TotalUsers }} </div>
                  </td>
            </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
            <tr>
                  <td class="text-center" colspan="7">
                        {{ "No Linked Countries." | translate }}
                  </td>
            </tr>
      </ng-template>
</p-table>

<p-table [value]="_TierData.UserStatistics" [paginator]="true" [rows]="5" responsiveLayout="scroll" [rowHover]="true"
      styleClass="p-datatable-gridlines">
      <ng-template pTemplate="header">
            <tr>
                  <th>
                        <div class="flex">Country</div>
                  </th>
                  <th pSortableColumn="TotalUsers">
                        <div class="flex">Total Users <p-sortIcon field="TotalUsers"></p-sortIcon></div>
                  </th>
            </tr>
      </ng-template>
      <ng-template pTemplate="body" let-rowData let-Statistic>
            <tr [pSelectableRow]="rowData">
                  <td style="min-width: 10rem;">
                        <div class="flex">
                              <img src="assets/demo/flags/flag_placeholder.png"
                                    [class]="'flag flag-' +  this.allCountries.getCodeFromName( Statistic.Country?.EnName)"
                                    alt="{{ Statistic.Country?.EnName }}" />
                              &nbsp;
                              {{Statistic.Country?.EnName}}
                        </div>
                  </td>
                  <td style="min-width: 10rem;">
                        <div class="flex">
                              {{Statistic.TotalUsers }}
                        </div>
                  </td>
            </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
            <tr>
                  <td class="text-center" colspan="7">
                        {{ "No Linked Countries." | translate }}
                  </td>
            </tr>
      </ng-template>
</p-table>