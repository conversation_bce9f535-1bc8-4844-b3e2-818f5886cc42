import { Component } from '@angular/core';
import { AppConfig } from '../demo/domain/appconfig';
import { Subscription } from 'rxjs';
import { DashboardService } from '../services/dashboard.service';
import { ConfigService } from '../demo/service/app.config.service';
import { CompanyModel } from '../Model/CompanyModel';
import { CompanyService } from '../services/company.service';
import { EndpointUsedInEnum } from '../enum/endpoint-used-in-enum';
import { UserModel } from '../Model/UserModel';
import { AuthService } from '../services/auth.service';
import { TimeConverter } from '../enum/time-enum';


@Component({
  selector: 'app-dashboard-company',
  templateUrl: './dashboard-company.component.html',
  styleUrls: ['./dashboard-company.component.scss']
})
export class DashboardCompanyComponent {
  companies: CompanyModel[];
  CompanyId: string = '';
  Company: CompanyModel | null = null;
  years: { label: string; value: number }[] = [];
  Year: { label: string; value: number } | null = null;

  config: AppConfig;
  subscription: Subscription;
  loading: boolean = true;
  lineOptions: any;
  lineData: any;

  pieData: any;
  pieOptions: any;
  barData: any;
  barOptions: any;
  ColumnOptions: any;

  DiscountSalesWeeklyStatistics: any;
  DiscountsSalesEstimationsYearly: any;

  CouponRedemptionWeeklyStatistics: any;
  CouponsRedemptionYearly: any;

  CouponWeeklyStatistics: any;
  CouponsYearly: any;

  CouponsSalesEstimationWeeklyStatistics: any;
  CouponsSalesEstimationYearly: any;

  DiscountRedemptionWeeklyStatistics: any;
  DiscountRedemptionYearly: any;

  DiscountWeeklyStatistics: any;
  DiscountYearly: any;


  BroadcastMessagesWeeklyStatistics: any;
  BroadcastMessagesYearly: any;

  BroadcastMessagesSentWeeklyStatistics: any;
  BroadcastMessagesSentYearly: any;

  selectedCard: string | null = 'discountRedemption';

  user: UserModel;


  constructor(private dashboardService: DashboardService, public configService: ConfigService, private CompanyService: CompanyService, private authService: AuthService) { }

  ngOnInit() {
    this.user = this.authService.getUserData();
    if (this.user.UserType != 1) {
      this.CompanyService.GetAllId(EndpointUsedInEnum.Coupon).subscribe((data) => {
        this.companies = data;
      });
    }
    // else if (this.user.UserType == 1) {
    //   this.CompanyService
    //     .GetCompanyByUser()
    //     .subscribe((data) => {
    //       this.CompanyId = data.Id;
    //     });
    // }
    this.loadData();
    this.years = TimeConverter.generateYears();
  }

  loadData() {
    this.config = this.configService.config;
    this.updateChartOptions();
    this.dashboardService.GetCompanyDetailsDashboard(this.CompanyId, this.Year).subscribe((data: any) => {
      var ResultContent = data.ResultContent;
      if (!ResultContent) {
        console.log('errorinContent')
        return;
      }
      this.CouponRedemptionWeeklyStatistics = ResultContent.CouponRedemptionStatistics.WeeklyStatistics;
      this.CouponWeeklyStatistics = ResultContent.CouponStatistics.WeeklyStatistics;
      this.CouponsSalesEstimationWeeklyStatistics = ResultContent.CouponSalesStatistics.WeeklyStatistics;
      this.DiscountSalesWeeklyStatistics = ResultContent.DiscountSalesStatistics.WeeklyStatistics;
      this.DiscountRedemptionWeeklyStatistics = ResultContent.DiscountRedemptionStatistics.WeeklyStatistics;
      this.DiscountWeeklyStatistics = ResultContent.DiscountStatistics.WeeklyStatistics;
      this.BroadcastMessagesWeeklyStatistics = ResultContent.DiscountRedemptionStatistics.WeeklyStatistics;
      this.BroadcastMessagesSentWeeklyStatistics = ResultContent.DiscountRedemptionStatistics.WeeklyStatistics;
      //Yearly
      this.DiscountRedemptionYearly = this.dashboardService.formatBarData(ResultContent.DiscountRedemptionStatistics.YearlyTotals);
      this.DiscountYearly = this.dashboardService.formatBarData(ResultContent.DiscountStatistics.YearlyTotals);
      this.DiscountsSalesEstimationsYearly = this.dashboardService.formatBarData(ResultContent.DiscountSalesStatistics.YearlyTotals);
      this.CouponsYearly = this.dashboardService.formatBarData(ResultContent.CouponStatistics.YearlyTotals);
      this.CouponsSalesEstimationYearly = this.dashboardService.formatBarData(ResultContent.CouponSalesStatistics.YearlyTotals);
      this.CouponsRedemptionYearly = this.dashboardService.formatBarData(ResultContent.CouponRedemptionStatistics.YearlyTotals);

      this.loading = false;
    });

    this.lineData = {
      labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],
      datasets: [
        {
          label: 'First Dataset',
          data: [65, 59, 80, 81, 56, 55, 40],
          fill: false,
          backgroundColor: '#a0d2fa',
          borderColor: '#a0d2fa',
          tension: .4
        },
        {
          label: 'Second Dataset',
          data: [28, 48, 40, 19, 86, 27, 90],
          fill: false,
          backgroundColor: '#1c80cf',
          borderColor: '#1c80cf',
          tension: .4
        }
      ]
    };
    this.BroadcastMessagesSentYearly = this.BroadcastMessagesYearly = this.lineData;
    this.pieData = {
      labels: ['A', 'B', 'C'],
      datasets: [
        {
          data: [300, 50, 100],
          backgroundColor: [
            "#009688",
            "#3f51b5",
            "#9c27b0"
          ],
          hoverBackgroundColor: [
            "#009688",
            "#3f51b5",
            "#9c27b0"
          ]
        }
      ]
    };
    this.barData = {
      labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],
      datasets: [
        {
          label: 'My First dataset',
          backgroundColor: '#1c80cf',
          borderColor: '#1c80cf',
          data: [65, 59, 80, 81, 56, 55, 40]
        },
        {
          label: 'My Second dataset',
          backgroundColor: '#a0d2fa',
          borderColor: '#a0d2fa',
          data: [28, 48, 40, 19, 86, 27, 90]
        }
      ]
    };
  }

  selectCard(cardName: string): void {
    this.selectedCard = cardName;
  }

  changeCompany() {
    this.CompanyId = this.Company ? this.Company.Id : null;
    this.loading = true;
    this.loadData();
  }

  changeYear() {
    this.loading = true;
    this.loadData();
  }

  // THEME 
  updateChartOptions() {
    if (this.config.dark) {
      this.applyDarkTheme();
    } else {
      this.applyLightTheme();
    }
  }

  applyDarkTheme() {

    this.barOptions = {
      responsive: true,
      indexAxis: 'y',
      plugins: {
        legend: {
          labels: {
            color: '#ebedef'
          }
        }
      },
      scales: {
        x: {
          ticks: {
            color: '#ebedef'
          },
          grid: {
            color: 'rgba(160, 167, 181, .3)',
          }
        },
        y: {
          ticks: {
            color: '#ebedef'
          },
          grid: {
            color: 'rgba(160, 167, 181, .3)',
          }
        },
      }
    };
    this.ColumnOptions = {
      responsive: true,
      indexAxis: 'x',
      plugins: {
        legend: {
          labels: {
            color: '#ebedef'
          }
        }
      },
      scales: {
        x: {
          ticks: {
            color: '#ebedef'
          },
          grid: {
            color: 'rgba(160, 167, 181, .3)',
          }
        },
        y: {
          ticks: {
            color: '#ebedef'
          },
          grid: {
            color: 'rgba(160, 167, 181, .3)',
          }
        },
      }
    };
    this.pieOptions = {
      plugins: {
        legend: {
          labels: {
            color: '#ebedef'
          }
        }
      }
    };
  }

  applyLightTheme() {
    this.barOptions = {
      indexAxis: 'y',
      responsive: true,
      plugins: {
        legend: {
          labels: {
            fontColor: '#A0A7B5'
          }
        }
      },
      scales: {
        x: {
          ticks: {
            color: '#A0A7B5'
          },
          grid: {
            color: 'rgba(160, 167, 181, .3)',
          }
        },
        y: {
          ticks: {
            color: '#A0A7B5'
          },
          grid: {
            color: 'rgba(160, 167, 181, .3)',
          }
        },
      }
    };
    this.ColumnOptions = {
      indexAxis: 'x',
      responsive: true,
      plugins: {
        legend: {
          labels: {
            fontColor: '#A0A7B5'
          }
        }
      },
      scales: {
        x: {
          ticks: {
            color: '#A0A7B5'
          },
          grid: {
            color: 'rgba(160, 167, 181, .3)',
          }
        },
        y: {
          ticks: {
            color: '#A0A7B5'
          },
          grid: {
            color: 'rgba(160, 167, 181, .3)',
          }
        },
      }
    };
    this.pieOptions = {
      plugins: {
        legend: {
          labels: {
            color: '#495057'
          }
        }
      }
    };
  }
}
