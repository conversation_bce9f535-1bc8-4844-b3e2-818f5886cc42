<!-- <p-toast></p-toast> -->
<div>

      <h3>
            <p-button icon="pi pi-arrow-left" styleClass="p-button-rounded p-button-secondary p-button-text"
                  routerLink="/subscriptions">
            </p-button>
            {{title}}
      </h3>
      <!-- <p-messages *ngIf="this.newSubscription.HasError" [value]="msgs"></p-messages> -->
      <!-- <p-tabView [activeIndex]="1"> -->
      <p-tabView [(activeIndex)]="activeIndex" (onChange)="onTabChange($event)">
            <p-tabPanel header="Main Info" [ngStyle]="{ color: 'var(--cyan-300-color)' }">
                  <p-card role="region">
                        <subscription-main-info [(_SubscriptionData)]="newSubscription" [(activeIndex)]="activeIndex"
                              [isEditing]="isEditing"></subscription-main-info>
                  </p-card>
            </p-tabPanel>
            <p-tabPanel header="Subscription Statistics" [disabled]="tabsDisabled" *ngIf="isEditing">
                  <p-card role="region">
                        <subscription-statistics [(_SubscriptionData)]="newSubscription" [(activeIndex)]="activeIndex"></subscription-statistics>
                  </p-card>
            </p-tabPanel>

      </p-tabView>
</div>