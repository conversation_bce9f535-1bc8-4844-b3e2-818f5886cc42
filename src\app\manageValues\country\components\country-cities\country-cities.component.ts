import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { ConfirmationDialogComponent } from 'src/app/components/confirmation-dialog/confirmation-dialog.component';
import { CityModel } from 'src/app/Model/CityModel';
import { Country } from 'src/app/Model/Country';

@Component({
  selector: 'app-country-cities',
  templateUrl: './country-cities.component.html',
  styleUrls: ['./country-cities.component.scss'],

})
export class CountryCitiesComponent implements OnInit {
  @ViewChild('confirmationDialog') confirmationDialog: ConfirmationDialogComponent;
  confirmationAction: string = '';
  OverrideWarning: boolean = false;

  @Input() _CountryData: Country;
  @Input() isEditing: boolean;
  @Input() activeIndex: number;
  @Output() activeIndexChange: EventEmitter<number> = new EventEmitter();
  pressedNext = false;
  cityForm = new FormGroup({
    EnName: new FormControl('', [Validators.required, Validators.minLength(3)]),
    ArName: new FormControl('', [Validators.required, Validators.minLength(3)]),
  });

  routeState: any;
  enableEdit: boolean = false;
  _City: CityModel = new CityModel();
  pressedSave = false;

  constructor(private ref: ChangeDetectorRef,
    private router: Router, private messageService: MessageService,) {
    if (this.router.getCurrentNavigation()?.extras.state) {
      this.routeState = this.router.getCurrentNavigation()?.extras.state;
      if (this.routeState) {
        this._CountryData = this.routeState.data ? this.routeState.data : new Country();
        this._CountryData.Cities = this.routeState.data.Cities ? this.routeState.data.Cities : [];

      }
    }
  }

  ngOnInit(): void {
  }

  addToCities() {
    this.pressedSave = true;
    if (!this.cityForm.valid) {
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Check your input' });
      return;
    }
    if (!this._CountryData.Cities) {
      this._CountryData.Cities = [];
    }
    var x = 0;
    if (this._CountryData.Cities.length > 0) {
      x = this._CountryData.Cities.reduce((a, b) => a.RowId > b.RowId ? a : b).RowId;
    }

    // first Inti To x when new added 
    if (x == undefined) this._City.RowId = 1; else
      this._City.RowId = ++x;
    this._City.EnName = this._City.EnName != null ? this._City.EnName : '';
    this._City.ArName = this._City.ArName != null ? this._City.ArName : '';
    this._CountryData.Cities.push(this._City)

    this._City = new CityModel();
    this.cityForm.reset();
    this.pressedSave = false;
    // this.ref.detectChanges();
  }

  next() {
    if (this._CountryData.Cities.length == 0) {
      this._CountryData.HasError = true;
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please Enter One city at least' });
      return;
    }
    else {
      this.pressedNext = true;
      this._CountryData.HasError = false;
      // The activeIndex was set to a direct value because during editing, the navigate tabs, a problem in navigation when pressing the buttons (next, back).
      this.activeIndex = 2;
      this.activeIndexChange.emit(this.activeIndex);
    }

  }

  back() {
    // The activeIndex was set to a direct value because during editing, the navigate tabs, a problem in navigation when pressing the buttons (next, back).
    this.activeIndex = 0;
    this.activeIndexChange.emit(this.activeIndex);
  }

  showSelectedcity(city: CityModel) {
    // Scroll to the top of the page
    window.scrollTo({ top: 0, behavior: 'smooth' });
    this.enableEdit = true;

    if (city.Id == null)
      this._City = JSON.parse(JSON.stringify(this._CountryData.Cities.find((x) => x.RowId == city.RowId)));
    else
      this._City = JSON.parse(JSON.stringify(this._CountryData.Cities.find((x) => x.Id == city.Id)));

    setTimeout(() => {
    }, 1000);
  }

  editCity() {

    this.ref.detectChanges();
    this._City.EnName = this._City.EnName != null ? this._City.EnName : '';
    this._City.ArName = this._City.ArName != null ? this._City.ArName : '';

    this._CountryData.Cities[this._CountryData.Cities.findIndex((x) => x.Id == this._City.Id)] = this._City;
    this.ref.detectChanges();
    this.enableEdit = false;
    this._City = new CityModel();
    // Mark all controls as Untouched to turnoff trigger validation
    this.cityForm.markAsUntouched();
    this.cityForm.reset();
    this.ref.detectChanges();
  }


  deleteSelectedCity(city: CityModel) {
    // Assuming cityes is your array of cityes
    const cityIndex = this._CountryData.Cities.findIndex(city => city.Id === city.Id);
    // Check if the city to delete is the last item
    const isLastCity = cityIndex === this._CountryData.Cities.length - 1;
    if (city.MallsNum != 0 || city.CompanyBranchesNum != 0) { 
      this.messageService.add({
        severity: "error",
        summary: "Delete city",
        detail: "You cann't delete this City: " + city.EnName + " because it related with Malls or Company Branches",
        life: 5000
      });
      return;
    }
    if (isLastCity) {
      this.messageService.add({
        severity: "error",
        summary: "Delete city",
        detail: "The last city cannot be deleted. ",
      },);
      return;
    }
    // Method to show the confirmation dialog
    this.confirmationDialog.message = 'Do you want to delete this city ' + city.EnName;
    this.confirmationDialog.item = city;
    this.confirmationDialog.openDialog();
    this._City = city;
    this.confirmationAction = 'delete';

  }

  // Method to handle the confirmation result
  handleConfirmationAction(result: boolean): void {
    if (this.confirmationAction == 'delete') {
      if (result) {
        if (this._City.RowId) {
          this._CountryData.Cities = this._CountryData.Cities.filter(currentItem => currentItem.RowId != this._City.RowId);
          this._City = new CityModel();
          this.OverrideWarning = false;
          this.confirmationAction = '';
        } else if (this._City.Id != null || this._City.Id != undefined) {
          this._CountryData.Cities = this._CountryData.Cities.filter(currentItem => currentItem.Id != this._City.Id);
          this._City = new CityModel();
          this.OverrideWarning = false;
          this.confirmationAction = '';
        }
      } else {
        this.confirmationAction = '';
        this._City = new CityModel();
      }

    }
  }

  CheckValid(input: FormControl) {
    if (input.invalid && (this.pressedSave || input.touched)) {
      return 'red';
    }
    return '#515C66';
  }

  confirmDeletecity(city: CityModel) {

    if (city.Id == null || city.Id == undefined) {
      const index = this._CountryData.Cities.indexOf(city, 0);
      if (index > -1) {
        this._CountryData.Cities.splice(index, 1);
        this.ref.detectChanges();

      }
      this._City = new CityModel();
    }
    else {
      // this.companyService.DeleteCompanycity(city.Id, true).subscribe((data) => {
      //   if (data['HasError'] == false) {
      //     const index = this._CountryData.Cities.indexOf(city, 0);
      //     if (index > -1) {
      //       this._CountryData.Cities.splice(index, 1);
      //       this.ref.detectChanges();

      //     }
      //     this._City = new CityModel();
      //     //  this.deletecityDialog = false;

      //   } else {
      //     this.confirmationDialog.item = this._City;
      //     this.confirmationDialog.openDialog();
      //     this.confirmationDialog.message = data['EnErrorMessage'];
      //   }
      // });
    }

  }


}
