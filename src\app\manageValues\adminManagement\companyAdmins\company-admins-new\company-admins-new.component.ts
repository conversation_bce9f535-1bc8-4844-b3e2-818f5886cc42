import { ChangeDetectorRef, Component, ElementRef, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { Password } from 'primeng/password';
import { MenuService } from 'src/app/app.menu.service';
import { AdminType } from 'src/app/enum/admin-type';
import { CompanyAdminPageEnum, CompanyAdminPageEnumIconMap } from 'src/app/enum/company-admin-pages-enum';
import { CompanyBranchModel } from 'src/app/Model/CompanyBranchModel';
import { CompanyModel } from 'src/app/Model/CompanyModel';
import { UserModel } from 'src/app/Model/UserModel';
import { AuthService } from 'src/app/services/auth.service';
import { CompanyAdminService } from 'src/app/services/company-admin.service';
import { CompanyService } from 'src/app/services/company.service';
import { UserService } from 'src/app/services/user-service.service';
import { EmailValidator } from 'src/app/validators/email.validators';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'company-admins-new',
  templateUrl: './company-admins-new.component.html',
  styleUrls: ['./company-admins-new.component.scss']
})
export class CompanyAdminsNewComponent {
  @ViewChild('PasswordInput') PasswordInput: Password;
  @ViewChild('emailInput') emailInput: ElementRef;
  CompanyAdmin: UserModel = new UserModel();
  pressedSave: boolean = false;
  isEditing: boolean = false;
  title: string = 'Add New Company Admin';
  routeState: any;
  fileToUpload: any[] = [];
  companies: CompanyModel[] = [];
  companyProfileLogo: any;
  selectedCompany: CompanyModel = new CompanyModel();
  companyBranchs: CompanyBranchModel[] = [];
  currentUser: UserModel;
  CompanyAdminForm = new FormGroup({
    Name: new FormControl('', [Validators.required, Validators.minLength(3)]),
    Email: new FormControl('', [Validators.required, EmailValidator.isValidEmailFormat]),
    Password: new FormControl('', [Validators.required, Validators.pattern('(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[$@$!%*?&])[A-Za-z\d$@$!%*?&].{8,}')]),
    AdminType: new FormControl('', [Validators.required]),
    Company: new FormControl('', [Validators.required]),
    AdministeredBranches: new FormControl(''),
    AdministeredPages: new FormControl(''),
    ProfilePhotoUrl: new FormControl('',),
  });
  adminEmail: string = '';
  ProfilePhotoUrl: any = '';
  adminTypes = [];
  pagesList: { name: string, value: number }[] = [];
  CompanyAdminPageEnum = CompanyAdminPageEnum;
  CompanyAdminPageEnumIconMap = CompanyAdminPageEnumIconMap;

  constructor(private companyService: CompanyService, private messageService: MessageService, private CompanyAdminService: CompanyAdminService, private fb: FormBuilder, private router: Router, private ref: ChangeDetectorRef, private route: ActivatedRoute, private sanitizer: DomSanitizer, private userService: UserService, private authService: AuthService, private menuService: MenuService) {

    if (this.router.getCurrentNavigation()?.extras.state) {

      this.routeState = this.router.getCurrentNavigation()?.extras.state;
      if (this.routeState) {
        this.CompanyAdmin = this.routeState.data
          ? this.routeState.data
          : new UserModel();
        if (this.CompanyAdmin.Id == "") {
          this.isEditing = false;
        }
        else {
          this.isEditing = true;
        }
      }
    }
    if (this.route.snapshot.url[this.route.snapshot.url.length - 1].path === 'company-admin-edit') {
      this.isEditing = true;
      this.title = 'Edit Company Admin';
    }

  }

  ngOnInit() {
    this.currentUser = this.authService.getUserData();
    if (this.isEditing) {
      this.CompanyAdminForm.controls.Password.clearValidators();
      this.adminEmail = this.CompanyAdmin.Email;
    } else {
      setTimeout(() => {
        this.CompanyAdminForm.controls.Password.reset()
        this.CompanyAdminForm.reset()
      }, 900);
    }
    if (this.isEditing == true && this.CompanyAdmin.Id == "") {
      this.router.navigate(['/company-admins-management']);
      return;
    }

    if (this.CompanyAdmin.ProfilePhotoUrl) {
      this.ProfilePhotoUrl = this.CompanyAdmin.ProfilePhotoUrl ? this.sanitizer.bypassSecurityTrustUrl(environment.imageSrc + this.CompanyAdmin.ProfilePhotoUrl) : '';
    }

    if (this.currentUser.UserType == 1) {
      this.CompanyAdminForm.controls.Company.clearValidators();
    }
    else {
      this.companyService.GetAllId().subscribe((data) => {
        this.companies = data;
        if (this.CompanyAdmin.Company) { // edit Discount
          this.selectedCompany = this.companies.find(x => x.Id == this.CompanyAdmin.Company.Id);
          this.CompanyAdmin.CompanyId = this.CompanyAdmin.Company.Id;
        }
        this.companyBranchs = this.selectedCompany.CompanyBranches;
      });
    }
    if (this.CompanyAdmin.Company && this.CompanyAdmin.Company.LogoUrl) {
      this.companyProfileLogo = environment.imageSrc + this.CompanyAdmin.Company.LogoUrl;
    }

    this.adminTypes = Object.keys(AdminType)
      .filter(key => isNaN(Number(key)))  // Filter out numeric keys (enum reverse keys)
      .map(key => ({
        label: key,
        value: AdminType[key]
      }));
    this.initializePagesList()

  }

  save() {
    // this.onEmailInputFocusOut();

    this.pressedSave = true;
    // Mark all controls as touched to trigger validation
    this.CompanyAdminForm.markAllAsTouched();

    if (this.isEditing) {
      if (this.passwordControl.value && this.passwordControl.value.trim() !== '') {
        this.passwordControl.setValidators([Validators.required, Validators.pattern('(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[$@$!%*?&])[A-Za-z\d$@$!%*?&].{8,}')])
        // this.passwordControl.setErrors({ 'ErrorRequired': true });
      }
    }
    if (!this.ProfilePhotoUrl) {
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please Upload Profile Photo !' });
      return;
    }
    if (this.CompanyAdminForm.getError('EmailError') || this.CompanyAdminForm.getError('ErrorRequired')) {
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'This email is already used. Please enter another email' });
      return;
    }
    if (this.CompanyAdmin.AdminType == 1 && (this.CompanyAdmin.AdministeredPages.length == 0 || this.CompanyAdmin.AdministeredBranches.length == 0))//sub Admin
    {
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Check your input' });
      return;
    }
    if (!this.CompanyAdminForm.valid) {
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Check your input' });
      return;
    } else {
      if (this.CompanyAdmin.AdminType == 0)// SuperAdmin
      {
        this.CompanyAdmin.AdministeredCountries = [];
        this.CompanyAdmin.AdministeredPages = [];
      }

      let form: FormData = new FormData();
      if (this.CompanyAdmin.ProfilePhoto)
        form.append('ProfilePhoto', this.CompanyAdmin.ProfilePhoto);
      delete this.CompanyAdmin.ProfilePhotoUrl;
      delete this.CompanyAdmin.ProfilePhoto;
      delete this.CompanyAdmin.CompanyId;
      delete this.CompanyAdmin.MaxServiceDiscount;
      if (this.CompanyAdmin.Company) {
        const attributesToKeepInCompany = ['Id', 'EnName', 'ArName'];
        // Delete attributes not in attributesToKeepInCompany
        Object.keys(this.CompanyAdmin.Company).forEach((key) => {
          if (!attributesToKeepInCompany.includes(key)) {
            delete this.CompanyAdmin.Company[key];
          }
        });
      };

      form.append('request', JSON.stringify(this.CompanyAdmin));


      if (this.isEditing) {

        this.CompanyAdminService.EditCompanyAdmin(form).subscribe(data => {
          if (data.HasError) {
            console.log('post result', data);
          }
          else {
            if (this.adminEmail == this.currentUser.Email) {
              this.authService.updateCurrentUser(data.ResultContent.Email, data.ResultContent.Name, 1, data.ResultContent.AdminType, data.ResultContent.AdministeredPages, data.ResultContent.ProfilePhotoUrl);
              // Notify the side menu to refresh
              this.menuService.notifyMenuRefresh();
              // this.router.navigate(['/']);
            }
            this.router.navigate(['company-admins-management'])
          }
        });
      }
      else {
        this.CompanyAdminService.AddCompanyAdmin(form).subscribe((data) => {
          if (data.HasError) {
            console.log('post result', data);
          }
          else {
            this.router.navigate(['company-admins-management'])
          }
        })
      }
    }
  }

  initializePagesList() {
    this.pagesList = Object.keys(CompanyAdminPageEnum)
      .filter(key => isNaN(Number(key))) // Exclude numeric keys from reverse mapping
      .map(key => ({ name: key, value: CompanyAdminPageEnum[key] }));
  }

  get columns() {
    const mid = Math.ceil(this.pagesList.length / 2);
    return [this.pagesList.slice(0, mid), this.pagesList.slice(mid)];
  }

  uploadFile(files: any) {

    if (files.length === 0) {
      return;
    }
    var file = <File>files[0];
    const reader = new FileReader();
    this.CompanyAdmin.ProfilePhoto = file;
    reader.readAsDataURL(file);
    reader.onload = (e: any) => {
      this.ProfilePhotoUrl = e.target.result;
    }
    this.fileToUpload.push(file);
    this.ProfilePhotoUrl = URL.createObjectURL(this.fileToUpload[0]);
  }


  clearFileInput() {
    this.fileToUpload = [];
    this.ProfilePhotoUrl = '';
  }

  // Optional method to check password control errors programmatically
  get passwordControl() {
    return this.CompanyAdminForm.get('Password');
  }

  onEmailInputFocusOut(): void {
    // Your logic here, triggered when the input field loses focus
    var data = {
      'Email': this.CompanyAdmin.Email,
    };
    if (!this.isEditing) {
      if (this.CompanyAdmin.Email != null)
        this.userService.CheckUserEmailExist(data)
          .subscribe((data) => {
            if (data['HasError'] == true) {
              // this.messageService.add({ severity: 'error', summary: 'Error', detail: data['EnErrorMessage'] });
              this.CompanyAdminForm.get('email').setErrors({ 'EmailError': true });

            }
          });
    } else {
      if (this.adminEmail != this.CompanyAdmin.Email && this.isEditing)
        this.userService.CheckUserEmailExist(data)
          .subscribe((data) => {
            if (data['HasError'] == true) {
              // this.messageService.add({ severity: 'error', summary: 'Error', detail: data['EnErrorMessage'] });
              this.CompanyAdminForm.get('email').setErrors({ 'EmailError': true });
            }
          });
    }
  }

  CheckValid(input: FormControl) {
    if (input.invalid && (this.pressedSave || input.touched)) {
      return 'red';
    }
    return '#515C66';
  }

  refreshCompanyRelatedDetails() {
    let selectedCompany = this.companies.find(x => x.Id == this.CompanyAdmin.CompanyId);
    this.CompanyAdmin.CompanyId = selectedCompany.Id;
    this.CompanyAdmin.Company = selectedCompany;
    this.companyBranchs = selectedCompany.CompanyBranches;
    // this.companyProfileLogo  = environment.imageSrc + selectedCompany.LogoUrl.split("wwwroot\\")[1];
    this.companyProfileLogo = selectedCompany.LogoUrl ? this.sanitizer.bypassSecurityTrustUrl(environment.imageSrc + selectedCompany.LogoUrl) : '';
  }
}
