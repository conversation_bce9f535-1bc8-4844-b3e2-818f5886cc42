import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { Dto } from '../Model/Dto';
import { map } from 'rxjs';
import { LanguageModel } from '../Model/LanguageModel';

@Injectable({
  providedIn: 'root'
})
export class LanguageService {



  constructor(private httpClient: HttpClient) {

  }

  GetAllLanguages() {
    return this.httpClient.get<Dto<LanguageModel>>(`${environment.apiUrl}` + 'Language/GetAll')
      .pipe(
        map((res: any) => {
            return res.ListResultContent;
        })
      );
  }
}
