import { <PERSON>ttp<PERSON><PERSON><PERSON>, <PERSON><PERSON>p<PERSON><PERSON><PERSON>, HttpInterceptor, } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { Dto } from '../Model/Dto';
import { TranslateService } from '@ngx-translate/core';

@Injectable()
export class ApiTokenInterceptor implements HttpInterceptor {
  hold: any;
  blacklist: Array<string>;
  unhandelexp: Dto<string>;

  constructor(private translateService: TranslateService) {
    this.blacklist = [
    ];
  }

  intercept(request: HttpRequest<unknown>, next: HttpHandler): any {
    if (request.headers.get('content-type') == 'application/json') {
      request = request.clone({
        headers: request.headers.set('Accept', 'application/json')
      });
      request = request.clone({
        headers: request.headers.set('content-type', 'application/json')
      });
    }
        if (this.isValidRequestForInterceptor(request.url)) {
      const token = localStorage.getItem('token');
      // Get language from localStorage first, then from translation service, default to 'en'
      const lang = localStorage.getItem('lang') || this.translateService.currentLang || 'en';

      let headers: any = {};

      if (token) {
        headers.Authorization = 'bearer ' + token;
      }

      // Add language header
      headers['lang'] = lang;

      request = request.clone({
        setHeaders: headers
      });
    }
    return next.handle(request);
  }



  private isValidRequestForInterceptor(requestUrl: string): boolean {
    let positionIndicator: string = `${environment.apiUrl}/`;
    const position = requestUrl.indexOf(positionIndicator);
    if (position > 0) {
      const destination: string = requestUrl.substr(position + positionIndicator.length);
      for (const address of this.blacklist) {
        if (new RegExp(address).test(destination)) {
          return false;
        }
      }
    }
    return true;
  }
}
