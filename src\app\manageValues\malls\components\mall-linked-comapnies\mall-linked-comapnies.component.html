<p-table [value]="_MallData.AssociatedCompanies" selectionMode="single"
[paginator]="true" [rows]="5" responsiveLayout="scroll">
<ng-template pTemplate="header">
      <tr>
            <th pSortableColumn="name">Company Name<p-sortIcon
                        field="name"></p-sortIcon></th>
            <th pSortableColumn="name">Company Branches</th>
      </tr>
</ng-template>
<ng-template pTemplate="body" let-rowData let-company>
      <tr [pSelectableRow]="rowData">
            <td style="min-width: 10rem;">
                  <img width="60"
                        [src]="
                  companyProfileLogo(company.LogoUrl)| placeholder: 'assets/pages/icons/company.png' "
                        alt="company.EnName" />
                  &nbsp;&nbsp;
                  {{company.EnName}}
            </td>
            <td style="min-width: 10rem;">
                  {{getBranchNames(company.CompanyBranches) }}
            </td>
      </tr>
</ng-template>
<ng-template pTemplate="emptymessage">
      <tr>
            <td class="text-center" colspan="7">
                  {{ "No Linked Companies." | translate }}
            </td>
      </tr>
</ng-template>
</p-table>