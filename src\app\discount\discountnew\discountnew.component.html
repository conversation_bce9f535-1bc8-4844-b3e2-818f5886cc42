<div>
    <h3>
        <p-button
            icon="pi pi-arrow-left"
            styleClass="p-button-rounded p-button-secondary p-button-text"
            (click)="goBack()"
        ></p-button>
        {{ title }}
    </h3>
    <p-tabView [(activeIndex)]="activeIndex">
        <p-tabPanel
            header="Main info"
            [ngStyle]="{ color: 'var(--cyan-300-color)' }"
        >
            <p-card role="region">
                <app-main-info
                    [_discountData]="_discountData"
                    [(activeIndex)]="activeIndex"
                    [editing]="editing"
                    [(fileToUpload)]="fileToUpload"
                ></app-main-info>
            </p-card>
        </p-tabPanel>
        <p-tabPanel header="Timing & Details">
            <p-card role="region">
                <app-timing-details
                    [_discountData]="_discountData"
                    [(activeIndex)]="activeIndex"
                    [editing]="editing"
                ></app-timing-details>
            </p-card>
        </p-tabPanel>
    </p-tabView>
</div>
