<div>
    <p-confirmDialog header="Confirmation" key="confirm1" icon="pi pi-exclamation-triangle"
        message="Are you sure you want to proceed?" [style]="{ width: '350px' }" acceptButtonStyleClass="p-button-text"
        rejectButtonStyleClass="p-button-text"></p-confirmDialog>
    <p-toast></p-toast>
    <!-- <p-table #dt [value]="Coupons" dataKey="Id" [rowHover]="true" [rows]="10" [showCurrentPageReport]="true"
        responsiveLayout="scroll" [rowsPerPageOptions]="[10, 25, 50]" [loading]="loading" [paginator]="true"
        currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries" [filterDelay]="0"
        styleClass="p-datatable-gridlines "> -->
    <p-table #dt [value]="Coupons" [paginator]="paginator" [rows]="pageSize" [totalRecords]="totalRecords" [lazy]="true"
        [loading]="loading" (onLazyLoad)="loadData($event)" [showCurrentPageReport]="true"
        [currentPageReportTemplate]="'Showing {first} to {last} of {totalRecords} records'" responsiveLayout="scroll"
        [rowHover]="true" styleClass="p-datatable-gridlines">

        <ng-template pTemplate="caption">
            <app-grid-headers [myDt]="dt" addNewTxt="Add New Coupon" goRoute="/coupon-new" gridTitle="Coupons"
                [title]="title" (SearchEvent)="ReceivedFilteredData($event)"></app-grid-headers>
        </ng-template>

        <ng-template pTemplate="header">
            <tr>
                <th>
                    <div class="flex justify-content-between align-items-center">
                        {{ "Ad ID" | translate }}
                    </div>
                </th>
                <th *ngIf="user.UserType == 0">
                    <div class="flex justify-content-between align-items-center">
                        {{ "Company" | translate }}
                    </div>
                </th>
                <th>
                    <div class="flex justify-content-between align-items-center">
                        {{ "Title" | translate }}
                    </div>
                </th>
                <th>
                    <div class="flex justify-content-between align-items-center">
                        {{ "Value" | translate }}
                    </div>
                </th>
                <th>
                    <div class="flex justify-content-between align-items-center">
                        {{ "Start Date" | translate }}
                    </div>
                </th>
                <th>
                    <div class="flex justify-content-between align-items-center">
                        {{ "End Date" | translate }}
                    </div>
                </th>
                <th>
                    <div class="flex justify-content-between align-items-center">
                        {{ "Updated Date" | translate }}
                    </div>
                </th>

                <!-- <th pSortableColumn="lastAction">
                    <div class="flex justify-content-between align-items-center">
                        {{ "Last Action" | translate }}
                        <p-sortIcon field="lastAction"></p-sortIcon>
                    </div>
                </th> -->
                <th>
                    <div class="flex justify-content-between align-items-center">
                        {{ "Targeted User Num" | translate }}
                    </div>
                </th>
                <th>
                    <div class="flex justify-content-between align-items-center">
                        {{ "Status" | translate }}
                    </div>
                </th>

                <th>

                    <div class="flex justify-content-between align-items-center">
                        {{ "Edit" | translate }}
                    </div>
                </th>
                <!-- <th style="width: 5rem"></th> -->
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-coupon>
            <!-- {{ coupon | json }} -->
            <tr  [ngClass]="{'inactive-table-row': coupon.Active === false}">
                <td>
                    <span class="p-column-title" style="text-align: center">ID</span>
                    <div class="d-flex align-items-center">
                        <p-inputSwitch *ngIf="canActiveCoupon(coupon)" [(ngModel)]="coupon.Active"
                            (onChange)="changeCouponLastActionLog(coupon.Id, coupon.Active)"></p-inputSwitch>
                        <span class="ms-2">{{ coupon.UserFriendly }}</span>
                        <br />
                    </div>
                    <i class="pi pi-info-circle" (click)="showDialog(coupon)"></i>
                </td>

                <td *ngIf="user.UserType == 0">
                    <span class="p-column-title">Company</span>
                    <p-avatar *ngIf="coupon.Company.LogoUrl" image="{{imageSrc + coupon.Company.LogoUrl}}"
                        styleClass="mr-2" size="medium" shape="circle"></p-avatar>
                    <!-- <p-avatar *ngIf="coupon.Company.LogoUrl" image="{{imageSrc + coupon.Company.LogoUrl.split('wwwroot\\')[1]}}"  styleClass="mr-2" size="xlarge" shape="circle"></p-avatar> -->
                    <!-- <p-avatar ref="noLogoCompany" [label]="coupon.Company.EnName[0]" image="https://primefaces.org/cdn/primeng/images/demo/avatar/amyelsner.png" styleClass="mr-2" size="large" [style]="{ 'background-color': '#2196F3', color: '#ffffff' }" shape="circle"></p-avatar> -->
                    {{ coupon.Company.EnName }}
                </td>

                <td>
                    <span class="p-column-title">Title</span>
                    <p-avatar *ngIf="coupon.ImageUrl" image="{{imageSrc + coupon.ImageUrl}}" styleClass="mr-2"
                        size="medium" shape="circle"></p-avatar>
                    {{ coupon.EnTitle }}
                </td>
                <td>
                    <span class="p-column-title">Coupon Value</span>
                    {{ coupon.CouponValue | CouponValue }}
                </td>
                <td>
                    <span class="p-column-title">Start Date</span>
                    {{ coupon.StartDate | date : "dd/MM/yyyy"}}
                </td>
                <td>
                    <span class="p-column-title">End Date</span>
                    {{ coupon.EndDate | date : "dd/MM/yyyy"}}
                </td>
                <td>
                    <span class="p-column-title">Updated Date</span>
                    {{ coupon.UpdatedDate | date : "dd/MM/yyyy"}}
                </td>
                <td>
                    <div class="flex">
                        {{ coupon.TargetedUserNum }}
                    </div>
                </td>
                <!-- <td style="min-width: 12rem;">

                    <span [ngClass]="iconForLastActionLog(coupon.CouponStatuses[0].Status)" class="me-2"
                        [ngStyle]="colorOfLastActionLog(coupon.CouponStatuses[0].Status)"> </span>

                    <span
                        [class]="'Coupon-action action-' + coupon.CouponStatuses[0].Status">{{ActionLogEnumName(coupon.CouponStatuses[0].Status)}}</span><br>
                    <span *ngIf="coupon.CouponStatuses[0].Admin?.Name!=null"> by
                        {{coupon.CouponStatuses[0].Admin?.Name}} </span>
                </td> -->

                <td style="min-width: 12rem;">
                    <div class="flex">
                        <span class="me-2" [ngStyle]="colorOfStatus(coupon.Status)"> </span>

                        <span [class]="'coupon-status status-' + coupon.Status">{{StatusName(coupon.Status)}}</span>
                    </div>
                <td>
                    <span class="p-column-title">Edit</span>
                    <div class="d-flex align-items-center justify-content-between">

                        <p-button [disabled]="!coupon.Active" *ngIf="canEditCoupon(coupon)" icon="pi pi-pencil"
                            styleClass="p-button-rounded" class="mx-1"
                            (click)="edit({Coupon: coupon, state: 'edit'})"></p-button>

                        <p-button *ngIf="user.UserType == 0" icon="pi pi-trash"
                            styleClass="p-button-rounded p-button-danger" class="mx-1"
                            (click)="Delete(coupon.Id)"></p-button>

                        <p-button *ngIf="user.UserType == 0" icon="pi pi-wrench" styleClass="p-button-rounded"
                            (click)="rejectOrAccept(coupon)" class="mx-1"></p-button>
                        <p-button *ngIf="user.UserType == 1" icon="pi pi-eye" styleClass="p-button-rounded"
                            (click)="rejectOrAccept(coupon)" class="mx-1"></p-button>
                    </div>
                </td>
            </tr>
            <p-dialog header="Coupon History" [(visible)]="coupon.VisibleDialog" [breakpoints]="{ '960px': '75vw' }"
                [style]="{ width: '50vw' }" [draggable]="false" [resizable]="true" [id]="coupon.Id">
                <table>
                    <tr *ngFor="let item of coupon.CouponLog">
                        <th>
                            <i [ngClass]="iconForLastActionLog(item!.Status)" class="me-2"
                                [ngStyle]="colorOfLastActionLog(item!.Status)"></i>
                        </th>
                        <th>
                            <span [class]="'coupon-action action- action-' + item.Status">
                                {{ item.Status | CouponLog }}
                            </span>
                        </th>
                        &nbsp;&nbsp;&nbsp;
                        <th>
                            {{ item.Admin?.Name }}
                        </th>
                        &nbsp;&nbsp;&nbsp;
                        <th>
                            {{
                            item.CreationDate
                            | localTime
                            }}
                            <!-- {{
                        item.CreationDate.toString()
                        | date : "dd/MM/yyyy - hh:mm"
                        }} -->
                        </th>
                        &nbsp;&nbsp;&nbsp;
                        <th>
                            {{ item.Note }}
                        </th>
                    </tr>
                </table>
            </p-dialog>
        </ng-template>
        <ng-template pTemplate="emptymessage">
            <tr>
                <td class="text-center" colspan="7">
                    {{ "No Coupon found." | translate }}
                </td>
            </tr>
        </ng-template>
    </p-table>
</div>