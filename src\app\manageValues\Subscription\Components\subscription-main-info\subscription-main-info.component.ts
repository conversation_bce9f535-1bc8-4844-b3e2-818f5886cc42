import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { SubscriptionModel } from 'src/app/Model/SubscriptionModel';
import { SubscriptionService } from 'src/app/services/subscription.service';

@Component({
  selector: 'subscription-main-info',
  templateUrl: './subscription-main-info.component.html',
  styleUrls: ['./subscription-main-info.component.scss']
})
export class SubscriptionMainInfoComponent {
  @Input() _SubscriptionData: SubscriptionModel;
  @Input() isEditing: boolean;
  @Input() activeIndex: number;
  @Output() activeIndexChange: EventEmitter<number> = new EventEmitter();
  pressedSave = false;

  SubscriptionMainInfoForm = new FormGroup({
    EnName: new FormControl('', [Validators.required, Validators.minLength(3)]),
    ArName: new FormControl('', [Validators.required, Validators.minLength(3)]),

  });

  constructor(private messageService: MessageService, private SubscriptionService: SubscriptionService, private router: Router) { }

  ngOnInit(): void { }

  save() {
    this.pressedSave = true;
    if (!this.SubscriptionMainInfoForm.valid) {
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Check your input' });
      return;
    }

    delete this._SubscriptionData.TotalCountries;
    delete this._SubscriptionData.TotalCompanies;
    delete this._SubscriptionData.CompanyStatistics;


    if (this._SubscriptionData.Id) {
      this.SubscriptionService.EditSubscription(this._SubscriptionData).subscribe(data => {
        if (data.HasError == true) {
          return;
        } else {
          this.router.navigate(['subscriptions']);
        }
      });
    }
    else {
      this.SubscriptionService.AddSubscription(this._SubscriptionData).subscribe((data) => {
        if (data.HasError == true) {
          return;
        }
        else {
          this.router.navigate(['subscriptions']);
        }
      })
    }
  }

  CheckValid(input: FormControl) {
    if (input.invalid && (this.pressedSave || input.touched)) {
      return 'red';
    }
    return '#515C66';
  }

}
