import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { TiersModel } from '../Model/TiersModel';
import { Observable, catchError, map, of } from 'rxjs';
import { Dto } from '../Model/Dto';


@Injectable({
  providedIn: 'root'
})
export class TierService {

  constructor(private httpClient: HttpClient) { }

  getAllTiers(IsVip: boolean = false): Observable<TiersModel[]> {
    let params = new HttpParams();;
    if (IsVip) {
      params = params.set('IsVip', IsVip);
    }
    return this.httpClient.get<TiersModel[]>(`${environment.apiUrl}Tier/GetAll`, { params })
      .pipe(
        map((res: any) => {
          if (!res.HasError) {
            return res.ListResultContent;
          }
        })
      );
  }

  getAllTiersTable(page: number = null, pageSize: number = null, Name: string = null) {
    var http;
    var url = `${environment.apiUrl}Tier/getAllAdminTable`;
    // var params = {};
    // if (page != null) {
    let params = new HttpParams();;
    if (page) {
      params = params.set('PageNumber', page);
    }
    if (pageSize) {
      params = params.set('PageSize', pageSize);
    }
    if (Name) {
      params = params.set('Name', Name);
    }
    http = this.httpClient.get(url, { params });
    return http.pipe(
      map((res: Dto<TiersModel>) => {
        return res;

      }),
      catchError((error) => {
        return of(false);
      })
    );
  }
  getTierById(Id: string = "") {
    return this.httpClient.get<Dto<TiersModel>>(`${environment.apiUrl}Tier/GetTierById?Id=${Id}`)
      .pipe(
        map((res: any) => {
          return res['ResultContent'];
        })
      );
  }
  public AddTier(data): Observable<Dto<TiersModel>> {
    var http;
    var url = `${environment.apiUrl}Tier/AddTier`;
    http = this.httpClient.post(url, data);
    return http.pipe(
      map((res: Dto<TiersModel>) => {
        return res;

      }),
      catchError(error => {
        return of(false);
      }));
  }
  public EditTier(data): Observable<Dto<TiersModel>> {
    var http;
    var url = `${environment.apiUrl}Tier/EditTier`;
    http = this.httpClient.put(url, data);
    return http.pipe(
      map((res: Dto<TiersModel>) => {
        return res;
      }),
      catchError(error => {
        return of(false);
      }));
  }
  public DeleteTier(TierId: String): Observable<any> {
    var http;
    var url = `${environment.apiUrl}Tier/DeleteTier?Id=${TierId}`;
    http = this.httpClient.delete(url);
    return http.pipe(
      map((res: any) => {
        return res;
      }),
      catchError((error) => {
        return of(false);
      })
    );
  }
}
