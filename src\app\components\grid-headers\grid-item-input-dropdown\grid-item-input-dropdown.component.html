<div class="d-flex flex-column">
    <label class="fs-6 fw-bold">{{label}}</label>
    <span class="p-input-icon-left">
        <p-dropdown *ngIf="listType == 'Industry'" [editable]="true" [showClear]="true" [options]="industries"
            optionLabel="Name" (onChange)="onIndustrySelection($event)"></p-dropdown>

        <p-dropdown *ngIf="listType == 'Gender'" [editable]="true" [showClear]="true" [options]="genders"
            (onChange)="onGenderSelection($event)"></p-dropdown>

        <p-dropdown *ngIf="listType == 'Country'" [editable]="true" [showClear]="true" [options]="countries"
            optionLabel="EnName" (onChange)="onCountrySelection($event)">
            <ng-template let-option pTemplate="item">
                <img src="assets/demo/flags/flag_placeholder.png"
                    [class]="'flag flag-' + this.allCountries.getCodeFromName(option.EnName)"
                    alt="{{ option.EnName }}" /> &nbsp;
                <span>{{ option.EnName }}</span>
            </ng-template></p-dropdown>



        <p-dropdown *ngIf="listType == 'AdminType'" [editable]="true" [showClear]="true" [options]="adminTypes"
            (onChange)="onAdminTypeSelection($event)"></p-dropdown>

        <p-dropdown *ngIf="listType == 'Company'" [editable]="true" [showClear]="true" [options]="companies"
            optionLabel="EnName" (onChange)="onCompanySelection($event)"></p-dropdown>

        <p-dropdown *ngIf="listType == 'Tier'" [editable]="true" [showClear]="true" [options]="tiers"
            optionLabel="EnName" (onChange)="onTierSelection($event)">

            <ng-template let-tier pTemplate="item">
                <span class="p-badge" [style.backgroundColor]="tier.Color">{{tier.EnName}}</span>
            </ng-template>

            <ng-template let-tier pTemplate="selectedItem">
                <span *ngIf="tier" class="p-badge" [ngStyle]="{'background-color': tier.Color}">
                    {{tier.EnName}}
                </span>
                <span *ngIf="!tier">Select a Tier</span>
            </ng-template>

        </p-dropdown>

        <i class={{icon}}></i>
    </span>
</div>