import { Component, OnInit, ChangeDetectorRef, Input } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { CompanyBranchWorkHours } from 'src/app/Model/CompanyBranchWorkHours';
import { openCloseHourssValidator } from 'src/app/validators/open-close-hours.directive';

interface OpenHours {
    day: string;
    active: boolean;
    dayChar: string;
    hours: { open: string, close: string }[]
}

@Component({
    selector: 'app-open-hours',
    templateUrl: './open-hours.component.html',
    styleUrls: ['./open-hours.component.scss']
})
export class OpenHoursComponent implements OnInit {
    openHours: OpenHours[];
    @Input() workHours: CompanyBranchWorkHours[] = [];

    openHoursGroup: FormGroup;

    constructor(private fb: FormBuilder) { }
    /*openHoursGroup: FormGroup = new FormGroup({


        openTime: new FormControl(''),
        closeTime: new FormControl(''),
        openTime2: new FormControl(''),
        closeTime2: new FormControl(''),



    }, { validators: openCloseHourssValidator })*/

    ngOnInit(): void {

        // Initialize the form with an empty FormArray
        this.openHoursGroup = this.fb.group({
            items: this.fb.array([]),
        });

        // Add some form groups to the FormArray dynamically
        this.addFormGroups(this.workHours.length);
        // this.openHours = [
        //     { day: "Saturday", active: false, dayChar: 'S', hours: [{ open: '', close: '' }] },
        //     { day: "Sunday", active: false, dayChar: 'S', hours: [{ open: '', close: '' }] },
        //     { day: "Monday", active: false, dayChar: 'M', hours: [{ open: '', close: '' }] },
        //     { day: "Tuesday", active: false, dayChar: 'T', hours: [{ open: '', close: '' }] },
        //     { day: "Wednesday", active: false, dayChar: 'W', hours: [{ open: '', close: '' }] },
        //     { day: "Friday", active: false, dayChar: 'F', hours: [{ open: '', close: '' }] },
        // ];
    }

    get items() {
        return this.openHoursGroup.get('items') as FormArray;
    }
    addFormGroups(count: number) {
        for (let i = 0; i < count; i++) {
            // Create a FormGroup with specific controls
            const group = this.fb.group({
                openTime: new FormControl(''),
                closeTime: new FormControl(''),
                openTime2: new FormControl(''),
                closeTime2: new FormControl(''),
            }, { validators: openCloseHourssValidator });

            // Add the FormGroup to the FormArray
            this.items.push(group);
        }
    }
    show() {
        console.log(this.openHours);
    }
}
