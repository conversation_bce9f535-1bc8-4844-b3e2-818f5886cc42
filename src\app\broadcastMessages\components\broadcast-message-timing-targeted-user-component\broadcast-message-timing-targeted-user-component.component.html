<!-- <p>{{_BroadcastMessageData|json}}</p> -->
<p-toast></p-toast>
<!-- <div class="grid p-fluid">
      <div class="col-12 md:col-6">
            <p-card>
                  <div class="col-8 md:col-4">
                        <label [style.color]="CheckValid(timingTargetUsersForm.controls.TargetedTiers)"
                              for="TargetedTiers">Targeted Tiers</label>
                        <p-multiSelect selectedItemsLabel="No. of Selected Tiers: {0}"
                              [options]="_BroadcastMessageData.Tiers" [(ngModel)]="_BroadcastMessageData.TargetedTiers"
                              defaultLabel="Select Tiers" optionLabel="EnName" id="tiers"
                              formControlName="TargetedTiers" class="multiselect-custom">
                        </p-multiSelect>
                  </div>
            </p-card>
      </div>
</div> -->
<div [formGroup]="timingTargetUsersForm">

      <p-card>
            <div class="flex flex-column gap-2 mb-3">
                  <h4>Timing</h4>
                  <div class="col">
                        <p-checkbox binary="true" inputId="SendAsSoonAsPossible" formControlName="SendAsSoonAsPossible"
                              [(ngModel)]="_BroadcastMessageData.SendAsSoonAsPossible" (click)="checkASAP()"
                              id="SendAsSoonAsPossible" label="Send as soon as possible after getting approved">
                        </p-checkbox>
                  </div>

                  <div class="col-12 row" *ngIf="!_BroadcastMessageData.SendAsSoonAsPossible">
                        <div class="col-6">
                              <div class="flex flex-column gap-2 mb-3">
                                    <label [style.color]="CheckValid(timingTargetUsersForm.controls.LaunchDate)"
                                          for="LaunchDate">Launch Date</label>
                                    <input type="date" pInputText id="LaunchDate"
                                          [(ngModel)]="_BroadcastMessageData.LaunchDate" [min]="currentDate"
                                          [ngModel]="_BroadcastMessageData.LaunchDate | date : 'yyyy-MM-dd'  "
                                          class="w-50" formControlName="LaunchDate" />
                              </div>
                        </div>
                        <div class="col-6 grid gap-3">
                              <div class="flex flex-column gap-2 mb-3">
                                    <label for="LaunchStartTime">Launch time</label>
                                    <p-dropdown [options]="timeOptions" [(ngModel)]="selectedTimeSlot"
                                          [ngModelOptions]="{standalone: true}" placeholder="Select a time slot"
                                          [showClear]="true" (onChange)="onTimeSlotChange()" class="w-50">
                                    </p-dropdown>
                              </div>
                        </div>
                  </div>
            </div>
      </p-card>
      <br>

      <div class=" grid p-fluid">
            <div class="col-12 md:col-6">
                  <p-card>

                        <h4>Targeted Users</h4>

                        <div class="col-8  flex flex-column gap-2 mb-3">
                              <label [style.color]="CheckValid(timingTargetUsersForm.controls.TargetedTiers)"
                                    for="TargetedTiers">Targeted Tiers</label>
                              <p-multiSelect selectedItemsLabel="No. of Selected Tiers: {0}"
                                    [options]="_BroadcastMessageData.Tiers"
                                    [(ngModel)]="_BroadcastMessageData.TargetedTiers" defaultLabel="Select Tiers"
                                    optionLabel="EnName" id="tiers" formControlName="TargetedTiers"
                                    class="multiselect-custom">
                              </p-multiSelect>
                        </div>
                        <div class="col-4  flex flex-column gap-2 mb-3">
                              <label [style.color]="CheckValid(timingTargetUsersForm.controls.TargetedGender)"
                                    for="TargetedGender">Targeted Gender</label>
                              <p-dropdown [options]="genders" [(ngModel)]="_BroadcastMessageData.TargetedGender"
                                    [showClear]="true" placeholder="select Gender"
                                    formControlName="TargetedGender"></p-dropdown>

                        </div>

                        <div class="col-12 row">
                              <div class="col-6">
                                    <div class="flex flex-column gap-2 mb-3">
                                          <label [style.color]="CheckValid(timingTargetUsersForm.controls.TargetedMinAge)"
                                                for="TargetedMinAge">Targeted Min
                                                Age</label>
                                          <p-inputNumber id="TargetedMinAge"
                                                [(ngModel)]="_BroadcastMessageData.TargetedMinAge"
                                                formControlName="TargetedMinAge" mode="decimal"
                                                class="p-inputtext-sm w-50" [min]="0" [max]="100" />
                                    </div>
                              </div>
                              <div class="col-6">
                                    <div class="flex flex-column gap-2 mb-3">
                                          <label [style.color]="CheckValid(timingTargetUsersForm.controls.TargetedMaxAge)"
                                                for="TargetedMaxAge">Targeted Max
                                                Age</label>
                                          <p-inputNumber id="TargetedMaxAge"
                                                [(ngModel)]="_BroadcastMessageData.TargetedMaxAge"
                                                formControlName="TargetedMaxAge" mode="decimal"
                                                class="p-inputtext-sm w-50" [min]="0" [max]="100" />
                                    </div>
                              </div>

                              <div [style.color]="CheckValid(timingTargetUsersForm.controls.TargetedMinAge)"
                                    *ngIf="timingTargetUsersForm.hasError('invalidRange')">
                                    Max value must be greater than min value.
                              </div>
                        </div>
                        <!-- <p>{{_BroadcastMessageData.Countries|json}}</p> -->
                        <div class="col-12 row">
                              <div class="col-6">
                                    <div class="flex flex-column gap-2 mb-3">
                                          <label [style.color]="CheckValid(timingTargetUsersForm.controls.TargetedCountry)"
                                                for="TargetedCountry">Targeted
                                                Country</label>
                                          <p-dropdown [options]="_BroadcastMessageData.Countries"
                                                [(ngModel)]="_BroadcastMessageData.TargetedCountry"
                                                placeholder="select Country" (onChange)="selectCountry()"
                                                optionLabel="EnName" formControlName="TargetedCountry"></p-dropdown>
                                    </div>
                              </div>
                              <div class="col-6">
                                    <div class="flex flex-column gap-2 mb-3">
                                          <label [style.color]="CheckValid(timingTargetUsersForm.controls.TargetedCities)"
                                                for="TargetedCities">Targeted
                                                Cities</label>
                                          <p-multiSelect selectedItemsLabel="No. of Selected Cities: {0}"
                                                [options]="Cities" [(ngModel)]="_BroadcastMessageData.TargetedCities"
                                                defaultLabel="Select Cities" optionLabel="EnName" id="cities"
                                                formControlName="TargetedCities" class="multiselect-custom">
                                          </p-multiSelect>
                                    </div>
                              </div>
                        </div>

                        <div class="col-12 row">
                              <div class="col-8">
                                    <div class="flex flex-column gap-2 mb-3">
                                          <label [style.color]="CheckValid(timingTargetUsersForm.controls.TargetedNationalities)"
                                                for="TargetedNationalities">Targeted
                                                Nationalities</label>
                                          <p-multiSelect selectedItemsLabel="No. of Selected nationalities: {0}"
                                                [options]="nationalities"
                                                [(ngModel)]="_BroadcastMessageData.TargetedNationalities"
                                                defaultLabel="Select Nationalities" optionLabel="EnName"
                                                id="nationalities" formControlName="TargetedNationalities"
                                                class="multiselect-custom">
                                          </p-multiSelect>
                                    </div>
                              </div>
                        </div>
                  </p-card>
            </div>
      </div>

      <br />
      <div class="flex justify-content-end gap-2 mb-3">
            <p-button label="back" styleClass="p-button-outlined p-button-secondary" (click)="back()"></p-button>
            <p-button label="save" (onClick)="save()"></p-button>

      </div>
</div>