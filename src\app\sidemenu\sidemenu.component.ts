import { AfterViewInit, Component, OnInit, OnDestroy } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { MenuService } from '../app.menu.service';
import { UserType } from '../enum/user-type';
import { WebsiteAdminPageEnum } from '../enum/website-admin-pages-enum';
import { CompanyAdminPageEnum } from '../enum/company-admin-pages-enum';
import { PermissionService } from '../services/permission.service';
import { AuthService } from '../services/auth.service';

@Component({
    selector: 'app-sidemenu',
    templateUrl: './sidemenu.component.html',
    styleUrls: ['./sidemenu.component.scss']
})
export class SidemenuComponent implements OnInit, AfterViewInit, OnDestroy {
    model: any[];
    user: any;
    userData: any;
    menuSourceSubscription: Subscription;
    menuResetSubscription: Subscription;
    active = false;
    key: string;
    private subscription!: Subscription;

    constructor(public translate: TranslateService, private menuService: MenuService, private permissionService: PermissionService, private authService: AuthService) {
        this.user = this.authService.getUserData();
        this.permissionService.updateUser(this.user);
        this.menuSourceSubscription = this.menuService.menuSource$.subscribe(key => {
            // deactivate current active menu
            if (this.active && this.key !== key && key.indexOf(this.key) !== 0) {
                this.active = false;
            }
        });

        this.menuResetSubscription = this.menuService.resetSource$.subscribe(() => {
            this.active = false;
        });

    }
    // we don't need to swithLang here 
    /* switchLang(lang: string) {
         if (lang == "ar") {
             localStorage.setItem('lang', 'ar');
         }
         else {
             localStorage.setItem('lang', 'en');
         }
     }*/
    ngOnInit(): void {
        // console.log('Dashboard');
        // console.log(this.translate.getTranslation('Dashboard'));
        // console.log('test: ', this.translate.getTranslation('Dashboard'));
        this.translate.get('Dashboard').subscribe((res: string) => {
            //console.log('res: ', res);
            //=> 'hello world'
        });
        this.setMenu()
        // Subscribe to the menu refresh event
        this.subscription = this.menuService.menuRefreshEvent.subscribe(() => {
            this.user = this.authService.getUserData();
            this.permissionService.updateUser(this.user);
            this.setMenu();
        });

    }
    setMenu() {
        if (this.user.UserType === UserType.WebsiteAdmin) {
            if (this.permissionService.isPageAllowed(WebsiteAdminPageEnum.Dashboard)) {
                this.model = [
                    {
                        label: 'Dashboard', icon: 'pi pi-home',
                        items: [
                            { label: 'Admin Details', icon: 'pi pi-home', routerLink: ['/'] },
                            { label: 'General Dashboard', icon: 'pi pi-chart-bar', routerLink: ['dashboard'] },
                            { label: 'Company Details', icon: 'pi pi-chart-line', routerLink: ['company-dashboard'] },
                        ]
                    },
                ];
            } else {
                this.model = [
                    {
                        label: 'Dashboard', icon: 'pi pi-home',
                        items: [
                            { label: 'Admin Details', icon: 'pi pi-home', routerLink: ['/'] },
                        ]
                    },
                ];
            }
            if (this.permissionService.isPageAllowed(WebsiteAdminPageEnum.Companies)) {
                this.model.push({
                    label: 'Company',
                    items: [
                        { label: 'Companies', icon: 'pi pi-building', routerLink: ['companies'] },
                    ]
                });
            }
            if (this.permissionService.isPageAllowed(WebsiteAdminPageEnum.Discounts)) {
                this.model.push({
                    label: 'Discount', icon: 'pi pi-percentage',
                    items: [
                        { label: 'Inbox', icon: 'pi pi-inbox', routerLink: ['/discounts-inbox'] },
                        { label: 'Reviewed', icon: 'pi pi-verified', routerLink: ['/discounts-reviewed'] },
                        { label: 'Deactivated', icon: 'pi pi-calendar-times', routerLink: ['/discounts-deactivated'] },
                    ]
                });
            }
            if (this.permissionService.isPageAllowed(WebsiteAdminPageEnum.Coupons)) {
                this.model.push({
                    label: 'Coupon', icon: 'pi pi-ticket',
                    items: [
                        { label: 'Inbox', icon: 'pi pi-inbox', routerLink: ['/coupons-inbox'] },
                        { label: 'Reviewed', icon: 'pi pi-verified', routerLink: ['/coupons-reviewed'] },
                        { label: 'Deactivated', icon: 'pi pi-calendar-times', routerLink: ['/coupons-deactivated'] },
                    ]
                });
            }
            if (this.permissionService.isPageAllowed(WebsiteAdminPageEnum.EndUsers)) {
                this.model.push({
                    label: 'End Users', icon: 'pi pi-ticket',
                    items: [
                        { label: 'End Users', icon: 'pi pi-user', routerLink: ['/end-users'] },
                    ]
                });
            }
            if (this.permissionService.isPageAllowed(WebsiteAdminPageEnum.BroadcastMessages)) {
                this.model.push({
                    label: 'Broadcast Messages', icon: 'pi pi-envelope',
                    items: [
                        { label: 'Inbox', icon: 'pi pi-inbox', routerLink: ['/broadcast-messages-inbox'] },
                        { label: 'Reviewed', icon: 'pi pi-verified', routerLink: ['/broadcast-messages-reviewed'] },
                        { label: 'Deactivated', icon: 'pi pi-calendar-times', routerLink: ['/broadcast-messages-deactivated'] },
                    ]
                });
            }
            const manageValuesMenu = [
                { label: 'Industries', icon: 'pi pi-building', routerLink: ['/industries'], permission: WebsiteAdminPageEnum.Industries },
                { label: 'Malls', icon: 'pi pi-shopping-cart', routerLink: ['/malls'], permission: WebsiteAdminPageEnum.Malls },
                { label: 'Countries', icon: 'pi pi-globe', routerLink: ['/countries'], permission: WebsiteAdminPageEnum.Countries },
                { label: 'Tiers', icon: 'pi pi-th-large', routerLink: ['/tiers'], permission: WebsiteAdminPageEnum.Tiers },
                { label: 'Subscriptions', icon: 'pi pi-wallet', routerLink: ['/subscriptions'], permission: WebsiteAdminPageEnum.Subscriptions },
                { label: 'Website Admins', icon: 'pi pi-users', routerLink: ['/website-admins-management'], permission: WebsiteAdminPageEnum.WebsiteAdmins },
                { label: 'Company Admins', icon: 'pi pi-user-edit', routerLink: ['/company-admins-management'], permission: WebsiteAdminPageEnum.CompanyAdmins },
            ];
            const allowedAccessItems = manageValuesMenu.filter((item) => this.permissionService.isPageAllowed(item.permission));
            if (allowedAccessItems.length > 0)
                this.model.push({
                    label: 'Manage Values',
                    items: allowedAccessItems
                });
        }


        if (this.user.UserType === UserType.CompanyAdmin) {
            if (this.permissionService.isPageAllowed(CompanyAdminPageEnum.Dashboard)) {
                this.model = [
                    {
                        label: 'Dashboard', icon: 'pi pi-home',
                        items: [
                            { label: 'Admin Details', icon: 'pi pi-home', routerLink: ['/'] },
                            { label: 'Company Details', icon: 'pi pi-chart-line', routerLink: ['company-dashboard'] },
                        ]
                    },
                ];
            } else {
                this.model = [
                    {
                        label: 'Dashboard', icon: 'pi pi-home',
                        items: [
                            { label: 'Admin Details', icon: 'pi pi-home', routerLink: ['/'] },
                        ]
                    },
                ];
            }

            if (this.permissionService.isPageAllowed(CompanyAdminPageEnum.CompanyProfile)) {
                this.model.push({
                    label: 'Company',
                    items: [
                        { label: 'Company', icon: 'pi pi-building', routerLink: ['companies'], permission: CompanyAdminPageEnum.CompanyProfile },
                    ]
                });
            }
            if (this.permissionService.isPageAllowed(CompanyAdminPageEnum.Discounts)) {
                this.model.push({
                    label: 'Discount', icon: 'pi pi-percentage',
                    items: [
                        { label: 'Discounts', icon: 'pi pi-verified', routerLink: ['/company-discounts'], permission: CompanyAdminPageEnum.Discounts },
                    ]
                });
            } if (this.permissionService.isPageAllowed(CompanyAdminPageEnum.Coupons)) {
                this.model.push({
                    label: 'Coupon', icon: 'pi pi-ticket',
                    items: [
                        { label: 'Coupons', icon: 'pi pi-ticket', routerLink: ['/company-coupons'], permission: CompanyAdminPageEnum.Coupons },
                    ]
                });
            }
            if (this.permissionService.isPageAllowed(CompanyAdminPageEnum.BroadcastMessages)) {
                this.model.push({
                    label: 'Broadcast Messages', icon: 'pi pi-envelope',
                    items: [
                        { label: 'Broadcast Messages', icon: 'pi pi-envelope', routerLink: ['/company-broadcast-messages'], permission: CompanyAdminPageEnum.BroadcastMessages },
                    ]
                });
            }
            if (this.permissionService.isPageAllowed(CompanyAdminPageEnum.CompanyAdmins)) {
                this.model.push({
                    label: 'Manage Values',
                    items: [{ label: 'Company Admins', icon: 'pi pi-user-edit', routerLink: ['/company-admins-management'], permission: CompanyAdminPageEnum.CompanyAdmins }]
                });
            }
        }

    }

    ngOnDestroy() {
        if (this.menuSourceSubscription) {
            this.menuSourceSubscription.unsubscribe();
        }

        if (this.menuResetSubscription) {
            this.menuResetSubscription.unsubscribe();
        }
        // Clean up the subscription
        if (this.subscription) {
            this.subscription.unsubscribe();
        }
    }
    ngAfterViewInit(): void {
        // this.translate.get('Dashboard').subscribe((res: string) => {
        //     console.log('res: ', res);
        //     //=> 'hello world'
        // });
    }
}
