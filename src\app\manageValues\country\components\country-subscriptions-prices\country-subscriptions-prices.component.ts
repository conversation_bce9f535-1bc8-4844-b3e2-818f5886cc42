import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { Country } from 'src/app/Model/Country';
import { SubscriptionModel } from 'src/app/Model/SubscriptionModel';
import { CountryService } from 'src/app/services/country.service';
import { SubscriptionService } from 'src/app/services/subscription.service';

@Component({
  selector: 'country-subscriptions-prices',
  templateUrl: './country-subscriptions-prices.component.html',
  styleUrls: ['./country-subscriptions-prices.component.scss'],

})
export class CountrySubscriptionsPricesComponent implements OnInit {
  @Input() _CountryData: Country;
  @Input() isEditing: boolean;
  @Input() activeIndex: number;
  @Output() activeIndexChange: EventEmitter<number> = new EventEmitter();
  pressedNext = false;

  SubscriptionStatusMessage: string = '';
  Subscriptions: SubscriptionModel[] = [];

  constructor(private router: Router, private messageService: MessageService, private ref: ChangeDetectorRef
    , private SubscriptionService: SubscriptionService, private CountryService: CountryService,) { }

  ngOnInit(): void {
    this.refreshSubscriptions()
  }

  save() {
    this._CountryData.CountrySubscriptions = this.Subscriptions;

    if (this._CountryData.IsSubsciptionsDiscountEnabled && !this._CountryData.SubsciptionsDiscountValue) {
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please Insert Subscriptions Discount Value' });
      window.scroll({
        top: 0,
        left: 0,
        behavior: 'smooth'
      });
      return;
    }
    if (this._CountryData.CountrySubscriptions.filter(x => x.FeePerYear == null).length > 0) {
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please check your inputs' });
      window.scroll({
        top: 0,
        left: 0,
        behavior: 'smooth'
      });
      this.SubscriptionStatusMessage = 'Please add values for all Subscription Fee Per Year';
      return;
    }
    this.SubscriptionStatusMessage = '';
    if (this._CountryData.HasError == true) {
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please Check the tabs data !' });
      window.scroll({
        top: 0,
        left: 0,
        behavior: 'smooth'
      });
      return;
    }

    let form: FormData = new FormData();
    form.append('request', JSON.stringify(this._CountryData));

    if (this._CountryData.Id) {
      this.CountryService.EditCountry(form).subscribe(data => {
        if (data.HasError) {
          console.log('edit result', data);
        }
        else {
          this.CountryService.getCountries().subscribe();
          this.router.navigate(['countries'])
        }
      });
    }
    else {
      this.CountryService.AddCountry(form).subscribe((data) => {
        if (data.HasError) {
          console.log('add result', data);
        }
        else {
          this.router.navigate(['countries'])
        }
      })
    }


  }

  refreshSubscriptions() {
    if (this._CountryData.CountrySubscriptions.length > 0) {
      this.Subscriptions = this._CountryData.CountrySubscriptions;
    }
    else {
      this.SubscriptionService.getAllSubscriptions().subscribe((data) => {
        this.Subscriptions = data.map(Subscription => ({
          Id: Subscription.Id,
          ArName: Subscription.ArName,
          EnName: Subscription.EnName,
          FeePerYear: 0,
          // Checked: false
        }));
      });
    }

  }
  onChangeIsSubsciptionsDiscountEnabled() {
    if (!this._CountryData.IsSubsciptionsDiscountEnabled) {
      this._CountryData.SubsciptionsDiscountValue = 0;
    }
  }

  back() {
    // The activeIndex was set to a direct value because during editing, the navigate tabs, a problem in navigation when pressing the buttons (next, back).
    this.activeIndex = 3;
    this.activeIndexChange.emit(this.activeIndex);
  }

}


