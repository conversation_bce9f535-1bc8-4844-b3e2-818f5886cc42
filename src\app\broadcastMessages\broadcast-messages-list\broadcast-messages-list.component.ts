import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ConfirmationService, MessageService } from 'primeng/api';
import { UserModel } from 'src/app/Model/UserModel';
import { BroadcastMessageModel } from 'src/app/Model/BroadcastMessageModel';
import { BroadcastMessageActionLogEnum } from 'src/app/enum/broadcast-message-action-log-enum';
import { BroadcastMessagesService } from 'src/app/services/broadcast-messages.service';
import { environment } from 'src/environments/environment';
import { BroadcastMessageStatusEnum } from 'src/app/enum/broadcast-message-status-enum';
import { BroadcastMessageTypePipe } from 'src/app/pipe/broadcast-message-type.pipe';
import { AuthService } from 'src/app/services/auth.service';

@Component({
    selector: 'app-broadcast-messages-list',
    templateUrl: './broadcast-messages-list.component.html',
    styleUrls: ['./broadcast-messages-list.component.scss'],
    providers: [MessageService, ConfirmationService],
})
export class BroadcastMessagesListComponent implements OnInit {
    BroadcastMessages: BroadcastMessageModel[];
    loading: boolean = true;
    visible: boolean = false;
    reviewed: boolean = false;
    dactivated: boolean = false;
    user: UserModel;
    title: string;
    formData: BroadcastMessageModel;
    imageSrc: string = environment.imageSrc;


    //filters
    CompanyNameOrBroadcastMessageTitle: string = '';
    StartDate: any; EndDate: any;
    SendAsSoonAsPossible: boolean;

    totalRecords: number = 0;
    pageSize: number = 10;
    pageIndex: number = 0;
    paginator: boolean = false;

    //  track the first load
    private isInitialLoad: boolean = false;


    constructor(private BroadcastMessagesService: BroadcastMessagesService, private router: Router, private route: ActivatedRoute, private messageService: MessageService,
        private confirmationService: ConfirmationService, private broadcastMessageTypePipe: BroadcastMessageTypePipe, private authService: AuthService) {
        route.data.subscribe((item) => {
            this.reviewed = item['reviewed'];
            this.dactivated = item['deactivated'];
            if (this.dactivated == true) {
                this.title = "Deactivated Broadcast Messages";
            } else
                if (this.reviewed == true) {
                    this.title = "Reviewed Broadcast Messages";
                }
                else if (this.reviewed == false) {
                    this.title = "Inbox Broadcast Messages";
                }
            //

        });

    }
    ngOnInit(): void {

        this.loadData({ first: 0, rows: this.pageSize });
        this.isInitialLoad = true;
        // this.route.paramMap.subscribe(params => {
        //     this.urlSegment = params.get('id');
        //     console.log(this.urlSegment);
        // });
    }

    loadData(event: any) {
        // Avoid double loading during the first render
        if (this.isInitialLoad) {
            this.isInitialLoad = false; // Set the flag to false after the first load
            return;
        }

        const pageNumber = event.first / event.rows + 1; // Calculate the page number
        const pageSize = event.rows; // Rows per page
        this.paginator = true;
        this.user = this.authService.getUserData();
        this.fetchBroadcastMessagessData(pageNumber, pageSize)
    }

    fetchBroadcastMessagessData(pageNumber, pageSize) {
        if (this.user.UserType == 0) {
            if (this.dactivated == true)
                this.BroadcastMessagesService.getBroadcastMessageForAdmin(pageNumber, pageSize, this.reviewed, this.dactivated, this.CompanyNameOrBroadcastMessageTitle, this.StartDate, this.EndDate, this.SendAsSoonAsPossible).subscribe((data: any) => {
                    // console.log('data', data);
                    this.BroadcastMessages = data.ListResultContent;
                    this.totalRecords = data.TotalRecords; // Set total records for pagination
                    this.loading = false;

                });
            else
                this.BroadcastMessagesService.getBroadcastMessageForAdmin(pageNumber, pageSize, this.reviewed, false, this.CompanyNameOrBroadcastMessageTitle, this.StartDate, this.EndDate, this.SendAsSoonAsPossible).subscribe((data: any) => {
                    this.BroadcastMessages = data.ListResultContent;
                    this.totalRecords = data.TotalRecords; // Set total records for pagination
                    this.loading = false;

                });
        }
        else {

            this.BroadcastMessagesService.getBroadcastMessagesForCompany( pageNumber, pageSize, this.CompanyNameOrBroadcastMessageTitle, this.StartDate, this.EndDate, this.SendAsSoonAsPossible).subscribe((data) => {

                this.BroadcastMessages = data.ListResultContent;
                this.totalRecords = data.TotalRecords; // Set total records for pagination
                this.loading = false;
            });
        }
    }
    colorOfLastActionLog(lastActionLog: BroadcastMessageActionLogEnum) {
        switch (lastActionLog) {
            case BroadcastMessageActionLogEnum.Approved:
                return { color: '#23df36' };
            case BroadcastMessageActionLogEnum.Created:
                return { color: '#8a23df' };
            case BroadcastMessageActionLogEnum.Edited:
                return { color: '#f1f11d' };
            case BroadcastMessageActionLogEnum.Rejected:
                return { color: '#f10a0a' };
            case BroadcastMessageActionLogEnum.Deactivated:
                return { color: '#f10a0a' };
            case BroadcastMessageActionLogEnum.Reactivated:
                return { color: '#0c46e8' };
            default:
                return { color: 'gray' };
        }
    }
    iconForLastActionLog(lastActionLog: BroadcastMessageActionLogEnum) {
        switch (lastActionLog) {
            case BroadcastMessageActionLogEnum.Approved:
                return 'pi pi-check-circle';
            case BroadcastMessageActionLogEnum.Created:
                return 'pi pi-clock';
            case BroadcastMessageActionLogEnum.Edited:
                return 'pi pi-pencil';
            case BroadcastMessageActionLogEnum.Rejected:
                return 'pi pi-times';
            case BroadcastMessageActionLogEnum.Deactivated:
                return 'pi pi-minus-circle';
            case BroadcastMessageActionLogEnum.Reactivated:
                return 'pi pi-check-circle';
            default:
                return 'pi pi-clock';
        }
    }
    ActionLogEnumName(lastActionLog: BroadcastMessageActionLogEnum) {
        switch (lastActionLog) {
            case BroadcastMessageActionLogEnum.Approved:
                return 'Approved';
            case BroadcastMessageActionLogEnum.Created:
                return 'Created';
            case BroadcastMessageActionLogEnum.Edited:
                return 'Edited';
            case BroadcastMessageActionLogEnum.Rejected:
                return 'Rejected';
            case BroadcastMessageActionLogEnum.Deactivated:
                return 'Deactivated';
            case BroadcastMessageActionLogEnum.Reactivated:
                return 'Reactivated';
            default:
                return 'Created';
        }
    }

    colorOfStatus(status: BroadcastMessageStatusEnum) {
        switch (status) {
            case BroadcastMessageStatusEnum.PendingApproval:
                return { color: 'rgb(35, 126, 223)' };
            case BroadcastMessageStatusEnum.Approved:
                return { color: 'rgb(35, 223, 35)' };
            case BroadcastMessageStatusEnum.Sent:
                return { color: 'rgb(35, 223, 142)' };
            case BroadcastMessageStatusEnum.Rejected:
                return { color: 'rgb(223, 35, 35)' };
            default:
                return { color: 'gray' };
        }
    }

    StatusName(status: BroadcastMessageStatusEnum) {
        switch (status) {
            case BroadcastMessageStatusEnum.PendingApproval:
                return 'Pending';
            case BroadcastMessageStatusEnum.Approved:
                return 'Approved';
            case BroadcastMessageStatusEnum.Sent:
                return 'Running';
            case BroadcastMessageStatusEnum.Rejected:
                return 'Rejected';

            default:
                return 'Approved';
        }
    }
    showDialog(dis: BroadcastMessageModel) {
        // First, close all dialogs
        this.BroadcastMessages.forEach(BroadcastMessage => BroadcastMessage.VisibleDialog = false);
        dis.VisibleDialog = true;
    }
    edit(e: { BroadcastMessage?: BroadcastMessageModel, state: string }) {
        if (e.BroadcastMessage.Active == false) {
            this.messageService.add({
                severity: "warn",
                summary: "Warning",
                detail: "Please activate the Broadcast Message before Editing ! ",
            });

        } else {
            this.formData = e.BroadcastMessage;
            this.BroadcastMessagesService.getBroadcastMessageById(e.BroadcastMessage.Id.toString()).subscribe((data) => {
                this.router.navigate(['broadcast-message-edit'], {
                    state: {
                        //  data: this.formData,
                        data: data,
                        command: 'edit'
                    },
                })
            });
        }
    }
    rejectOrAccept(BroadcastMessage: BroadcastMessageModel) {

        this.BroadcastMessagesService.getBroadcastMessageById(BroadcastMessage.Id.toString()).subscribe((data) => {
            data['BroadcastMessagesLog'] = BroadcastMessage.BroadcastMessagesLog;
            this.router.navigate(['broadcast-message-preview'], {
                state: {
                    data: data,
                },
            });
        });

    }
    changeBroadcastMessageLastActionLog(id: string) {
        this.BroadcastMessagesService.EditActiveBroadcastMessage(id).subscribe((data) => {
            if (data['HasError'] == true) {
                this.messageService.add({
                    severity: "error",
                    summary: "Error in Activation Broadcast Message ",
                    detail: data['EnErrorMessage'],
                });
            }
            if (this.dactivated == false) {
                if (data['HasError'] == false && data['ResultContent']['Active'] == false) {
                    var BroadcastMessage = this.BroadcastMessages.find((x) => x.Id == id);
                    // Update the data array (remove the deleted item)
                    this.BroadcastMessages = this.BroadcastMessages.filter(currentItem => currentItem.Id !== BroadcastMessage.Id);
                }
            } else if (this.dactivated == true) {
                if (data['HasError'] == false && data['ResultContent']['Active'] == true) {
                    var BroadcastMessage = this.BroadcastMessages.find((x) => x.Id == id);
                    // Update the data array (remove the deleted item)
                    this.BroadcastMessages = this.BroadcastMessages.filter(currentItem => currentItem.Id !== BroadcastMessage.Id);
                }
            }
        });

    }
    ReceivedFilteredData(event) {
    
        this.CompanyNameOrBroadcastMessageTitle = event.CompanyNameOrBroadcastMessageTitle;
        this.StartDate = event.startDate;
        this.EndDate = event.endDate;
        this.SendAsSoonAsPossible = event.SendAsSoonAsPossible
        this.totalRecords = event.TotalRecords; // Set total records for pagination
        this.fetchBroadcastMessagessData(1, this.pageSize);
        this.BroadcastMessages = event.ListResultContent;

        //this.paginator = false;

    }

    Delete(id) {

        this.confirmationService.confirm({
            key: "confirm1",
            target: event.target,
            message: "Are You Sure Delete This Broadcast Message",
            icon: "pi pi-exclamation-triangle",
            accept: () => {
                this.BroadcastMessagesService.DeleteBroadcastMessage(id)
                    .subscribe((data: any) => {
                        if (data['HasError'] == false) {
                            this.messageService.add({
                                severity: "success",
                                summary: "Success Message",
                                detail: "Delete Broadcast Message Successfully",
                            });
                            var BroadcastMessages = this.BroadcastMessages.find((x) => x.Id == id);
                            const index = this.BroadcastMessages.indexOf(BroadcastMessages, 0);
                            // Update the data array (remove the deleted item)
                            this.BroadcastMessages = this.BroadcastMessages.filter(currentItem => currentItem.Id !== BroadcastMessages.Id);
                            /* if (index > -1) {
                                 this.BroadcastMessagess.splice(index, 1);
 
                             }*/
                        }
                        else {
                            this.messageService.add({
                                severity: "error",
                                summary: "Error",
                                detail: "Can't Delete This Broadcast Message ",
                            });
                        }
                    });
            },
            reject: () => {
                this.messageService.add({
                    severity: "error",
                    summary: "Rejected",
                    detail: "You have rejected",
                });
            },
        });
        // alert("Are You Sure Delete This BroadcastMessages");
    }

    canEditBroadcastMessage(BroadcastMessage: BroadcastMessageModel): boolean {
        // Multi-condition logic to determine if the BroadcastMessages can be edited
        if (BroadcastMessage.Active == false) {
            return false;
        }
        if (this.user.UserType == 1) // companyAdmin
        {
            if (BroadcastMessage.Status == 3) //Sent
                return false;
        }


        // If none of the above conditions are met, return true
        return true;
    }

    canActiveBroadcastMessage(BroadcastMessage: BroadcastMessageModel): boolean {
        // Multi-condition logic to determine if the BroadcastMessages can be actived

        if (this.user.UserType == 1) // companyAdmin
            return false;

        // hide the Activate switch for PendingApproval, Rejected and Sent BroadcastMessagess 
        if (BroadcastMessage.Status == 0 || BroadcastMessage.Status == 2 || BroadcastMessage.Status == 3)
            return false;


        // If none of the above conditions are met, return true
        return true;
    }

    // Use the pipe programmatically
    transformedTypeLabel(type) {
        return this.broadcastMessageTypePipe.transform(type);
    }

}
