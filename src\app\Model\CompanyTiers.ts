import { CompanyModel } from "./CompanyModel";
import { TiersModel } from "./TiersModel";

export class CompanyTiers {
    Id?: string = "";

    CompanyId?: string = "";
    Company?: CompanyModel = null;

    TierId?: string = "";
    Tier?: TiersModel = null;


    // Same times recive 
    ArName?: string = "";
    EnName?: string = "";
    Rank?: number = 0;
    IsVip?:boolean;
    MonthlyRenewablility?:boolean=false;
    //NotMapped
    Checked?: boolean = false;
}
