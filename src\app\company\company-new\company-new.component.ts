import { Component, ViewChild } from "@angular/core";
import { Router } from "@angular/router";
import { CompanyModel } from "src/app/Model/CompanyModel";
import { UserModel } from "src/app/Model/UserModel";
import { CompanyIndustryService } from "src/app/services/company-industry.service";
import { SharedDataComponentService } from "src/app/services/shared-data-component.service";
import { OverviewComponent } from "../components/overview/overview.component";
import { Message, MessageService } from "primeng/api";
import { BranchesComponent } from "../components/branches/branches.component";
import { AuthService } from "src/app/services/auth.service";

@Component({
  selector: "app-company-new",
  templateUrl: "./company-new.component.html",
  styleUrls: ["./company-new.component.scss"],

})
export class CompanyNewComponent {
  newCompany: CompanyModel = new CompanyModel();
  fileToUpload: any[] = [];
  routeState: any;
  activeIndex = 0;
  previousTabIndex: number = 0; // Track the previous tab index
  isEditing: boolean = true;
  title: string = 'Add New Company';

  tabsDisabled: boolean = true;  // Control whether tabs are disabled or not
  userType: number = 0; // by default super Admin

  msgs: Message[] = [];

  @ViewChild(OverviewComponent) OverviewComponent!: OverviewComponent;
  @ViewChild(BranchesComponent) BranchesComponent!: BranchesComponent;

  constructor(private router: Router, private sharedDataComponentService: SharedDataComponentService, private messageService: MessageService, private authService: AuthService) {
    if (this.router.getCurrentNavigation()?.extras.state) {
      this.routeState = this.router.getCurrentNavigation()?.extras.state;
      if (this.routeState) {

        this.newCompany = this.routeState.data
          ? this.routeState.data
          : new CompanyModel();
      }
    }
    if (!(this.routeState && this.routeState.command == 'edit')) {
      // this.isEditing = true;
      this.isEditing = false;

      this.newCompany.CompanyAdmin = new UserModel();
      this.newCompany.CompanyAdmin.UserType = 0;

    }
    if (this.isEditing == true) { this.tabsDisabled = false; this.title = 'Edit Company'; }
  }
  ngOnInit() {
    this.userType = this.authService.getUserData().UserType;
  }
  onTabChange(event: any) {

    const newTabIndex = event.index; // Get the newly selected tab index
    let proceed = true;
    if (this.previousTabIndex === 0 && newTabIndex !== 0) {
      // If Tab 1 is selected, trigger function in app-child-one-component
      this.OverviewComponent.next();
      proceed = this.OverviewComponent.pressedNext;
      if (this.newCompany.hasError == true) {
        this.msgs = [];
        this.msgs.push({ severity: 'error', summary: 'Error Message', detail: 'Please Check the Overview Tab !' });
      }
      this.BranchesComponent.next();
      proceed = true;
      if (this.newCompany.hasError == true) {
        this.msgs = [];
        this.msgs.push({ severity: 'error', summary: 'Error Message', detail: 'Please Check the Branches Tab !' });
      }
    } else if (this.previousTabIndex === 1 && newTabIndex !== 1) {

      this.BranchesComponent.next();
      proceed = true;
      if (this.newCompany.hasError == true) {
        this.msgs = [];
        this.msgs.push({ severity: 'error', summary: 'Error Message', detail: 'Please Check the Branches Tab !' });
      }
    }
  }

  // Method to handle child component event and disable tabs
  handleDisableTabs(disable: boolean) {
    this.tabsDisabled = disable;  // If true, disable all tabs

  }
}
