import { Pipe, PipeTransform } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Pipe({
    name: 'translateDb'
})
export class TranslatePipeDB implements PipeTransform {

    constructor(private translateService: TranslateService) {

    }
    transform(value: unknown, ...args: unknown[]): unknown {
        if (this.translateService.currentLang === 'ar') {
            return value["ArName"];
        }
        else {
            return value["EnName"];
        }
    }

}
