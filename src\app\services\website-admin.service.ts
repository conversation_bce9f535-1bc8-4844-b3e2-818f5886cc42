import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { catchError, map } from 'rxjs/operators';
import { Dto } from '../Model/Dto';
import { environment } from 'src/environments/environment';
import { UserModel } from '../Model/UserModel';
import { Observable, of } from 'rxjs';

@Injectable({
	providedIn: 'root'
})
export class WebsiteAdminService {

	constructor(protected httpClient: HttpClient,) { }

	GetAllWebsiteAdmins(page: number = null, pageSize: number = null, Name: string = null, adminType: string = null, CountryId: string = null) {
		let params = new HttpParams();
		if (page) {
			params = params.set('PageNumber', page);
		}
		if (pageSize) {
			params = params.set('PageSize', pageSize);
		}
		if (Name) {
			params = params.set('Name', Name);
		}
		if (adminType != null) {
			params = params.set('AdminType', adminType);
		}
		if (CountryId) {
			params = params.set('CountryId', CountryId);
		}
		return this.httpClient.get<Dto<UserModel>>(`${environment.apiUrl}` + 'WebsiteAdmin/GetAll', { params })
			.pipe(
				map((res: any) => {
					return res;

				})
			);
	}

	getWebsiteAdminById(Id: string = "") {
		return this.httpClient.get<Dto<UserModel>>(`${environment.apiUrl}WebsiteAdmin/GetWebsiteAdminById?Id=${Id}`)
			.pipe(
				map((res: any) => {

					return res['ResultContent'];
				})
			);
	}

	public AddWebsiteAdmin(data): Observable<Dto<UserModel>> {
		var http;
		var url = `${environment.apiUrl}WebsiteAdmin/Register`;
		http = this.httpClient.post(url, data);
		return http.pipe(
			map((res: Dto<UserModel>) => {
				return res;
			}),
			catchError(error => {
				return of(false);

			}));
	}

	public EditWebsiteAdmin(data): Observable<Dto<UserModel>> {
		var http;
		var url = `${environment.apiUrl}WebsiteAdmin/EditWebsiteAdmin`;
		http = this.httpClient.put(url, data);
		return http.pipe(
			map((res: Dto<UserModel>) => {

				return res;

			}),
			catchError(error => {

				return of(false);
			}));
	}

	public EditActiveWebsiteAdmin(WebsiteAdminId: String): Observable<any> {
		var http;
		var url = `${environment.apiUrl}WebsiteAdmin/EditWebsiteAdminActive?Id=${WebsiteAdminId}`;
		http = this.httpClient.patch(url, {});
		return http.pipe(
			map((res: any) => {
				return res;
			}),
			catchError((error) => {

				return of(false);
			})
		);
	}

	public DeleteWebsiteAdmin(WebsiteAdminId: string): Observable<any> {
		var http;
		var url = `${environment.apiUrl}WebsiteAdmin/DeleteWebsiteAdmin?Id=${WebsiteAdminId}`;

		http = this.httpClient.delete(url);
		return http.pipe(
			map((res: any) => {
				return res;
			}),
			catchError((error) => {

				return of(false);
			})
		);
	}

}
