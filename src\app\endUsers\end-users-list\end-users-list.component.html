<p-toast></p-toast>
<div class="grid">
    <div class="col-12">
        <div>
            <p-confirmDialog header="Confirmation" key="confirm1" icon="pi pi-exclamation-triangle"
                message="Are you sure you want to proceed?" [style]="{ width: '350px' }"
                acceptButtonStyleClass="p-button-text" rejectButtonStyleClass="p-button-text"></p-confirmDialog>


            <p-table #dt [value]="EndUsers" [paginator]="paginator" [rows]="pageSize" [totalRecords]="totalRecords"
                [lazy]="true" [loading]="loading" (onLazyLoad)="loadData($event)" [showCurrentPageReport]="true"
                [currentPageReportTemplate]="'Showing {first} to {last} of {totalRecords} records'"
                responsiveLayout="scroll" [rowHover]="true" styleClass="p-datatable-gridlines">

                <ng-template pTemplate="caption">
                    <app-grid-headers [myDt]="EndUsers" (SearchEvent)='ReceivedFilteredData($event)' addNewTxt=""
                        goRoute="" gridTitle="EndUsersManagement" [title]="title">
                    </app-grid-headers>
                </ng-template>
                <ng-template pTemplate="header">
                    <tr>
                        <th>
                            <div class="flex justify-content-between align-items-center">
                                {{ "ID" | translate }}
                            </div>
                        </th>
                        <th>
                            <div class="flex justify-content-between align-items-center">
                                {{ "Name" | translate }}
                            </div>
                        </th>
                        <th>
                            <div class="flex justify-content-between align-items-center">
                                {{ "Gender" | translate }}
                            </div>
                        </th>
                        <th>
                            <div class="flex justify-content-between align-items-center">
                                {{ "Age" | translate }}
                            </div>
                        </th>
                        <th>
                            <div class="flex justify-content-between align-items-center">
                                {{ "Nationality" | translate }}
                            </div>
                        </th>
                        <th>
                            <div class="flex justify-content-between align-items-center">
                                {{ "Residance" | translate }}
                            </div>
                        </th>
                        <th>
                            <div class="flex justify-content-between align-items-center">
                                {{ "Tier" | translate }}
                            </div>
                        </th>
                        <th>
                            <div class="flex justify-content-between align-items-center">
                                {{ "Expire Date" | translate }}
                            </div>
                        </th>
                        <th>
                            <div class="flex justify-content-between align-items-center">
                                {{ "Discounts" | translate }}
                            </div>
                        </th>
                        <th>
                            <div class="flex justify-content-between align-items-center">
                                {{ "Total Saving" | translate }}
                            </div>
                        </th>
                        <!-- <th>
                            <div class="flex justify-content-between align-items-center">
                                {{ "Edit" | translate }}
                            </div>
                        </th> -->
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-EndUser>
                    <tr>
                        <td>

                            <div class="flex">
                                {{ EndUser.UserFriendly }}
                            </div>
                        </td>
                        <td>
                            <div class="flex">

                                {{ EndUser.Name }}
                            </div>
                        </td>
                        <td>

                            <div class="flex">
                                {{ EndUser.Gender | Gender }}
                            </div>
                        </td>
                        <td>
                            <div class="flex">
                                {{ EndUser.Age }}
                            </div>
                        </td>
                        <td>
                            <div class="flex">
                                {{ EndUser.Nationality?.EnName}}
                            </div>
                        </td>
                        <td>
                            <div class="flex">
                                {{ EndUser.Residance }}
                            </div>
                        </td>
                        <td>
                            <div>

                                <a (click)="openDialog(EndUser)">
                                    <!-- {{ EndUser.Tier?.EnName }} -->
                                    <span class="p-badge p-badge-lg" [style.backgroundColor]="EndUser.Tier?.Color">{{
                                        EndUser.Tier?.EnName
                                        }}</span>
                                </a>
                            </div>
                        </td>
                        <td>
                            <div class="flex">
                                {{ EndUser.ExpireDate | date : "dd/MM/yyyy"}}
                            </div>
                        </td>
                        <td>
                            <div class="flex">
                                {{ EndUser.RedeemedDiscountsNum }}
                            </div>
                        </td>
                        <td>
                            <div class="flex">
                                {{ EndUser.TotalEstimatedSavings }} {{EndUser.TotalEstimatedSavingsCurrency}}
                            </div>
                        </td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td class="text-center" colspan="7">
                            {{ "No End Users found." | translate }}
                        </td>
                    </tr>
                </ng-template>
            </p-table>
        </div>
    </div>
</div>

<p-dialog [(visible)]="displayDialog" [modal]="true" [closable]="true" header="Upgrade End User to VIP "  [breakpoints]="{ '960px': '75vw' }" [style]="{ width: '25vw' }" [draggable]="false" >
    <div *ngIf="selectedEndUser">
        <div class="d-flex gap-2 align-items-center mb-4">
            <p><strong>Name :</strong> {{ selectedEndUser.Name }}</p>
        </div>
        <div class="d-flex gap-2 align-items-center mb-4">
            <p><strong>Tier :</strong></p>
            <p-dropdown [options]="tiers" [(ngModel)]="selectedTier" optionLabel="EnName"
                placeholder="Choose the Tier"></p-dropdown>
        </div>

    </div>
    <div class="p-dialog-footer">
        <button pButton label="Save" icon="pi pi-check" (click)="MakeEndUserVipUser()"
            [disabled]="selectedTier==undefined" class="p-button-success"></button>
        <button pButton label="Cancel" icon="pi pi-times" (click)="displayDialog = false"
            class="p-button-secondary"></button>
    </div>
</p-dialog>
