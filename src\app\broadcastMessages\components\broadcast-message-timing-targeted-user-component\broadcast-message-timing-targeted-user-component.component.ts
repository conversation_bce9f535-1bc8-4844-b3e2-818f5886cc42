import { ChangeDetectorRef, Component, EventEmitter, Input, Output } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { Gender } from 'src/app/enum/gender';
import { EnumTime, TimeConverter } from 'src/app/enum/time-enum';
import { BroadcastMessageModel } from 'src/app/Model/BroadcastMessageModel';
import { NationalityModel } from 'src/app/Model/NationalityModel';
import { UserModel } from 'src/app/Model/UserModel';
import { AuthService } from 'src/app/services/auth.service';
import { BroadcastMessagesService } from 'src/app/services/broadcast-messages.service';
import { CountryService } from 'src/app/services/country.service';
import { NationalityService } from 'src/app/services/nationality.service';

@Component({
  selector: 'broadcast-message-timing-targeted-user-component',
  templateUrl: './broadcast-message-timing-targeted-user-component.component.html',
  styleUrls: ['./broadcast-message-timing-targeted-user-component.component.scss']
})

export class BroadcastMessageTimingTargetedUserComponentComponent {

  @Input() _BroadcastMessageData: BroadcastMessageModel;
  @Input() activeIndex: number;
  @Input() editing: boolean;
  user: UserModel;
  @Output() activeIndexChange: EventEmitter<number> = new EventEmitter();
  routeState: any;
  enableEdit: boolean = false;
  saveButtonDisable: boolean = false;
  pressedNext = false;
  currentDate: string;
  Cities = [];
  timingTargetUsersForm = new FormGroup({
    SendAsSoonAsPossible: new FormControl(''),
    LaunchDate: new FormControl(''),
    TargetedTiers: new FormControl(''),
    TargetedCities: new FormControl(''),
    TargetedCountry: new FormControl('', [Validators.required]),
    TargetedNationalities: new FormControl(''),
    TargetedGender: new FormControl(''),
    TargetedMaxAge: new FormControl(''),
    TargetedMinAge: new FormControl(''),

  }, { validators: this.minValueValidator });

  timeOptions: { label: string; value: string }[];
  selectedTimeSlot: string;

  genders = [];
  countries = [];
  nationalities: NationalityModel[] = [];

  constructor(private router: Router, private messageService: MessageService, private ref: ChangeDetectorRef, private broadcastMessagesService: BroadcastMessagesService, private countryService: CountryService,
    private nationalityService: NationalityService, private authService: AuthService
  ) {
    this.currentDate = new Date().toISOString().slice(0, 10);
    this.user = this.authService.getUserData();

    if (this.router.getCurrentNavigation()?.extras.state) {
      this.routeState = this.router.getCurrentNavigation()?.extras.state;
      if (this.routeState) {
        this.enableEdit = this.routeState.command == 'edit'
      }
    }


  }
  ngOnInit() {
    // Convert enum to an array of objects for Gender
    this.genders =
      Object.keys(Gender)
        .filter(key => isNaN(Number(key)))
        .map(key => ({
          label: key,
          value: Gender[key]
        }));
    this.genders = [{ label: '', value: null }, ... this.genders,];

    this.timeOptions = Object.values(EnumTime).map(time => ({
      label: time,
      value: time
    }));

    this.countryService.Countries.subscribe((data) => {
      this.countries = data;
      if (this._BroadcastMessageData.TargetedCountry) {
        let country = this.countries.find(item => item.Id === this._BroadcastMessageData.TargetedCountry.Id);
        this.Cities = country.Cities.map(key => ({
          Id: key.Id,
          ArName: key.ArName,
          EnName: key.EnName,
          CountryId: country.Id
        }));
        // this._BroadcastMessageData.TargetedCities.map(key => ({
        //   Id: key.Id,
        //   ArName: key.ArName,
        //   EnName: key.EnName,
        // CountryId :country.Id
        // }));;;
      }


    });
    if (this._BroadcastMessageData.LaunchStartTime && this._BroadcastMessageData.LaunchEndTime) {
      this.selectedTimeSlot = TimeConverter.findTimeSlot(this._BroadcastMessageData.LaunchStartTime, this._BroadcastMessageData.LaunchEndTime, Object.values(EnumTime));
    }

    this.nationalityService.getAllNationalities().subscribe((data) => {
      this.nationalities = data
    });

  }

  onTimeSlotChange(): void {
    if (this.selectedTimeSlot) {
      const convertedTime = TimeConverter.convertToTimeObject(this.selectedTimeSlot);
      this._BroadcastMessageData.LaunchStartTime = convertedTime.StartTime;
      this._BroadcastMessageData.LaunchEndTime = convertedTime.EndTime;
      // console.log('Converted Time:', convertedTime);
      // Example: { StartTime: "08:00", EndTime: "09:00" }
    } else {
      this._BroadcastMessageData.LaunchStartTime = null;
      this._BroadcastMessageData.LaunchEndTime = null;
    }
  }
  back() {
    this.activeIndex--;
    this.activeIndexChange.emit(this.activeIndex);
  }

  save() {
    // console.log('data', this._BroadcastMessageData)
    this.pressedNext = true;
    if (!this.timingTargetUsersForm.valid) {
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Check your inputs' });
      return;
    }
    if (this._BroadcastMessageData.hasError == true) {
      // the message will be catch from the new Broadcast Message Component
      // this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please Check the tabs data !' });
      // window.scroll({
      //     top: 0,
      //     left: 0,
      //     behavior: 'smooth'
      // });

      return;
    }



    delete this._BroadcastMessageData.Active;
    delete this._BroadcastMessageData.CompanyId;
    delete this._BroadcastMessageData.Countries;
    delete this._BroadcastMessageData.Note;
    delete this._BroadcastMessageData.Tiers;
    delete this._BroadcastMessageData.VisibleDialog;
    delete this._BroadcastMessageData.hasError;

    if (this._BroadcastMessageData.Company) {
      const attributesToKeepInCompany = ['Id', 'EnName', 'ArName'];
      // Delete attributes not in attributesToKeepInCompany
      Object.keys(this._BroadcastMessageData.Company).forEach((key) => {
        if (!attributesToKeepInCompany.includes(key)) {
          delete this._BroadcastMessageData.Company[key];
        }
      });
    }
    if (this.editing) {
      this.broadcastMessagesService.EditBroadcastMessage(this._BroadcastMessageData).subscribe(data => {
        if (data.HasError) {
          console.log('post result', data);
        }
        else {
          this.router.navigate(['broadcast-messages-inbox'])
        }
      });
    } else {
      delete this._BroadcastMessageData.Id;
      delete this._BroadcastMessageData.UserFriendly;
      this.broadcastMessagesService.AddBroadcastMessage(this._BroadcastMessageData).subscribe((data) => {
        if (data.HasError) {
          console.log('post result', data);
        }
        else {
          this.router.navigate(['broadcast-messages-inbox'])
        }
      })
    }
  }

  CheckValid(input: FormControl) {
    if (input.invalid && (this.pressedNext || input.touched)) {
      return 'red';
    }
    return '#515C66';
  }

  checkASAP() {
    if (this._BroadcastMessageData.SendAsSoonAsPossible) {
      this.timingTargetUsersForm.get('LaunchDate')?.clearValidators();
      this.timingTargetUsersForm.get('LaunchDate')?.reset();
      this._BroadcastMessageData.LaunchDate = null;
      this._BroadcastMessageData.LaunchStartTime = null;
      this._BroadcastMessageData.LaunchEndTime = null;
      this.selectedTimeSlot = null;
    } else {
      this.timingTargetUsersForm.get('LaunchDate').setValidators([Validators.required,])
    }
    this.timingTargetUsersForm.get('LaunchDate')?.updateValueAndValidity()
  }


  selectCountry() {
    let country = this.countries.find(item => item.Id === this._BroadcastMessageData.TargetedCountry.Id);
    this.Cities = country.Cities.map(key => ({
      Id: key.Id,
      ArName: key.ArName,
      EnName: key.EnName,
    }));
  }

  minValueValidator(control) {
    const minValue = control.get('TargetedMinAge').value;
    const maxValue = control.get('TargetedMaxAge').value;
    if (minValue !== '' && maxValue !== '' && minValue >= maxValue) {
      control.get('TargetedMinAge').setErrors({ 'TargetedMinAgeError': true });
      control.get('TargetedMaxAge').setErrors({ 'TargetedMaxAgeError': true });
      return { 'invalidRange': true };
    } else {

      control.get('TargetedMinAge').setErrors(null);
      control.get('TargetedMaxAge').setErrors(null);
      return null;

    }
  }
}
