import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ConfirmationService, MessageService } from 'primeng/api';
import { UserModel } from 'src/app/Model/UserModel';
import { DiscountModel } from 'src/app/Model/DiscountModel';
import { DiscountActionLogEnum } from 'src/app/enum/discount-action-log-enum';
import { DiscountService } from 'src/app/services/discount.service';
import { environment } from 'src/environments/environment';
import { DiscountStatusEnum } from 'src/app/enum/discount-status-enum';
import { AuthService } from 'src/app/services/auth.service';

@Component({
    selector: 'app-discountlist',
    templateUrl: './discountlist.component.html',
    styleUrls: ['./discountlist.component.scss'],
    providers: [MessageService, ConfirmationService],
})
export class DiscountlistComponent implements OnInit {
    discounts: DiscountModel[];
    loading: boolean = true;
    visible: boolean = false;
    reviewed: boolean = false;
    dactivated: boolean = false;
    user: UserModel;
    title: string;
    formData: DiscountModel;
    imageSrc: string = environment.imageSrc;


    //filters
    CompanyNameOrDiscountTitle: string = '';
    StartDate: any; EndDate: any;

    totalRecords: number = 0;
    pageSize: number = 10;
    pageIndex: number = 0;
    paginator: boolean = false;

    //  track the first load
    private isInitialLoad: boolean = false;


    constructor(private discountService: DiscountService, private router: Router, private route: ActivatedRoute, private messageService: MessageService,
        private confirmationService: ConfirmationService, private authService: AuthService) {
        route.data.subscribe((item) => {
            this.reviewed = item['reviewed'];
            this.dactivated = item['deactivated'];
            if (this.dactivated == true) {
                this.title = "Discount Deactivated";
            } else
                if (this.reviewed == true) {
                    this.title = "Discount Reviewed";
                }
                else if (this.reviewed == false) {
                    this.title = "Discount Inbox";
                }
            //

        });

    }
    ngOnInit(): void {

        this.loadData({ first: 0, rows: this.pageSize });
        this.isInitialLoad = true;
        // this.route.paramMap.subscribe(params => {
        //     this.urlSegment = params.get('id');
        //     console.log(this.urlSegment);
        // });
    }

    loadData(event: any) {
        // Avoid double loading during the first render
        if (this.isInitialLoad) {
            this.isInitialLoad = false; // Set the flag to false after the first load
            return;
        }

        this.user = this.authService.getUserData();
        const pageNumber = event.first / event.rows + 1; // Calculate the page number
        const pageSize = event.rows; // Rows per page
        this.paginator = true;
        this.fetchDiscountsData(pageNumber, pageSize)
    }

    fetchDiscountsData(pageNumber, pageSize) {
        if (this.user.UserType == 0) {
            if (this.dactivated == true)
                this.discountService.getDiscountForAdmin(pageNumber, pageSize, this.reviewed, this.dactivated, this.CompanyNameOrDiscountTitle, this.StartDate, this.EndDate).subscribe((data: any) => {
                    this.discounts = data.ListResultContent;
                    this.totalRecords = data.TotalRecords; // Set total records for pagination
                    this.loading = false;

                });
            else
                this.discountService.getDiscountForAdmin(pageNumber, pageSize, this.reviewed, false, this.CompanyNameOrDiscountTitle, this.StartDate, this.EndDate).subscribe((data: any) => {
                    this.discounts = data.ListResultContent;
                    this.totalRecords = data.TotalRecords; // Set total records for pagination
                    this.loading = false;

                });
        }
        else {
            this.discountService.getDiscountForCompany(pageNumber, pageSize, this.CompanyNameOrDiscountTitle, this.StartDate, this.EndDate).subscribe((data) => {

                this.discounts = data.ListResultContent;
                this.totalRecords = data.TotalRecords; // Set total records for pagination
                this.loading = false;
            });
        }
    }
    colorOfLastActionLog(lastActionLog: DiscountActionLogEnum) {
        switch (lastActionLog) {
            case DiscountActionLogEnum.Approved:
                return { color: '#23df36' };
            case DiscountActionLogEnum.Created:
                return { color: '#8a23df' };
            case DiscountActionLogEnum.Edited:
                return { color: '#f1f11d' };
            case DiscountActionLogEnum.Rejected:
                return { color: '#f10a0a' };
            case DiscountActionLogEnum.Deactivated:
                return { color: '#f10a0a' };
            case DiscountActionLogEnum.Reactivated:
                return { color: '#0c46e8' };
            default:
                return { color: 'gray' };
        }
    }
    iconForLastActionLog(lastActionLog: DiscountActionLogEnum) {
        switch (lastActionLog) {
            case DiscountActionLogEnum.Approved:
                return 'pi pi-check-circle';
            case DiscountActionLogEnum.Created:
                return 'pi pi-clock';
            case DiscountActionLogEnum.Edited:
                return 'pi pi-pencil';
            case DiscountActionLogEnum.Rejected:
                return 'pi pi-times';
            case DiscountActionLogEnum.Deactivated:
                return 'pi pi-minus-circle';
            case DiscountActionLogEnum.Reactivated:
                return 'pi pi-check-circle';
            default:
                return 'pi pi-clock';
        }
    }
    ActionLogEnumName(lastActionLog: DiscountActionLogEnum) {
        switch (lastActionLog) {
            case DiscountActionLogEnum.Approved:
                return 'Approved';
            case DiscountActionLogEnum.Created:
                return 'Created';
            case DiscountActionLogEnum.Edited:
                return 'Edited';
            case DiscountActionLogEnum.Rejected:
                return 'Rejected';
            case DiscountActionLogEnum.Deactivated:
                return 'Deactivated';
            case DiscountActionLogEnum.Reactivated:
                return 'Reactivated';
            default:
                return 'Created';
        }
    }

    colorOfStatus(status: DiscountStatusEnum) {
        switch (status) {
            case DiscountStatusEnum.PendingApproval:
                return { color: 'rgb(35, 126, 223)' };
            case DiscountStatusEnum.Approved:
                return { color: 'rgb(35, 223, 35)' };
            case DiscountStatusEnum.Running:
                return { color: 'rgb(35, 223, 142)' };
            case DiscountStatusEnum.Expired:
                return { color: 'red' };
            case DiscountStatusEnum.Rejected:
                return { color: 'rgb(223, 35, 35)' };
            default:
                return { color: 'gray' };
        }
    }

    StatusName(status: DiscountStatusEnum) {
        switch (status) {
            case DiscountStatusEnum.PendingApproval:
                return 'Pending';
            case DiscountStatusEnum.Approved:
                return 'Approved';
            case DiscountStatusEnum.Running:
                return 'Running';
            case DiscountStatusEnum.Rejected:
                return 'Rejected';
            case DiscountStatusEnum.Expired:
                return 'Expired';
            default:
                return 'Approved';
        }
    }
    showDialog(dis: DiscountModel) {
        // First, close all dialogs
        this.discounts.forEach(discount => discount.VisibleDialog = false);
        dis.VisibleDialog = true;
    }
    edit(e: { discount?: DiscountModel, state: string }) {
        if (e.discount.Active == false) {
            this.messageService.add({
                severity: "warn",
                summary: "Warning",
                detail: "Please activate the company before Editing ! ",
            });

        } else {
            this.formData = e.discount;
            this.discountService.getDiscountById(e.discount.Id.toString()).subscribe((data) => {
                this.router.navigate(['discount-edit'], {
                    state: {
                        //  data: this.formData,
                        data: data,
                        command: 'edit'
                    },
                })
            });
        }
    }
    rejectOrAccept(discount: DiscountModel) {

        this.discountService.getDiscountById(discount.Id.toString()).subscribe((data) => {
            data['DiscountStatuses'] = discount.DiscountStatuses;
            this.router.navigate(['discount-preview'], {
                state: {
                    data: data,
                },
            });
        });

    }
    changeDiscountLastActionLog(id: string) {
        this.discountService.EditActiveDiscount(id).subscribe((data) => {
            if (this.dactivated == false) {
                if (data['HasError'] == false && data['ResultContent']['Active'] == false) {
                    var discount = this.discounts.find((x) => x.Id == id);
                    // Update the data array (remove the deleted item)
                    this.discounts = this.discounts.filter(currentItem => currentItem.Id !== discount.Id);
                }
            } else if (this.dactivated == true) {
                if (data['HasError'] == false && data['ResultContent']['Active'] == true) {
                    var discount = this.discounts.find((x) => x.Id == id);
                    // Update the data array (remove the deleted item)
                    this.discounts = this.discounts.filter(currentItem => currentItem.Id !== discount.Id);
                }
            }
        });

    }
    // Delete(id: string) {
    //     this.discountService.DeleteDiscount(id).subscribe((data) => {
    //         console.log(data);
    //     });
    // }
    ReceivedFilteredData(event) {
        this.CompanyNameOrDiscountTitle = event.companyOrDiscountName;
        this.StartDate = event.startDate;
        this.EndDate = event.endDate;
        this.totalRecords = event.TotalRecords; // Set total records for pagination
        this.fetchDiscountsData(1, this.pageSize);
        this.discounts = event.ListResultContent;

        //this.paginator = false;

    }

    Delete(id) {
        this.confirmationService.confirm({
            key: "confirm1",
            target: event.target,
            message: "Are You Sure Delete This Discount",
            icon: "pi pi-exclamation-triangle",
            accept: () => {
                this.discountService.DeleteDiscount(id)
                    .subscribe((data: any) => {
                        if (data['HasError'] == false) {
                            this.messageService.add({
                                severity: "success",
                                summary: "Success Message",
                                detail: "Delete Discount Successfully",
                            });
                            var discount = this.discounts.find((x) => x.Id == id);
                            const index = this.discounts.indexOf(discount, 0);
                            // Update the data array (remove the deleted item)
                            this.discounts = this.discounts.filter(currentItem => currentItem.Id !== discount.Id);
                            /* if (index > -1) {
                                 this.discounts.splice(index, 1);
 
                             }*/
                        }
                        else {
                            this.messageService.add({
                                severity: "error",
                                summary: "Error",
                                detail: "Can't Delete This Discount ",
                            });
                        }
                    });
            },
            reject: () => {
                this.messageService.add({
                    severity: "error",
                    summary: "Rejected",
                    detail: "You have rejected",
                });
            },
        });
        // alert("Are You Sure Delete This Discount");
    }

    canEditDiscount(discount: DiscountModel): boolean {
        // Multi-condition logic to determine if the discount can be edited
        if (discount.Active == false) {
            return false;
        }
        if (discount.Status == 4) // expired discount
            return false;

        if (this.user.UserType == 1) // companyAdmin
        {
            if (discount.Status == 3) //Running
                return false;
        }


        // If none of the above conditions are met, return true
        return true;
    }

    canActiveDiscount(discount: DiscountModel): boolean {
        // Multi-condition logic to determine if the discount can be actived

        if (this.user.UserType == 1) // companyAdmin
            return false;

        // hide the Activate switch for PendingApproval, Rejected and Expired discounts 
        if (discount.Status == 0 || discount.Status == 2 || discount.Status == 4)
            return false;


        // If none of the above conditions are met, return true
        return true;
    }
}
