import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ConfirmationService, MessageService } from 'primeng/api';
import { UserModel } from 'src/app/Model/UserModel';
import { CouponModel } from 'src/app/Model/CouponModel';
import { CouponActionLogEnum } from 'src/app/enum/coupon-action-log-enum';
import { CouponService } from 'src/app/services/coupon.service';
import { environment } from 'src/environments/environment';
import { CouponStatusEnum } from 'src/app/enum/coupon-status-enum';
import { AuthService } from 'src/app/services/auth.service';

@Component({
    selector: 'app-coupon-list',
    templateUrl: './coupon-list.component.html',
    styleUrls: ['./coupon-list.component.scss'],
    providers: [MessageService, ConfirmationService],
})
export class CouponlistComponent implements OnInit {
    Coupons: CouponModel[];
    loading: boolean = true;
    visible: boolean = false;
    reviewed: boolean = false;
    dactivated: boolean = false;
    user: UserModel;
    title: string;
    formData: CouponModel;
    imageSrc: string = environment.imageSrc;


    //filters
    CompanyNameOrCouponTitle: string = '';
    StartDate: any; EndDate: any;

    totalRecords: number = 0;
    pageSize: number = 10;
    pageIndex: number = 0;
    paginator: boolean = false;

    //  track the first load
    private isInitialLoad: boolean = false;


    constructor(private CouponService: CouponService, private router: Router, private route: ActivatedRoute, private messageService: MessageService,
        private confirmationService: ConfirmationService, private authService: AuthService) {
        route.data.subscribe((item) => {
            this.reviewed = item['reviewed'];
            this.dactivated = item['deactivated'];
            if (this.dactivated == true) {
                this.title = "Deactivated Coupon";
            } else
                if (this.reviewed == true) {
                    this.title = "Reviewed Coupon";
                }
                else if (this.reviewed == false) {
                    this.title = "Inbox Coupon";
                }
            //

        });

    }
    ngOnInit(): void {

        this.loadData({ first: 0, rows: this.pageSize });
        this.isInitialLoad = true;
        // this.route.paramMap.subscribe(params => {
        //     this.urlSegment = params.get('id');
        //     console.log(this.urlSegment);
        // });
    }

    loadData(event: any) {
        // Avoid double loading during the first render
        if (this.isInitialLoad) {
            this.isInitialLoad = false; // Set the flag to false after the first load
            return;
        }
        const pageNumber = event.first / event.rows + 1; // Calculate the page number
        const pageSize = event.rows; // Rows per page
        this.paginator = true;
        this.user = this.authService.getUserData();
        this.fetchCouponsData(pageNumber, pageSize)
    }

    fetchCouponsData(pageNumber, pageSize) {
        if (this.user.UserType == 0) {
            if (this.dactivated == true)
                this.CouponService.getCouponForAdmin(pageNumber, pageSize, this.reviewed, this.dactivated, this.CompanyNameOrCouponTitle, this.StartDate, this.EndDate).subscribe((data: any) => {
                    this.Coupons = data.ListResultContent;
                    this.totalRecords = data.TotalRecords; // Set total records for pagination
                    this.loading = false;

                });
            else
                this.CouponService.getCouponForAdmin(pageNumber, pageSize, this.reviewed, false, this.CompanyNameOrCouponTitle, this.StartDate, this.EndDate).subscribe((data: any) => {
                    this.Coupons = data.ListResultContent;
                    this.totalRecords = data.TotalRecords; // Set total records for pagination
                    this.loading = false;

                });
        }
        else {

            this.CouponService.getCouponForCompany( pageNumber, pageSize, this.CompanyNameOrCouponTitle, this.StartDate, this.EndDate).subscribe((data) => {
                this.Coupons = data.ListResultContent;
                this.totalRecords = data.TotalRecords; // Set total records for pagination
                this.loading = false;
            });
        }
    }
    colorOfLastActionLog(lastActionLog: CouponActionLogEnum) {
        switch (lastActionLog) {
            case CouponActionLogEnum.Approved:
                return { color: '#23df36' };
            case CouponActionLogEnum.Created:
                return { color: '#8a23df' };
            case CouponActionLogEnum.Edited:
                return { color: '#f1f11d' };
            case CouponActionLogEnum.Rejected:
                return { color: '#f10a0a' };
            case CouponActionLogEnum.Deactivated:
                return { color: '#f10a0a' };
            case CouponActionLogEnum.Reactivated:
                return { color: '#0c46e8' };
            default:
                return { color: 'gray' };
        }
    }
    iconForLastActionLog(lastActionLog: CouponActionLogEnum) {
        switch (lastActionLog) {
            case CouponActionLogEnum.Approved:
                return 'pi pi-check-circle';
            case CouponActionLogEnum.Created:
                return 'pi pi-clock';
            case CouponActionLogEnum.Edited:
                return 'pi pi-pencil';
            case CouponActionLogEnum.Rejected:
                return 'pi pi-times';
            case CouponActionLogEnum.Deactivated:
                return 'pi pi-minus-circle';
            case CouponActionLogEnum.Reactivated:
                return 'pi pi-check-circle';
            default:
                return 'pi pi-clock';
        }
    }
    ActionLogEnumName(lastActionLog: CouponActionLogEnum) {
        switch (lastActionLog) {
            case CouponActionLogEnum.Approved:
                return 'Approved';
            case CouponActionLogEnum.Created:
                return 'Created';
            case CouponActionLogEnum.Edited:
                return 'Edited';
            case CouponActionLogEnum.Rejected:
                return 'Rejected';
            case CouponActionLogEnum.Deactivated:
                return 'Deactivated';
            case CouponActionLogEnum.Reactivated:
                return 'Reactivated';
            default:
                return 'Created';
        }
    }

    colorOfStatus(status: CouponStatusEnum) {
        switch (status) {
            case CouponStatusEnum.PendingApproval:
                return { color: 'rgb(35, 126, 223)' };
            case CouponStatusEnum.Approved:
                return { color: 'rgb(35, 223, 35)' };
            case CouponStatusEnum.Running:
                return { color: 'rgb(35, 223, 142)' };
            case CouponStatusEnum.Expired:
                return { color: 'red' };
            case CouponStatusEnum.Rejected:
                return { color: 'rgb(223, 35, 35)' };
            default:
                return { color: 'gray' };
        }
    }

    StatusName(status: CouponStatusEnum) {
        switch (status) {
            case CouponStatusEnum.PendingApproval:
                return 'Pending';
            case CouponStatusEnum.Approved:
                return 'Approved';
            case CouponStatusEnum.Running:
                return 'Running';
            case CouponStatusEnum.Rejected:
                return 'Rejected';
            case CouponStatusEnum.Expired:
                return 'Expired';
            default:
                return 'Approved';
        }
    }
    showDialog(dis: CouponModel) {
        // First, close all dialogs
        this.Coupons.forEach(Coupon => Coupon.VisibleDialog = false);
        dis.VisibleDialog = true;
    }
    edit(e: { Coupon?: CouponModel, state: string }) {
        if (e.Coupon.Active == false) {
            this.messageService.add({
                severity: "warn",
                summary: "Warning",
                detail: "Please activate the company before Editing ! ",
            });

        } else {
            this.formData = e.Coupon;
            this.CouponService.getCouponById(e.Coupon.Id.toString()).subscribe((data) => {
                this.router.navigate(['coupon-edit'], {
                    state: {
                        //  data: this.formData,
                        data: data,
                        command: 'edit'
                    },
                })
            });
        }
    }
    rejectOrAccept(Coupon: CouponModel) {

        this.CouponService.getCouponById(Coupon.Id.toString()).subscribe((data) => {
            data['CouponLog'] = Coupon.CouponLog;
            this.router.navigate(['coupon-preview'], {
                state: {
                    data: data,
                },
            });
        });

    }
    changeCouponLastActionLog(id: string) {
        // console.log('changed Coupon LastActionLog: ', id);
        this.CouponService.EditActiveCoupon(id).subscribe((data) => {
            if (this.dactivated == false) {
                if (data['HasError'] == false && data['ResultContent']['Active'] == false) {
                    var Coupon = this.Coupons.find((x) => x.Id == id);
                    // Update the data array (remove the deleted item)
                    this.Coupons = this.Coupons.filter(currentItem => currentItem.Id !== Coupon.Id);
                }
            } else if (this.dactivated == true) {
                if (data['HasError'] == false && data['ResultContent']['Active'] == true) {
                    var Coupon = this.Coupons.find((x) => x.Id == id);
                    // Update the data array (remove the deleted item)
                    this.Coupons = this.Coupons.filter(currentItem => currentItem.Id !== Coupon.Id);
                }
            }
        });

    }
    ReceivedFilteredData(event) {
        console.log('event', event);
        this.CompanyNameOrCouponTitle = event.companyOrCouponName;
        this.StartDate = event.startDate;
        this.EndDate = event.endDate;
        this.totalRecords = event.TotalRecords; // Set total records for pagination
        this.fetchCouponsData(1, this.pageSize);
        this.Coupons = event.ListResultContent;

        //this.paginator = false;

    }

    Delete(id) {
        console.log('here delete');
        this.confirmationService.confirm({
            key: "confirm1",
            target: event.target,
            message: "Are You Sure Delete This Coupon",
            icon: "pi pi-exclamation-triangle",
            accept: () => {
                this.CouponService.DeleteCoupon(id)
                    .subscribe((data: any) => {
                        console.log('data', data);
                        if (data['HasError'] == false) {
                            this.messageService.add({
                                severity: "success",
                                summary: "Success Message",
                                detail: "Delete Coupon Successfully",
                            });
                            var Coupon = this.Coupons.find((x) => x.Id == id);
                            const index = this.Coupons.indexOf(Coupon, 0);
                            // Update the data array (remove the deleted item)
                            this.Coupons = this.Coupons.filter(currentItem => currentItem.Id !== Coupon.Id);
                            /* if (index > -1) {
                                 this.Coupons.splice(index, 1);
 
                             }*/
                        }
                        else {
                            this.messageService.add({
                                severity: "error",
                                summary: "Error",
                                detail: "Can't Delete This Coupon ",
                            });
                        }
                    });
            },
            reject: () => {
                this.messageService.add({
                    severity: "error",
                    summary: "Rejected",
                    detail: "You have rejected",
                });
            },
        });
        // alert("Are You Sure Delete This Coupon");
    }

    canEditCoupon(Coupon: CouponModel): boolean {
        // Multi-condition logic to determine if the Coupon can be edited
        if (Coupon.Active == false) {
            return false;
        }
        if (Coupon.Status == 4) // expired Coupon
            return false;

        if (this.user.UserType == 1) // companyAdmin
        {
            if (Coupon.Status == 3) //Running
                return false;
        }


        // If none of the above conditions are met, return true
        return true;
    }

    canActiveCoupon(Coupon: CouponModel): boolean {
        // Multi-condition logic to determine if the Coupon can be actived

        if (this.user.UserType == 1) // companyAdmin
            return false;

        // hide the Activate switch for PendingApproval, Rejected and Expired Coupons 
        if (Coupon.Status == 0 || Coupon.Status == 2 || Coupon.Status == 4)
            return false;


        // If none of the above conditions are met, return true
        return true;
    }
}
