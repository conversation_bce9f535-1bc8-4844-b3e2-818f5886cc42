import { CityModel } from "./CityModel";
import { FilterModel } from "./FiltersModel";
import { IndustryModel } from "./IndustryModel";
import { TiersModel } from "./TiersModel";
import { SubscriptionModel } from "./SubscriptionModel";
import { CountryTierUpgradingMethodsEnum } from "../enum/country-tier-upgrading-methods-enum";

export class Country {
    Id: string = "";

    ArName: string = "";
    EnName: string = "";
    Currency?: string = "";
    CountryLanguages? = [];
    DefaultLanguage?;
    ShowEstimatedSaving: boolean = true;

    Cities?: CityModel[] = [];

    CountryTiers?: TiersModel[] = [];
    CountryFilter?: FilterModel[] = [];
    CountryIndustry?: IndustryModel[] = [];
    CountryIndustries?: IndustryModel[] = [];
    CountrySubscriptions?: SubscriptionModel[] = [];

    CountryTierUpgradingMethods?: CountryTierUpgradingMethodsEnum[] = [];

    PriceOfSub?: number = 0;
    TotalCompanies?: number = 0;
    TotalEndUsers?: number = 0;
    TierDiscountValue?: number = 0;
    IsTierDiscountEnabled: boolean = true;
    IsSubsciptionsDiscountEnabled: boolean = true;
    SubsciptionsDiscountValue?: number = 0;

    //Not Mapped
    HasError: boolean = false;

    Tiers: TiersModel[] = [];
}
