import { AdminType } from "../enum/admin-type";
import { UserType } from "../enum/user-type";
import { WebsiteAdminPageEnum } from "../enum/website-admin-pages-enum";
import { CompanyBranchModel } from "./CompanyBranchModel";
import { CompanyModel } from "./CompanyModel";
import { Country } from "./Country";

export class UserModel {
  Id?: string = "";
  UserName?: string;
  Name?: string;
  Password?: string = '';
  UserType?: UserType;
  AdminType?: AdminType;
  Email?: string;
  ProfilePhotoUrl?: string = "";
  ProfilePhoto?: File;
  MaxServiceDiscount?: number ;
  AdministeredCountries?: Country[] = [];
  AdministeredPages?: WebsiteAdminPageEnum[] = [];
  Active?: boolean = true;

  // Company Admin 
  Company?: CompanyModel;
  AdministeredBranches?: CompanyBranchModel[]=[];

  // login Objects
  User?: any;
  Token?: string;
  RefreshToken?: string;

  CompanyId?: string;
}