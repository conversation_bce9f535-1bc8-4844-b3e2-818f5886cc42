import { Location } from '@angular/common';
import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CouponModel } from 'src/app/Model/CouponModel';

@Component({
  selector: 'app-coupon-new',
  templateUrl: './coupon-new.component.html',
  styleUrls: ['./coupon-new.component.scss']
})
export class CouponNewComponent {
  title: string;
  routeState: any;
  _CouponData: CouponModel = new CouponModel();
  activeIndex = 0;
  editing: boolean = false;
  fileToUpload: any[] = [];

  constructor(private route: ActivatedRoute, private router: Router,private location: Location) {
    if (this.router.getCurrentNavigation()?.extras.state) {
      this.routeState = this.router.getCurrentNavigation()?.extras.state;
      if (this.routeState) {
        this._CouponData = this.routeState.data
          ? this.routeState.data
          : new CouponModel();
        if (this._CouponData.Id == "") {
          this.editing = false;
        }
        else {
          this.editing = true;
        }
      //  console.log("this.editing", this.editing);

      }
    }
    this.route.data.subscribe((item) => {
      this.title = item['title'];
    });
  }
  goBack() {
    this.location.back();
  }
}
