{"name": "poseidon", "version": "15.0.0", "license": "COMMERCIAL", "angular-cli": {}, "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint"}, "private": true, "dependencies": {"@angular/animations": "^15.1.0", "@angular/cdk": "^15.1.0", "@angular/common": "^15.1.0", "@angular/compiler": "^15.1.0", "@angular/core": "^15.1.0", "@angular/forms": "^15.1.0", "@angular/platform-browser": "^15.1.0", "@angular/platform-browser-dynamic": "^15.1.0", "@angular/router": "^15.1.0", "@fortawesome/fontawesome-free": "^6.4.0", "@fullcalendar/angular": "^6.0.3", "@fullcalendar/core": "^6.0.3", "@fullcalendar/daygrid": "^6.0.3", "@fullcalendar/interaction": "^6.0.3", "@fullcalendar/timegrid": "^6.0.3", "@ngx-translate/core": "^14.0.0", "@ngx-translate/http-loader": "^7.0.0", "bootstrap": "^5.3.0-alpha1", "chart.js": "^3.3.2", "jquery": "^3.6.3", "ngx-toastr": "^16.0.2", "primeflex": "^3.2.1", "primeicons": "6.0.1", "primeng": "15.1.1", "prismjs": "1.9.0", "quill": "^1.3.7", "rxjs": "~7.5.0", "tslib": "^2.3.0", "web-animations-js": "^2.3.2", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~15.1.0", "@angular-eslint/builder": "^15.1.0", "@angular-eslint/eslint-plugin": "^15.1.0", "@angular-eslint/eslint-plugin-template": "^15.1.0", "@angular-eslint/schematics": "^15.1.0", "@angular-eslint/template-parser": "^15.1.0", "@angular/cli": "^15.1.0", "@angular/compiler-cli": "^15.1.0", "@types/jasmine": "~3.10.0", "@types/node": "^12.11.1", "@typescript-eslint/parser": "5.37.0", "eslint": "^8.23.1", "jasmine-core": "~4.0.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "typescript": "~4.8.4"}}