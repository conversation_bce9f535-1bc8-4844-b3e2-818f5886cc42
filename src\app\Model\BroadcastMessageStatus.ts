import { BroadcastMessageActionLogEnum } from "../enum/broadcast-message-action-log-enum";
import { BroadcastMessageModel } from "./BroadcastMessageModel";

export class BroadcastMessageStatus {
      BroadcastMessagesId?: string;
      BroadcastMessages?: BroadcastMessageModel;
      Status?: BroadcastMessageActionLogEnum= BroadcastMessageActionLogEnum.Created;
      Note?: string = "";
      CreationDate?: Date;
}
