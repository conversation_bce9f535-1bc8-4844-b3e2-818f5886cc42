import { UserModel } from './../Model/UserModel';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from '../services/auth.service';

@Component({
  selector: 'app-login',
  templateUrl: './app.login.component.html',
  styles: [`
    .password-container {
      position: relative;
      display: flex;
      align-items: center;
      margin-top: 15px;

    }

    .password-container input {
      flex: 1;
      padding-right: 40px;
    }

    .toggle-password {
      position: absolute;
      right: 10px;
      cursor: pointer;
      font-size: 18px;
      color: gray;
    }

    .toggle-password:hover {
      color: black;
    }
  `]
})
export class AppLoginComponent implements OnInit {
  profileForm: FormGroup;
  invalidForm: boolean;
  formSubmitted: boolean;
  userModel: UserModel;
  loading = false;
  constructor(private formBuilder: FormBuilder,
    private AuthService: AuthService,
    private toastr: ToastrService,
    private route: Router,
  ) { }
  ngOnInit(): void {
    this.userModel = new UserModel();
    this.profileForm = this.formBuilder.group({
      email: new FormControl('', [Validators.required, Validators.minLength(5)]),
      //password: new FormControl('', [Validators.required, Validators.pattern('(?=\\D*\\d)(?=[^a-z]*[a-z])(?=[^A-Z]*[A-Z]).{8,30}')]),
      password: new FormControl('')
    });
  }
  onSubmit() {
    this.loading = true;
    this.formSubmitted = true;
    if (this.profileForm.valid) {
      this.userModel.Password = this.profileForm.value["password"];
      //this.userModel.Email = this.profileForm.value["userName"];
      this.userModel.Email = this.profileForm.value["email"];
      this.AuthService.Login(this.userModel).subscribe((res: boolean) => {
        this.loading = false;
        if (res) {
          this.route.navigate(['']);
        }
      })
    }
    else {
      this.profileForm.setErrors({
        invalidForm: true
      });
    }
  }
  isPasswordVisible: boolean = false;

  togglePasswordVisibility() {
    this.isPasswordVisible = !this.isPasswordVisible;
  }

}
