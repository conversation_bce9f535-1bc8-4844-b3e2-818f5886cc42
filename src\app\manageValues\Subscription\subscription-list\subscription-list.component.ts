import { Component, Input, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ConfirmationDialogComponent } from 'src/app/components/confirmation-dialog/confirmation-dialog.component';
import { SubscriptionModel } from 'src/app/Model/SubscriptionModel';
import { SubscriptionService } from 'src/app/services/subscription.service';

@Component({
  selector: 'app-subscription-list',
  templateUrl: './subscription-list.component.html',
  styleUrls: ['./subscription-list.component.scss'],
  providers: [MessageService, ConfirmationService],
})
export class SubscriptionListComponent {
  @Input() _Subscription: SubscriptionModel;
  Subscriptions?: SubscriptionModel[];
  Subscription: SubscriptionModel = new SubscriptionModel();
  loading: boolean = true;
  formData: SubscriptionModel;
  title: string;

  warningMessage: string = '';
  @ViewChild('confirmationDialog') confirmationDialog: ConfirmationDialogComponent;
  confirmationAction: string = '';
  OverrideWarning: boolean = false;


  totalRecords: number = 0;
  pageSize: number = 10;
  pageIndex: number = 0;
  paginator: boolean = false;

  //filters
  Name: string = '';

  //  track the first load
  private isInitialLoad: boolean = false;


  constructor(
    private SubscriptionService: SubscriptionService,
    private router: Router,
    private messageService: MessageService,) { }

  ngOnInit(): void {
    this.loadData({ first: 0, rows: this.pageSize });
    this.isInitialLoad = true;
    this.title = "Subscriptions Management";
  }

  loadData(event: any) {
    // Avoid double loading during the first render
    if (this.isInitialLoad) {
      this.isInitialLoad = false; // Set the flag to false after the first load
      return;
    }
    const pageNumber = event.first / event.rows + 1; // Calculate the page number
    const pageSize = event.rows; // Rows per page
    this.paginator = true;

    this.fetchSubscriptionsData(pageNumber, pageSize);

  }

  fetchSubscriptionsData(pageNumber, pageSize) {
    this.SubscriptionService
      .getAllSubscriptionsTable(pageNumber, pageSize, this.Name)
      .subscribe((data) => {
        this.Subscriptions = data.ListResultContent;
        this.totalRecords = data.TotalRecords; // Set total records for pagination
        this.loading = false;
      });
  }

  edit(e: { Subscription?: SubscriptionModel; state: string }) {

    this.formData = e.Subscription;

    this.SubscriptionService.getSubscriptionById(e.Subscription.Id.toString()).subscribe((data) => {
      this.router.navigate(["subscription-edit"], {
        state: {
          data: data,
          command: "edit",
        },
      });
    });


  }
  ReceivedFilteredData(event) {
    this.Name = event.Name;
    this.fetchSubscriptionsData(1, this.pageSize);

    this.Subscriptions = event.ListResultContent;
    this.totalRecords = event.TotalRecords; // Set total records for pagination
    //this.paginator = false;

  }

  delete(Subscription) {
    console.log('deleteSubscription', Subscription)
    this.Subscription = Subscription;
    if (Subscription.TotalCountries || Subscription.TotalUsers) {
      this.messageService.add({ severity: 'error', summary: 'Error', detail: `Cannot delete this Subscription. It has ${Subscription.TotalCompanies} associated companies and It has ${Subscription.TotalCountries} associated countries.` });
      return;
    }
    this.confirmationDialog.message = 'Do you want to delete this Subscription ' + this.Subscription.EnName;
    this.confirmationDialog.item = this.Subscription;
    this.confirmationDialog.openDialog();
    this.confirmationAction = 'delete';
  }

  // Method to handle the confirmation result
  handleConfirmationAction(result: boolean): void {
    if (this.confirmationAction == 'delete') {
      if (result) {
        this.SubscriptionService.DeleteSubscription(this.Subscription.Id).subscribe((data) => {
          if (data['HasError'] == false) {
            this.messageService.add({
              severity: "success",
              summary: "Success Message",
              detail: "Delete Subscription Successfully",
            });
            // Update the data array (remove the deleted item)
            this.Subscriptions = this.Subscriptions.filter(currentItem => currentItem.Id !== this.Subscription.Id);
            this.Subscription = new SubscriptionModel();
            this.confirmationAction = '';

          } else {
            this.confirmationDialog.item = this.Subscription;
            this.confirmationDialog.openDialog();
            this.confirmationDialog.message = data['EnErrorMessage'];
          }
        });
      } else {
        // console.log('Cancelled!');
        this.confirmationAction = '';
        this.Subscription = new SubscriptionModel();
      }

    }

  }
}
