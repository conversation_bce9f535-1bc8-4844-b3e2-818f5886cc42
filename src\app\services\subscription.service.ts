import { Injectable } from '@angular/core';
import { Observable, catchError, map, of } from 'rxjs';
import { SubscriptionModel } from '../Model/SubscriptionModel';
import { environment } from 'src/environments/environment';
import { HttpClient, HttpParams } from '@angular/common/http';
import { NationalityModel } from '../Model/NationalityModel';
import { Dto } from '../Model/Dto';

@Injectable({
  providedIn: 'root'
})
export class SubscriptionService {

  constructor(private httpClient: HttpClient) { }

  getAllSubscriptions(): Observable<SubscriptionModel[]>
  {
    return this.httpClient.get<SubscriptionModel[]>(`${environment.apiUrl}Subscription/GetAll`)
    .pipe(
      map((res: any) => {
        if (!res.HasError) {
          return res.ListResultContent;
        }
      })
    );
  }
   getAllSubscriptionsTable(page: number = null, pageSize: number = null ,Name:string=null) {
      var http;
      var url = `${environment.apiUrl}Subscription/getAllAdminTable`;
      // var params = {};
      // if (page != null) {
      let params = new HttpParams();;
      if (page) {
        params = params.set('PageNumber', page);
      }
      if (pageSize) {
        params = params.set('PageSize', pageSize);
      }
      if (Name) {
        params = params.set('Name', Name);
      }
      http = this.httpClient.get(url, { params });
      return http.pipe(
        map((res: Dto<SubscriptionModel>) => {
          return res;
  
        }),
        catchError((error) => {
          return of(false);
        })
      );
    }
     getSubscriptionById(Id: string = "") {
        return this.httpClient.get<Dto<SubscriptionModel>>(`${environment.apiUrl}Subscription/GetSubscriptionById?Id=${Id}`)
          .pipe(
            map((res: any) => {
              return res['ResultContent'];
            })
          );
      }
      public AddSubscription(data): Observable<Dto<SubscriptionModel>> {
        var http;
        var url = `${environment.apiUrl}Subscription/AddSubscription`;
        http = this.httpClient.post(url, data);
        return http.pipe(
          map((res: Dto<SubscriptionModel>) => {
            return res;
    
          }),
          catchError(error => {
            return of(false);
          }));
      }
      public EditSubscription(data): Observable<Dto<SubscriptionModel>> {
        var http;
        var url = `${environment.apiUrl}Subscription/EditSubscription`;
        http = this.httpClient.put(url, data);
        return http.pipe(
          map((res: Dto<SubscriptionModel>) => {
            return res;
          }),
          catchError(error => {
            return of(false);
          }));
      }
      public DeleteSubscription(SubscriptionId: String): Observable<any> {
        var http;
        var url = `${environment.apiUrl}Subscription/DeleteSubscription?Id=${SubscriptionId}`;
        http = this.httpClient.delete(url);
        return http.pipe(
          map((res: any) => {
            return res;
          }),
          catchError((error) => {
            return of(false);
          })
        );
      }
}
