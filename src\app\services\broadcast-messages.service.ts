import { BroadcastMessageModel } from '../Model/BroadcastMessageModel';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Dto } from '../Model/Dto';
import { environment } from 'src/environments/environment';
import { Observable, catchError, map, of } from 'rxjs';
import { BroadcastMessageStatusViewModel } from '../Model/BroadcastMessageStatusViewModel';
import { DatePipe } from '@angular/common';
import { AuthService } from './auth.service';

@Injectable({
    providedIn: 'root'
})
export class BroadcastMessagesService {

    constructor(private httpClient: HttpClient, private datePipe: DatePipe, private authService: AuthService) {
    }

    getBroadcastMessagesForCompany( page: number = null, pageSize: number = null, BroadcastMessageTitle: string = "", startDate: Date | null = null, endDate: Date | null = null, SendAsSoonAsPossible: boolean = false) {

        let params = new HttpParams();
        if (BroadcastMessageTitle) {
            params = params.set('BroadcastMessageTitle', BroadcastMessageTitle)
        }
        if (page) {
            params = params.set('PageNumber', page);
        }
        if (pageSize) {
            params = params.set('PageSize', pageSize);
        }
        if (SendAsSoonAsPossible) {
            params = params.set('SendAsSoonAsPossible', SendAsSoonAsPossible);
        }
        if (startDate)
            // params = params.set('DateRange.StartDate', startDate.toISOString())
            params = params.set('DateRange.StartDate', this.datePipe.transform(startDate, 'yyyy-MM-dd'))
        if (endDate)
            params = params.set('DateRange.EndDate', this.datePipe.transform(endDate, 'yyyy-MM-dd'))
        // params = params.set('DateRange.EndDate', endDate.toISOString())

        return this.httpClient.get(`${environment.apiUrl}BroadcastMessage/GetBroadcastMessageForCompany`
            , { params }
        ).pipe(
            map((res: any) => {
                return res;
            })
        );
    }

    getBroadcastMessageForAdmin(page: number = null, pageSize: number = null, ReviewedByAdmin = true, Deactivated = false, CompanyNameOrBroadcastMessageTitle: string = "", startDate: Date | null = null, endDate: Date | null = null, SendAsSoonAsPossible: boolean = false) {
        let params = new HttpParams();
        if (CompanyNameOrBroadcastMessageTitle) {
            params = params.set('CompanyNameOrBroadcastMessageTitle', CompanyNameOrBroadcastMessageTitle)
        }
        if (startDate)
            // params = params.set('DateRange.StartDate', startDate.toISOString())
            params = params.set('DateRange.StartDate', this.datePipe.transform(startDate, 'yyyy-MM-dd'))
        if (endDate)
            params = params.set('DateRange.EndDate', this.datePipe.transform(endDate, 'yyyy-MM-dd'))
        // params = params.set('DateRange.EndDate', endDate.toISOString())
        if (SendAsSoonAsPossible) {
            params = params.set('SendAsSoonAsPossible', SendAsSoonAsPossible);
        }
        if (page) {
            params = params.set('PageNumber', page);
        }
        if (pageSize) {
            params = params.set('PageSize', pageSize);
        }
        return this.httpClient.get(`${environment.apiUrl}BroadcastMessage/GetBroadcastMessageForAdmin?ReviewedByAdmin=${ReviewedByAdmin}&Deactivated=${Deactivated}`, { params })
            .pipe(
                map((res: any) => {
                    return res;
                })
            );
    }

    getBroadcastMessageById(Id: string = "") {
        return this.httpClient.get<Dto<BroadcastMessageModel>>(`${environment.apiUrl}BroadcastMessage/GetBroadcastMessageById?Id=${Id}`)
            .pipe(
                map((res: any) => {

                    return res['ResultContent'];
                })
            );
    }
    public AddBroadcastMessage(data): Observable<Dto<BroadcastMessageModel>> {
        var http;
        var url = `${environment.apiUrl}BroadcastMessage/AddBroadcastMessage`;
        http = this.httpClient.post(url, data);
        return http.pipe(
            map((res: Dto<BroadcastMessageModel>) => {
                    return res;
            }),
            catchError(error => {

                return of(false);
            }));
    }
    public EditBroadcastMessage(data): Observable<Dto<BroadcastMessageModel>> {
        var http;
        var url = `${environment.apiUrl}BroadcastMessage/EditBroadcastMessage`;
        http = this.httpClient.put(url, data);
        return http.pipe(
            map((res: Dto<BroadcastMessageModel>) => {
                    return res;
            }),
            catchError(error => {

                return of(false);
            }));
    }
    public RejectBroadcastMessage(BroadcastMessageModel: BroadcastMessageStatusViewModel) {
        return this.httpClient.patch(`${environment.apiUrl}BroadcastMessage/ReviewBroadcastMessage`, BroadcastMessageModel)
            .pipe(
                map((res: any) => {

                    return res;
                })
            );
    }
    public AcceptBroadcastMessage(BroadcastMessagesModel: BroadcastMessageStatusViewModel) {
        return this.httpClient.patch(`${environment.apiUrl}BroadcastMessage/ReviewBroadcastMessage`, BroadcastMessagesModel)
            .pipe(
                map((res: any) => {
                    return res;
                })
            );
    }
    public EditActiveBroadcastMessage(BroadcastMessageId: String): Observable<any> {
        var http;

        var url = `${environment.apiUrl}BroadcastMessage/EditBroadcastMessageActive?id=${BroadcastMessageId}`;
        http = this.httpClient.patch(url, {});
        return http.pipe(
            map((res: any) => {
                return res;
            }),
            catchError((error) => {

                return of(false);
            })
        );
    }
    public DeleteBroadcastMessage(BroadcastMessageId: string): Observable<any> {
        var http;

        var url = `${environment.apiUrl}BroadcastMessage/DeleteBroadcastMessage?Id=${BroadcastMessageId}`;
        http = this.httpClient.delete(url);
        return http.pipe(
            map((res: any) => {
                return res;
            }),
            catchError((error) => {
                return of(false);
            })
        );
    }
    //     public GetAllFiltered(CompanyOrBroadcastMessagesName: string, startDate: Date, endDate: Date): Observable<any> {
    //         let params = new HttpParams();

    //         if (CompanyOrBroadcastMessagesName) {
    //             params = params.set('CompanyNameOrBroadcastMessagesTitle', CompanyOrBroadcastMessagesName)
    //         }
    //         if (startDate)
    //             params = params.set('DateRange.StartDate', startDate.toISOString())
    //         if (endDate)
    //             params = params.set('DateRange.EndDate', endDate.toISOString())


    //         return this.httpClient.get(`${environment.apiUrl}BroadcastMessages/GetBroadcastMessagesForAdmin`, { params });
    //     }
}
