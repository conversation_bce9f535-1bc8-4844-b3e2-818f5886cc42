import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { environment } from 'src/environments/environment';
import { CompanyModel } from '../Model/CompanyModel';
import { Dto } from '../Model/Dto';
import { ConditionModel } from '../Model/ConditionModel';

@Injectable({
  providedIn: 'root'
})
export class ConditionService {
  constructor(private httpClient: HttpClient) { }
  GetAll() {
    return this.httpClient.get<Dto<ConditionModel>>(`${environment.apiUrl}` + 'Condition/GetAll')
      .pipe(
        map((res: any) => {
          if (!res.HasError) {
            return res.ListResultContent;
          }
        })
      );
  }
}
