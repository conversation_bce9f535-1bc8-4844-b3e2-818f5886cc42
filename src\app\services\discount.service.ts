import { DiscountModel } from '../Model/DiscountModel';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Dto } from '../Model/Dto';
import { environment } from 'src/environments/environment';
import { Observable, catchError, map, of } from 'rxjs';
import { DiscountStatusViewModel } from '../Model/DiscountStatusViewModel';
import { DatePipe } from '@angular/common';
import { AuthService } from './auth.service';

@Injectable({
	providedIn: 'root'
})
export class DiscountService {


	constructor(private httpClient: HttpClient, private datePipe: DatePipe, private authService: AuthService) {
	}

	getDiscountForCompany(page: number = null, pageSize: number = null, CompanyOrDiscountName: string = "", startDate: Date | null = null, endDate: Date | null = null) {

		let params = new HttpParams();
		if (CompanyOrDiscountName) {
			params = params.set('CompanyNameOrDiscountTitle', CompanyOrDiscountName)
		}
		if (page) {
			params = params.set('PageNumber', page);
		}
		if (pageSize) {
			params = params.set('PageSize', pageSize);
		}
		if (startDate)
			// params = params.set('DateRange.StartDate', startDate.toISOString())
			params = params.set('DateRange.StartDate', this.datePipe.transform(startDate, 'yyyy-MM-dd'))
		if (endDate)
			params = params.set('DateRange.EndDate', this.datePipe.transform(endDate, 'yyyy-MM-dd'))
		// params = params.set('DateRange.EndDate', endDate.toISOString())

		return this.httpClient.get(`${environment.apiUrl}Discount/GetDiscountForCompany?`, { params }).pipe(
			map((res: any) => {

				return res;
			})
		);
	}

	getDiscountForAdmin(page: number = null, pageSize: number = null, ReviewedByAdmin = true, Deactivated = false, CompanyOrDiscountName: string = "", startDate: Date | null = null, endDate: Date | null = null) {
		let params = new HttpParams();
		if (CompanyOrDiscountName) {
			params = params.set('CompanyNameOrDiscountTitle', CompanyOrDiscountName)
		}
		if (startDate)
			// params = params.set('DateRange.StartDate', startDate.toISOString())
			params = params.set('DateRange.StartDate', this.datePipe.transform(startDate, 'yyyy-MM-dd'))
		if (endDate)
			params = params.set('DateRange.EndDate', this.datePipe.transform(endDate, 'yyyy-MM-dd'))
		// params = params.set('DateRange.EndDate', endDate.toISOString())
		if (page) {
			params = params.set('PageNumber', page);
		}
		if (pageSize) {
			params = params.set('PageSize', pageSize);
		}
		return this.httpClient.get(`${environment.apiUrl}Discount/GetDiscountForAdmin?ReviewedByAdmin=${ReviewedByAdmin}&Deactivated=${Deactivated}`, { params })
			.pipe(
				map((res: any) => {

					if (res.HasError) {
						return of(false);
					} else {
						return res;
					}
				})
			);
	}

	getDiscountById(Id: string = "") {
		return this.httpClient.get<Dto<DiscountModel>>(`${environment.apiUrl}Discount/GetDiscountById?Id=${Id}`)
			.pipe(
				map((res: any) => {
					return res['ResultContent'];
				})
			);
	}
	public AddDiscount(data): Observable<Dto<DiscountModel>> {
		var http;
		var url = `${environment.apiUrl}Discount/AddDiscount`;
		http = this.httpClient.post(url, data);
		return http.pipe(
			map((res: Dto<DiscountModel>) => {
				return res;
			}),
			catchError(error => {

				return of(false);
			}));
	}
	public EditDiscount(data): Observable<Dto<DiscountModel>> {
		var http;
		var url = `${environment.apiUrl}Discount/EditDiscount`;
		http = this.httpClient.put(url, data);
		return http.pipe(
			map((res: Dto<DiscountModel>) => {
				return res;
			}),
			catchError(error => {

				return of(false);
			}));
	}
	public RejectDiscount(discountModel: DiscountStatusViewModel) {
		return this.httpClient.patch(`${environment.apiUrl}Discount/ReviewDiscount`, discountModel)
			.pipe(
				map((res: any) => {
					return res;
				})
			);
	}
	public AcceptDiscount(discountModel: DiscountStatusViewModel) {
		return this.httpClient.patch(`${environment.apiUrl}Discount/ReviewDiscount`, discountModel)
			.pipe(
				map((res: any) => {
					return res;
				})
			);
	}
	public EditActiveDiscount(discountId: String): Observable<any> {
		var http;

		var url = `${environment.apiUrl}Discount/EditDiscountActive?id=${discountId}`;
		http = this.httpClient.patch(url, {});
		return http.pipe(
			map((res: any) => {
				return res;
			}),
			catchError((error) => {

				return of(false);
			})
		);
	}
	public DeleteDiscount(discountId: string): Observable<any> {
		var http;
		var url = `${environment.apiUrl}Discount/DeleteDiscount?Id=${discountId}`;

		http = this.httpClient.delete(url);
		return http.pipe(
			map((res: any) => {
				return res;
			}),
			catchError((error) => {

				return of(false);
			})
		);
	}
	//     public GetAllFiltered(CompanyOrDiscountName: string, startDate: Date, endDate: Date): Observable<any> {
	//         let params = new HttpParams();

	//         if (CompanyOrDiscountName) {
	//             params = params.set('CompanyNameOrDiscountTitle', CompanyOrDiscountName)
	//         }
	//         if (startDate)
	//             params = params.set('DateRange.StartDate', startDate.toISOString())
	//         if (endDate)
	//             params = params.set('DateRange.EndDate', endDate.toISOString())


	//         return this.httpClient.get(`${environment.apiUrl}Discount/GetDiscountForAdmin`, { params });
	//     }
}
