import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MessageService } from 'primeng/api';
import { Country } from 'src/app/Model/Country';
import { LanguageModel } from 'src/app/Model/LanguageModel';
import { LanguageService } from 'src/app/services/language.service';
import { Currencies } from 'src/app/utilities/currencies';

@Component({
    selector: 'country-main-info',
    templateUrl: './country-main-info.component.html',
    styleUrls: ['./country-main-info.component.scss'],
  
})
export class CountryMainInfoComponent {
    @Input() _CountryData: Country;
    @Input() isEditing: boolean;
    @Input() activeIndex: number;
    @Output() activeIndexChange: EventEmitter<number> = new EventEmitter();
    pressedNext = false;
    selectedCurrency: any;
    CurrenciesList: Currencies = new Currencies();
    Currencies = [];
    mainInfoForm = new FormGroup({
        //CountryImage: new FormControl([], [Validators.required]),
        EnName: new FormControl('', [Validators.required, Validators.minLength(3)]),
        ArName: new FormControl('', [Validators.required, Validators.minLength(3)]),
        Currency: new FormControl('', [Validators.required]),
        CountryLanguages: new FormControl('', [Validators.required]),
        DefaultLanguage: new FormControl('', [Validators.required]),
        ShowEstimatedSaving: new FormControl('',),
    });

    Languages: LanguageModel[] = [];


    constructor(private messageService: MessageService, private LanguageService: LanguageService) {
    }
    ngOnInit(): void {
        this.LanguageService.GetAllLanguages().subscribe((data) => {
            this.Languages = data
        });
        if (this._CountryData.Currency) {
            this.selectedCurrency = this.CurrenciesList.currencies.find(ct => ct.code === this._CountryData.Currency);
        }
    }

    next() {
        // Mark all controls as touched to trigger validation

        this.mainInfoForm.markAllAsTouched();
        if (!this.mainInfoForm.valid) {
            this._CountryData.HasError = true;
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Check your input' });
            return;
        } else {
            this.pressedNext = true;
            this._CountryData.HasError = false;
            // The activeIndex was set to a direct value because during editing, the navigate tabs, a problem in navigation when pressing the buttons (next, back).
            this.activeIndex = 1;
            this.activeIndexChange.emit(this.activeIndex);
        }
    }


    CheckValid(input: FormControl) {
        if (input.invalid && (this.pressedNext || input.touched)) {
            return 'red';
        }
        return '#515C66';
    }
    selectCurrency() {
        this._CountryData.Currency = this.selectedCurrency.code;
    }
    ShowEstimatedSaving() { }
}
