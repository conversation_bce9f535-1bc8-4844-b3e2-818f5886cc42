import { Location } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { CouponModel } from 'src/app/Model/CouponModel';
import { CouponStatusViewModel } from 'src/app/Model/CouponStatusViewModel';
import { UserModel } from 'src/app/Model/UserModel';
import { CouponActionLogEnum } from 'src/app/enum/coupon-action-log-enum';
import { AuthService } from 'src/app/services/auth.service';
import { CouponService } from 'src/app/services/coupon.service';
import { environment } from 'src/environments/environment';

interface SelectboxObject {
    name: string;
    value: number;

}
@Component({
    selector: 'app-coupon-preview',
    templateUrl: './coupon-preview.component.html',
    styleUrls: ['./coupon-preview.component.scss'],
  
})
export class CouponPreviewComponent implements OnInit {

    Coupon: CouponModel;
    selected: any[];
    routeState: any;
    checkIfApproved: boolean;
    checkIfRejected: boolean;
    rejectDialogVisibility: boolean = false;
    rejectReasonControl: FormControl;
    user: UserModel;

    constructor(private router: Router, private CouponService: CouponService, private messageService: MessageService, private location: Location, private authService: AuthService) {
        this.user =  this.authService.getUserData();
        if (this.router.getCurrentNavigation()?.extras.state) {
            this.routeState = this.router.getCurrentNavigation()?.extras.state;

            if (this.routeState) {
                this.Coupon = this.routeState.data ? this.routeState.data : new CouponModel();
            }
        } else {
            this.Coupon = new CouponModel();
        }
        console.log('coupon', this.Coupon)
        this.rejectReasonControl = new FormControl('', [Validators.required]);
    }

    ngOnInit(): void {

        this.checkCouponApprovedRejected();

        console.log(this.checkIfRejected, this.checkIfApproved);

    }

    checkCouponApprovedRejected() {
        if (this.user.UserType == 1) {
            this.checkIfRejected = true;
            this.checkIfApproved = true;
        } else
            if (this.Coupon.Status == 2) { // Rejected : show approved button only 
                this.checkIfApproved = false;
                this.checkIfRejected = true;
            }
        if (this.Coupon.Status == 1) {  //approved  : show rejected button only 

            this.checkIfRejected = false;
            this.checkIfApproved = true;
        }
        // if (this.Coupon.Status == 4) { // Expired : this.Coupon.Status == 4
        //     this.checkIfRejected = true;
        //     this.checkIfApproved = true;
        // }
        if (this.Coupon.Active == false) { // diactivated
            this.checkIfRejected = true;
            this.checkIfApproved = true;
        }
        if (this.Coupon.Status == 3 || this.Coupon.Status == 4) { // running or Expired : hide buttons  
            this.checkIfRejected = true;
            this.checkIfApproved = true;
        }
    }

    Accept() {
        let _Coupon = new CouponStatusViewModel();
        _Coupon.Id = this.Coupon.Id;
        _Coupon.Note = this.Coupon.NoteCoupon;
        _Coupon.Status = CouponActionLogEnum.Approved;

        this.CouponService.AcceptCoupon(_Coupon).subscribe((data) => {
            if (!data.HasError) {
                this.router.navigate(['coupons-reviewed']);
            }
            else {
                this.messageService.add({ severity: 'error', summary: 'Error', detail: data.EnErrorMessage });
            }
        })
    }
    Reject() {
        if (this.rejectReasonControl.valid) {
            let _Coupon = new CouponStatusViewModel();
            _Coupon.Id = this.Coupon.Id;
            _Coupon.Note = this.Coupon.NoteCoupon;
            _Coupon.Status = CouponActionLogEnum.Rejected;

            this.CouponService.RejectCoupon(_Coupon).subscribe((data) => {
                if (!data.HasError) {
                    this.router.navigate(['coupons-reviewed']);
                }
                else {
                    this.messageService.add({ severity: 'error', summary: 'Error', detail: data.EnErrorMessage });
                }
            });
        } else {
            this.rejectReasonControl.markAsTouched();
        }
    }
    showRejectReasonDialog() {
        this.rejectDialogVisibility = true;
    }

    getImageUrl(image) {
        return image ? environment.imageSrc + image : '';
        // return logo ? this.sanitizer.bypassSecurityTrustUrl(environment.imageSrc + logo) : '';
    }
    goBack() {
        this.location.back();
    }
}
