<div class="container">
      <p-toast></p-toast>
      <div class="grid p-fluid" [formGroup]="mainInfoForm">

            <div class="col-12">
                  <div class="col-6 md:col-4">
                  <span class="flex flex-column gap-2 mb-3">
                        <label for="EnName" [style.color]="
                              CheckValid(mainInfoForm.controls.EnName)">English Name</label>
                        <input pInputText id="EnName" [(ngModel)]="_CountryData.EnName" class="w-50"
                              formControlName="EnName" />
                  </span>

                  <span class="flex flex-column gap-2 mb-3">
                        <label for="ArName" [style.color]="
                              CheckValid(mainInfoForm.controls.ArName)">Arabic Name</label>
                        <input pInputText id="ArName" [(ngModel)]="_CountryData.ArName" class="w-50"
                              formControlName="ArName" />
                  </span>

                  <div class="flex flex-column gap-2 mb-3">
                        <label [style.color]="CheckValid(mainInfoForm.controls.Currency)"
                              for="Currency">Currency</label>
                        <p-dropdown [options]="CurrenciesList.currencies" [(ngModel)]="selectedCurrency"
                              placeholder="select Currency" (onChange)="selectCurrency()" optionLabel="code"
                              formControlName="Currency" filter 
                              filterPlaceholder="Search..." ></p-dropdown>
                  </div>

                  <div class="flex flex-column gap-2 mb-3">
                        <label [style.color]="CheckValid(mainInfoForm.controls.CountryLanguages)"
                              for="CountryLanguages">End User App Languages</label>
                        <p-multiSelect selectedItemsLabel="No. of Selected Languages: {0}" [options]="Languages"
                              [(ngModel)]="_CountryData.CountryLanguages" defaultLabel="Select Languages"
                              optionLabel="Name" id="countryLanguages" formControlName="CountryLanguages"
                              class="multiselect-custom">
                        </p-multiSelect>
                  </div>
                  <div class="flex flex-column gap-2 mb-3">
                        <label [style.color]="CheckValid(mainInfoForm.controls.DefaultLanguage)"
                              for="DefaultLanguage">Default Language</label>
                        <p-dropdown [options]="Languages" [(ngModel)]="_CountryData.DefaultLanguage"
                              placeholder="select Language"  optionLabel="Name"
                              formControlName="DefaultLanguage"></p-dropdown>
                  </div>

                  <div class="flex flex-column gap-2 mb-3">
                        <div class="col">
                              <p-checkbox binary="true" inputId="ShowEstimatedSaving"
                                    formControlName="ShowEstimatedSaving"
                                    [(ngModel)]="_CountryData.ShowEstimatedSaving" (click)="ShowEstimatedSaving()"
                                    id="showEstimatedSaving" label="Show Estimated Saving">
                              </p-checkbox>
                        </div>

                  </div>
                  </div>
                  <div class="flex justify-content-end gap-2 mb-3">
                        <p-button label="Next" (click)="next()"></p-button>
                  </div>
            </div>
           
      </div>